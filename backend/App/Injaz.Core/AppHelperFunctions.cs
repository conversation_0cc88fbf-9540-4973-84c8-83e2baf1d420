using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Security.Cryptography;
using LinqKit;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.Setting;
using Injaz.Core.Dtos.Team;
using Injaz.Core.Dtos.User;
using Injaz.Core.Exceptions;
using Injaz.Core.Models;
using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Novell.Directory.Ldap;

namespace Injaz.Core;

public static class AppHelperFunctions
{
    public static int[] ComputeAvailableYears(AppDataContext appDataContext)
    {
        var result = (from m in appDataContext.KpiResults
            let minYear = appDataContext.KpiResults.Min(x => x.Year)
            let maxYear = appDataContext.KpiResults.Max(x => x.Year)
            select new
            {
                MinYear = minYear,
                MaxYear = maxYear
            }).FirstOrDefault();

        if (result == null)
        {
            result = new
            {
                MinYear = DateTime.UtcNow.Year,
                MaxYear = DateTime.UtcNow.Year,
            };
        }

        return Enumerable.Range(result.MinYear, result.MaxYear - result.MinYear + 2).ToArray();
    }

    public static string GetAchievedColor(double? achieved, KpiSetting.AchievementColorCoding[] codings)
    {
        foreach (var coding in codings)
        {
            if ((coding.Min == null || coding.Min <= achieved) &&
                (coding.Max == null || achieved < coding.Max))
            {
                return coding.Color;
            }
        }

        return "#aaaaaa";
    }

    public static PlanEntityPotentialAssigneeDto GetPlanEntityPotentialAssignees(
        Guid planEntityId,
        IQueryable<IPlanEntity> planEntities,
        IQueryable<Department> departments,
        IQueryable<User> users,
        string lang
    )
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var teamExpression = TeamSimpleDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);

        Expression<Func<IPlanEntity, PlanEntityPotentialAssigneeDto>> expression = item =>
            new PlanEntityPotentialAssigneeDto
            {
                // Children will be able to be assigned to
                // either the same department as its parent
                // *OR* any department that is a child
                // of the department assigned to the parent.
                Departments = item.AssignedDepartmentId == null
                    ? Array.Empty<DepartmentSimpleDto>()
                    : departments
                        .Where(x => x.HierarchyCode.StartsWith(item.AssignedDepartment.HierarchyCode))
                        .Select(x => departmentExpression.Invoke(x))
                        .ToList(),

                // Children will be able to be assigned
                // to the same team as the parent.
                Teams = item.AssignedTeam == null
                    ? Array.Empty<TeamSimpleDto>()
                    : new[] { teamExpression.Invoke(item.AssignedTeam) },

                // Tasks will be able to be assigned
                // to either the same user assigned to
                // the parent, users under a team assigned
                // to the parent, or users under a department
                // assigned to the parent (including its
                // child departments).
                Users = item.AssignedDepartmentId != null
                    ? users
                        .Where(x => x.Status == User.StatusActive)
                        .Where(x => x.DepartmentLinks.Any(y =>
                            y.Department.HierarchyCode.StartsWith(item.AssignedDepartment.HierarchyCode)))
                        .Select(x => userExpression.Invoke(x))
                        .ToList()
                    : item.AssignedTeamId != null
                        ? users.Where(x => x.TeamLinks.Any(y => y.TeamId == item.AssignedTeamId))
                            .Select(x => userExpression.Invoke(x))
                            .ToList()
                        : item.AssignedUserId != null
                            ? new[] { userExpression.Invoke(item.AssignedUser) }
                            : Array.Empty<UserSimpleDto>()
            };
        return planEntities
            .AsExpandable()
            .Where(x => x.Id == planEntityId)
            .Select(expression)
            .SingleOrDefault();
    }

    /// <summary>
    /// Gets the details needed to update a plan entity, i.e.,
    /// the entity itself, the children of the entity, and
    /// the ids of the children that are still consistent with
    /// the update on the entity in terms of period overlap
    /// and the assignees.
    /// </summary>
    /// <param name="entityId"></param>
    /// <param name="assignedDepartmentId">The new department to be assigned to the entity during the update.</param>
    /// <param name="assignedTeamId">The new team to be assigned to the entity during the update.</param>
    /// <param name="assignedUserId">The new user to be assigned to the entity during the update.</param>
    /// <param name="entities"></param>
    /// <param name="departments"></param>
    /// <param name="isInDraftPredicate">Determines if the entity is initially approved (defaults to false).</param>
    /// <param name="childrenExpression">An expression that tells us how to extract the children entities.</param>
    /// <returns></returns>
    public static PlanEntityForUpdateDto<T> GetPlanEntityForUpdate<T>(
        Guid entityId,
        Guid? assignedDepartmentId,
        Guid? assignedTeamId,
        Guid? assignedUserId,
        IQueryable<T> entities,
        IQueryable<Department> departments,
        Expression<Func<T, bool>> isInDraftPredicate = null,
        Expression<Func<T, IEnumerable<IPlanEntity>>> childrenExpression = null
    ) where T : IPlanEntity
    {
        var hierarchyCode = departments
            .FirstOrDefault(x => x.Id == assignedDepartmentId)?
            .HierarchyCode;

        var areChildrenConsistentPredicate = HelperExpression.AreChildrenConsistent<IPlanEntity>(
            hierarchyCode,
            assignedTeamId,
            assignedUserId
        );

        isInDraftPredicate ??= x => false;

        entities = entities.AsExpandable();

        return childrenExpression != null
                ? entities
                    .Where(x => x.Id.Equals(entityId))
                    .Select(x => new PlanEntityForUpdateDto<T>
                    {
                        Entity = x,
                        IsEditable = isInDraftPredicate.Invoke(x),
                        Children = childrenExpression
                            .Invoke(x)
                            .AsEnumerable(),
                        ConsistentChildrenIds = childrenExpression
                            .Invoke(x)
                            .Where(y => areChildrenConsistentPredicate.Invoke(y))
                            .Select(y => y.Id)
                            .AsEnumerable()
                    })
                    .FirstOrDefault()
                : entities
                    .Where(x => x.Id.Equals(entityId))
                    .Select(x => new PlanEntityForUpdateDto<T>
                    {
                        Entity = x,
                        IsEditable = isInDraftPredicate.Invoke(x),
                    })
                    .FirstOrDefault()
            ;
    }

    public static void UpdatePlanEntityPeriod(
        // The task list and each task's
        // subtasks should be loaded into
        // the plan.
        IPlanEntity entity,
        DateTime from,
        DateTime to,
        IReadOnlyList<Func<IPeriodPlanEntity, IEnumerable<IPeriodPlanEntity>>> childrenFetchers = null
    )
    {
        // Nullify the period of any child (and its children
        // regardless of fit in period) that does not
        // have a period that lies between the original parent's.
        if (childrenFetchers != null)
        {
            NullifyPeriods(entity, childrenFetchers, x => !(x.From >= from && x.To <= to));
        }

        // Update the period.
        entity.From = from;
        entity.To = to;
    }

    public static void UpdatePlanEntityAssignment(
        // The task list and each task's
        // subtasks should be loaded into
        // the plan.
        IPlanEntity entity,
        Guid[] consistentChildrenIds,
        Guid? departmentId,
        Guid? teamId,
        Guid? userId,
        IReadOnlyList<Func<IPlanEntity, IEnumerable<IPlanEntity>>> childrenFetchers = null
    )
    {
        // If there is no change then return.
        if (entity.AssignedDepartmentId == departmentId &&
            entity.AssignedTeamId == teamId &&
            entity.AssignedUserId == userId)
        {
            return;
        }

        // Nullify assigned for all children
        // that are not consistent with the
        // assigned for the entity. e.g.,
        // a user in the task that does not
        // exist in the entity's department/team
        if (childrenFetchers != null)
        {
            NullifyAssignment(entity, childrenFetchers, x => !consistentChildrenIds.Contains(x.Id));
        }

        // Update assigned.
        entity.AssignedDepartmentId = departmentId;
        entity.AssignedTeamId = teamId;
        entity.AssignedUserId = userId;
    }

    /// <summary>
    /// Nullify period of children
    /// plan entities
    /// </summary>
    private static void NullifyPeriods(
        IPeriodPlanEntity entity,
        IReadOnlyList<Func<IPeriodPlanEntity, IEnumerable<IPeriodPlanEntity>>> childrenFetchers,
        Func<IPeriodPlanEntity, bool> condition,
        int level = 0
    )
    {
        // Ensure that the current level has a fetcher.
        if (level >= childrenFetchers.Count) return;

        // Get the children.
        var children = childrenFetchers[level].Invoke(entity) ?? Array.Empty<IPeriodPlanEntity>();

        // Apply the condition only on the first level,
        // any subsequent level should have their fields
        // nullified either way (if the parent is nullified).
        if (level == 0)
        {
            children = children.Where(condition.Invoke);
        }

        foreach (var e in children)
        {
            e.From = null;
            e.To = null;

            NullifyPeriods(e, childrenFetchers, condition, level + 1);
        }
    }

    /// <summary>
    /// Nullify assigned of children
    /// plan entities
    /// </summary>
    private static void NullifyAssignment(
        IPlanEntity entity,
        IReadOnlyList<Func<IPlanEntity, IEnumerable<IPlanEntity>>> childrenFetchers,
        Func<IPlanEntity, bool> condition,
        int level = 0
    )
    {
        // Ensure that the current level has a fetcher.
        if (level >= childrenFetchers.Count) return;

        // Get the children.
        var children = childrenFetchers[level].Invoke(entity);

        // Apply the condition only on the first level,
        // any subsequent level should have their fields
        // nullified either way (if the parent is nullified).
        if (level == 0)
        {
            children = children.Where(condition.Invoke);
        }

        foreach (var e in children)
        {
            e.AssignedDepartmentId = null;
            e.AssignedTeamId = null;
            e.AssignedUserId = null;
            NullifyAssignment(e, childrenFetchers, condition, level + 1);
        }
    }

    public static bool ValidatePermissionIdList(string[] permissionIdList, out string error)
    {
        // Ensure that the permission is valid.
        if (permissionIdList.Any(x =>
            {
                // Ensure that the permission is one of
                // the valid permissions by the system.
                var permission = PermissionList.Value.FirstOrDefault(y => y.Id.Equals(x));
                if (permission == null) return true;

                // Ensure that a selected permission does not
                // have an overrider that is also selected
                // for the user.
                return permission.Overriders.Intersect(permissionIdList).Any();
            }))
        {
            error = "invalid_permission";
            return false;
        }

        error = null;
        return true;
    }

    public static (string ApiKey, string HashedApiKey) GenerateLinkedApplicationApiKey()
    {
        var tokenData = RandomNumberGenerator.GetBytes(512);
        var apiKey = Convert.ToBase64String(tokenData);
        var hashedApiKey = Convert.ToBase64String(SHA256.HashData(tokenData));

        return (apiKey, hashedApiKey);
    }

    public static bool ValidateLinkedApplicationApiKey(string apiKey, string hashedApiKey)
    {
        var providedApiKeyHash = Convert.ToBase64String(SHA256.HashData(Convert.FromBase64String(apiKey)));

        return providedApiKeyHash == hashedApiKey;
    }

    public static (bool Success, string Message) LdapLogin(
        string username,
        string password,
        LdapSetting settings)
    {
        username = username.Contains('@') ? username.Split("@")[0] : username;

        using var connection = new LdapConnection();
        connection.SecureSocketLayer = settings.IsSsl;

        try
        {
            connection.Connect(settings.Host, settings.Port);
            connection.Bind(username + "@" + settings.Domain, password);
            var searchBase = string.Join(",", settings.Domain.Split(".").Select(x => $"DC={x}"));
            var searchFilter = $"(&(objectClass=user)(sAMAccountName={username}))";
            var searchResponse = connection.Search(searchBase, LdapConnection.ScopeSub, searchFilter, null, false);
            searchResponse.HasMore();
            var entry = searchResponse.Next();
            var fullName = entry.GetAttribute("cn").StringValue;
            return (true, fullName);
        }

        catch (LdapException e)
        {
            return (false, e.LdapErrorMessage);
        }

        catch (Exception e)
        {
            return (false, e.Message);
        }

        finally
        {
            connection.Disconnect();
        }
    }

    public static bool IsMailSettingAvailable(SettingDto appSettings)
    {
        return appSettings.MailSetting is
               {
                   From: not null,
                   Host: not null,
                   Password: not null,
                   Username: not null
               }
               && appSettings.MailSetting.Port != 0;
    }

    public static void AssertUserInvolvedWithDepartment(
        Guid userId,
        Guid departmentId,
        IQueryable<Department> departments,
        bool hasFullAccessPermission)
    {
        if (hasFullAccessPermission) return;
        var hasAccess = departments
            .AsExpandable()
            .Where(x => x.Id == departmentId)
            .Select(HelperExpression
                .IsInvolvedWithDepartment(userId, departments, true))
            .FirstOrDefault();

        if (hasAccess) return;
        throw new GenericException()
        {
            Messages = new[] { "Access denied to this department" }
        };
    }
}
