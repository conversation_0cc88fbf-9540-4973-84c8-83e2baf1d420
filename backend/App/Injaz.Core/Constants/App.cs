using Injaz.Core.Misc;

namespace Injaz.Core.Constants;

public static class App
{
    // Kpi Progress States
    public static readonly KpiAchievementProgress Late = new("late", double.MinValue, 0.60);
    public static readonly KpiAchievementProgress Close = new("close", 0.60, 0.90);
    public static readonly KpiAchievementProgress Achieved = new("achieved", 0.90, 1.20);
    public static readonly KpiAchievementProgress Exceeded = new("exceeded", 1.20, double.MaxValue);
    public static readonly KpiAchievementProgress NotYet = new("not_yet", null, null);

    public static readonly KpiAchievementProgress[] KpiProgressStates =
    {
        Late,
        Close,
        Achieved,
        Exceeded,
        NotYet
    };

    // App settings
    public const string AppLogoFileName = "logo";
    public const string AppLoginLogoFileName = "login_logo";
    public const string AppLoginBackground = "login_background";

    // Plan settings
    // public const int PlanMaxDepartmentApprovals = 3;
}
