namespace Injaz.Core.Constants;

// public static class Permissions
// {
//     public static class Kpi
//     {
//         public const string Read = "kpi/read";
//         public const string Write = "kpi/write";
//         public const string Delete = "kpi/delete";
//         public const string Hide = "kpi/hide";
//         public const string Archive = "kpi/archive";
//         public const string Activate = "kpi/activate";
//     }
//
//     public static class KpiResult
//     {
//         public const string Read = "kpi_result/read";
//         public const string Write = "kpi_result/write";
//         public const string Delete = "kpi_result/delete";
//         public const string Approve = "kpi_result/approve";
//     }
//
//     public static class Operation
//     {
//         public const string Read = "operation/read";
//         public const string Write = "operation/write";
//         public const string Delete = "operation/delete";
//     }
//
//     public static class Department
//     {
//         public const string Read = "department/read";
//         public const string Write = "department/write";
//         public const string Delete = "department/delete";
//     }
//
//     public static class User
//     {
//         public const string Read = "user/read";
//         public const string Write = "user/write";
//         public const string Delete = "user/delete";
//     }
//
//     public static class Report
//     {
//         public const string Department = "report/department";
//         public const string Kpi = "report/kpi";
//     }
//
//     public static class System
//     {
//         public const string ManageCapabilityType = "system/capability_type/manage";
//         public const string ManageKpiTag = "system/kpi_tag/manage";
//         public const string ManageLibraryTag = "system/library_tag/manage";
//         public const string ManageOperationPolicy = "system/operation_policy/manage";
//         public const string ManageOperationRuleAndRegulation = "system/operation_rule_and_regulation/manage";
//         public const string ManageOperationSpecification = "system/operation_specification/manage";
//         public const string FullAccess = "system/full_access";
//     }
//
//     public static readonly string[] All =
//     {
//         Kpi.Read,
//         Kpi.Write,
//         Kpi.Delete,
//         Kpi.Hide,
//         Kpi.Archive,
//         Kpi.Activate,
//
//         KpiResult.Read,
//         KpiResult.Write,
//         KpiResult.Delete,
//         KpiResult.Approve,
//
//         Operation.Read,
//         Operation.Write,
//         Operation.Delete,
//
//         Department.Read,
//         Department.Write,
//         Department.Delete,
//
//         User.Read,
//         User.Write,
//         User.Delete,
//
//         Report.Department,
//         Report.Kpi,
//
//         System.ManageCapabilityType,
//         System.ManageKpiTag,
//         System.ManageLibraryTag,
//         System.ManageOperationPolicy,
//         System.ManageOperationRuleAndRegulation,
//         System.ManageOperationSpecification,
//         System.FullAccess
//     };
// }
