using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Abstraction;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Setting;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.DtoMappingStrategies;

public class DashboardTopItemsGetDtoMappingStrategy : IDtoMappingStrategy<AppSetting, DashboardTopItemsGetDto>
{
    public DashboardTopItemsGetDtoMappingStrategy()
    {
    }

    public IEnumerable<DashboardTopItemsGetDto> Execute(IQueryable<AppSetting> source)
    {
        var lang = HelperFunctions.GetLanguageCode();

        var items = source
            .AsExpandable()
            .Select(Mapper())
            .ToList();

        // Deserialize JSON and map to DTO
        var topItemsList = new List<DashboardTopItemsGetDto>();
        items.ForEach(x =>
        {
            if (x.Item != null)
            {
                var topItem = new DashboardTopItemsGetDto
                {
                    Title = SupportedCultures.LanguageArabic == lang ? x.Item.TitleAr : x.Item.TitleEn,
                    Items = x.Item.Items.Select(y => new NewTopItemGetDto
                    {
                        Label = SupportedCultures.LanguageArabic == lang ? y.LabelAr : y.LabelEn,
                        IconClass = y.IconClass,
                        Description = SupportedCultures.LanguageArabic == lang ? y.DescriptionAr : y.DescriptionEn,
                        Tags = SupportedCultures.LanguageArabic == lang ? y.TagsAr : y.TagsEn,
                    }).ToList()
                };
                topItemsList.Add(topItem);
            }
        });

        return topItemsList;
    }

    public static Expression<Func<AppSetting, DashboardTopItemsEditDto>> Mapper()
    {
        return appSetting => new DashboardTopItemsEditDto
        {
            Item = appSetting.DashboardTopItem
        };
    }
}
