using System;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using ActivityModel = Injaz.Core.Models.DomainClasses.App.Activity;


namespace Injaz.Core.Dtos.Activity;

public class ActivityListDto
{
    public static Expression<Func<ActivityModel, ActivityListDto>> Mapper(string lang)
    {
        return item => new ActivityListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
            Year = item.Year,
            InnovationCount = item.InnovationLinks.Count,
            InnovatorCount = item.InnovationLinks.Select(x => x.Innovation.InnovatorId).Distinct().Count()

        };
    }
    public Guid Id { get; set; }
    public string Name { get; set; }
    public int Year { get; set; }
    public int InnovationCount { get; set; }
    public int InnovatorCount { get; set; }
}
