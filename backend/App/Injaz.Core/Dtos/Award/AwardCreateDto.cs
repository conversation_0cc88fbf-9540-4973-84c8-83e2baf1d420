using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Innovator;

namespace Injaz.Core.Dtos.Award;

public class AwardCreateDto
{
    [Display(Name = "name_ar")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }
    [Display(Name = "name_en")] public string NameEn { get; set; }

    [Display(Name = "year")]
    [Required(ErrorMessage = "0_is_required")]
    public int Year { get; set; }
    public InnovatorSimpleDto Innovator { get; set; }
}
