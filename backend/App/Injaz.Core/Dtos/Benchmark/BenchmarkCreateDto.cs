using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.BenchmarkRequestReason;
using Injaz.Core.Dtos.BenchmarkSelectionReason;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.Partner;
using Injaz.Core.Dtos.StrategicGoal;
using BenchmarkModel = Injaz.Core.Models.DomainClasses.App.Benchmark;

namespace Injaz.Core.Dtos.Benchmark;

public class BenchmarkCreateDto
{
    [Display(Name = "visit_date")]
    [Required(ErrorMessage = "0_is_required")]
    public DateTime? VisitDate { get; set; }

    [Display(Name = "language")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       BenchmarkModel.LanguageArabic + "|" +
                       BenchmarkModel.LanguageEnglish + ")$",
        ErrorMessage = "0_is_invalid")]
    public string Language { get; set; }


    [Display(Name = "type")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       BenchmarkModel.TypeOperation + "|" +
                       BenchmarkModel.TypeService + "|" +
                       BenchmarkModel.TypeBestPractice + "|" +
                       BenchmarkModel.TypeKpi + ")$",
        ErrorMessage = "0_is_invalid")]
    public string Type { get; set; }

    [Display(Name = "method")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       BenchmarkModel.MethodVisit + "|" +
                       BenchmarkModel.MethodRemote + "|" +
                       BenchmarkModel.MethodResearch + "|" +
                       BenchmarkModel.MethodStatistics + ")$",
        ErrorMessage = "0_is_invalid")]
    public string Method { get; set; }


    [Display(Name = "management_type")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       BenchmarkModel.ManagementTypeOperation + "|" +
                       BenchmarkModel.ManagementTypeOther + ")$",
        ErrorMessage = "0_is_invalid")]
    public string ManagementType { get; set; }

    [Display(Name = "entity_type")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       BenchmarkModel.EntityTypeCountry + "|" +
                       BenchmarkModel.EntityTypePartner + "|" +
                       BenchmarkModel.EntityTypeOther + ")$",
        ErrorMessage = "0_is_invalid")]
    public string EntityType { get; set; }

    [Display(Name = "entity_name")] public string EntityName { get; set; }

    [Display(Name = "other_request_reasons")]
    public string OtherRequestReasons { get; set; }

    [Display(Name = "other_selection_reasons")]
    public string OtherSelectionReasons { get; set; }

    [Display(Name = "coordinator_employee_number")]
    [Required(ErrorMessage = "0_is_required")]
    public string CoordinatorEmployeeNumber { get; set; }

    [Display(Name = "coordinator_rank")]
    [Required(ErrorMessage = "0_is_required")]
    public string CoordinatorRank { get; set; }

    [Display(Name = "coordinator_full_name")]
    [Required(ErrorMessage = "0_is_required")]
    public string CoordinatorFullName { get; set; }

    [Display(Name = "coordinator_email")]
    [Required(ErrorMessage = "0_is_required")]
    public string CoordinatorEmail { get; set; }

    [Display(Name = "coordinator_phone")]
    [Required(ErrorMessage = "0_is_required")]
    public string CoordinatorPhone { get; set; }

    [Display(Name = "coordinator_office_number")]
    [Required(ErrorMessage = "0_is_required")]
    public string CoordinatorOfficeNumber { get; set; }

    [Display(Name = "agenda")]
    [Required(ErrorMessage = "0_is_required")]
    public string Agenda { get; set; }

    [Display(Name = "department")]
    [Required(ErrorMessage = "0_is_required")]
    public DepartmentSimpleDto Department { get; set; }

    [Display(Name = "partner")] public PartnerSimpleDto Partner { get; set; }

    [Display(Name = "goals")]
    [MinLength(1, ErrorMessage = "0_should_be_at_least_1")]
    public IEnumerable<StrategicGoalSimpleDto> Goals { get; set; }

    [Display(Name = "operations")] public IEnumerable<OperationSimpleDto> Operations { get; set; }

    [Display(Name = "other_managements")]
    public IEnumerable<BenchmarkOtherManagementCreateDto> OtherManagements { get; set; }

    [Display(Name = "request_reason")] public IEnumerable<BenchmarkRequestReasonSimpleDto> RequestReasons { get; set; }

    [Display(Name = "selection_reason")]
    public IEnumerable<BenchmarkSelectionReasonSimpleDto> SelectionReasons { get; set; }

    [Display(Name = "visitors")]
    [MinLength(1, ErrorMessage = "0_should_at_least_has_1_item")]
    public IEnumerable<BenchmarkVisitorCreateDto> Visitors { get; set; }

    [Display(Name = "kpi_results")] public IEnumerable<BenchmarkKpiResultCreateDto> KpiResults { get; set; }
}
