using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.BenchmarkRequestReason;
using Injaz.Core.Dtos.BenchmarkSelectionReason;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.Partner;
using Injaz.Core.Dtos.StrategicGoal;

namespace Injaz.Core.Dtos.Benchmark;

public class BenchmarkEditDto : BenchmarkCreateDto
{
    public static
        Expression<Func<Core.Models.DomainClasses.App.Benchmark, BenchmarkEditDto>> Mapper(
            string lang,
            bool canAchievedBeNegative
        )
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var partnerExpression = PartnerSimpleDto.Mapper(lang);
        var goalExpression = StrategicGoalSimpleDto.Mapper(lang);
        var operationExpression = OperationSimpleDto.Mapper(lang);
        var otherManagementExpression = BenchmarkOtherManagementEditDto.Mapper();
        var requestReasonExpression = BenchmarkRequestReasonSimpleDto.Mapper(lang);
        var selectionReasonExpression = BenchmarkSelectionReasonSimpleDto.Mapper(lang);
        var visitorExpression = BenchmarkVisitorEditDto.Mapper();
        var resultExpression = BenchmarkKpiResultEditDto.Mapper(lang, canAchievedBeNegative);

        return item => new BenchmarkEditDto
        {
            Id = item.Id,
            VisitDate = item.VisitDate,
            Language = item.Language,
            Type = item.Type,
            Method = item.Method,
            ManagementType = item.ManagementType,
            EntityType = item.EntityType,
            EntityName = item.EntityName,
            OtherRequestReasons = item.OtherRequestReasons,
            OtherSelectionReasons = item.OtherSelectionReasons,
            CoordinatorEmployeeNumber = item.CoordinatorEmployeeNumber,
            CoordinatorRank = item.CoordinatorRank,
            CoordinatorFullName = item.CoordinatorFullName,
            CoordinatorEmail = item.CoordinatorEmail,
            CoordinatorPhone = item.CoordinatorPhone,
            CoordinatorOfficeNumber = item.CoordinatorOfficeNumber,
            Agenda = item.Agenda,
            Department = departmentExpression.Invoke(item.Department),
            Partner = item.PartnerId == null ? null : partnerExpression.Invoke(item.Partner),
            Goals = item.GoalLinks.Select(x => goalExpression.Invoke(x.Goal)).ToList(),
            Operations = item.OperationLinks.Select(x => operationExpression.Invoke(x.Operation)).ToList(),
            OtherManagements = item.OtherManagements.Select(x => otherManagementExpression.Invoke(x)).ToList(),
            RequestReasons = item.RequestReasonLinks.Select(x => requestReasonExpression.Invoke(x.Reason)).ToList(),
            SelectionReasons = item.SelectionReasonLinks.Select(x => selectionReasonExpression.Invoke(x.Reason))
                .ToList(),
            Visitors = item.Visitors.Select(x => visitorExpression.Invoke(x)).ToList(),
            KpiResults = item.KpiResultLinks.Select(x => resultExpression.Invoke(x)).ToList(),
        };
    }

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}