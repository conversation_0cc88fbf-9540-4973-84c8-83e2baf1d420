using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.KpiResult;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Benchmark;

public class BenchmarkKpiResultGetDto
{
    public static Expression<Func<BenchmarkKpiResultLink, BenchmarkKpiResultGetDto>> Mapper(
        string lang,
        bool canAchievedBeNegative
    )
    {
        var expression = KpiResultSummaryDto.Mapper(lang, canAchievedBeNegative);
        return item => new BenchmarkKpiResultGetDto
        {
            Result = expression.Invoke(item.Result),
            PartnerResult = item.PartnerResult
        };
    }

    public KpiResultSummaryDto Result { get; set; }
    public double PartnerResult { get; set; }
}