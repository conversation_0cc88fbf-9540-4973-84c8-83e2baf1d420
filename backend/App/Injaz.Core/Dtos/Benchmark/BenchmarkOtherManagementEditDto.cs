using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Benchmark;

public class BenchmarkOtherManagementEditDto : BenchmarkOtherManagementCreateDto
{
    public static Expression<Func<BenchmarkOtherManagement, BenchmarkOtherManagementEditDto>> Mapper()
    {
        return item => new BenchmarkOtherManagementEditDto
        {
            Id = item.Id,
            Type = item.Type,
            Detail = item.Detail
        };
    }

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}