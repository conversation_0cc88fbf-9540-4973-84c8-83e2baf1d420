using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Benchmark;

public class BenchmarkVisitorCreateDto
{
    public static Expression<Func<BenchmarkVisitor, BenchmarkVisitorCreateDto>> Mapper()
    {
        return item => new BenchmarkVisitorCreateDto
        {
            EmployeeNumber = item.EmployeeNumber,
            Rank = item.Rank,
            FullName = item.FullName,
            Department = item.Department,
            EmploymentTitle = item.EmploymentTitle,
            Description = item.Description,
            Phone = item.Phone,
            Email = item.Email,
        };
    }

    [Display(Name = "employee_number")]
    [Required(ErrorMessage = "0_is_required")]
    public string EmployeeNumber { get; set; }

    [Display(Name = "rank")]
    [Required(ErrorMessage = "0_is_required")]
    public string Rank { get; set; }

    [Display(Name = "full_name")]
    [Required(ErrorMessage = "0_is_required")]
    public string FullName { get; set; }

    [Display(Name = "department")]
    [Required(ErrorMessage = "0_is_required")]
    public string Department { get; set; }

    [Display(Name = "employment_title")]
    [Required(ErrorMessage = "0_is_required")]
    public string EmploymentTitle { get; set; }

    [Display(Name = "description")]
    [Required(ErrorMessage = "0_is_required")]
    public string Description { get; set; }

    [Display(Name = "phone")]
    [Required(ErrorMessage = "0_is_required")]
    public string Phone { get; set; }

    [Display(Name = "email")]
    [Required(ErrorMessage = "0_is_required")]
    public string Email { get; set; }
}