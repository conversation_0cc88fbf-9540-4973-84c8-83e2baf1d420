using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Benchmark;

public class BenchmarkVisitorEditDto : BenchmarkVisitorCreateDto
{
    public static Expression<Func<BenchmarkVisitor, BenchmarkVisitorEditDto>> Mapper()
    {
        return item => new BenchmarkVisitorEditDto
        {
            Id = item.Id,
            EmployeeNumber = item.EmployeeNumber,
            Rank = item.Rank,
            FullName = item.FullName,
            Department = item.Department,
            EmploymentTitle = item.EmploymentTitle,
            Description = item.Description,
            Phone = item.Phone,
            Email = item.Email,
        };
    }

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}