using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.BenchmarkRequestReason;

public class BenchmarkRequestReasonGetDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.BenchmarkRequestReason, BenchmarkRequestReasonGetDto>>
        Mapper(string lang)
    {
        return item => new BenchmarkRequestReasonGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
