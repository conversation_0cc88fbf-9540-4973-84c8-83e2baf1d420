using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Capability;

public class CapabilitySimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Capability, CapabilitySimpleDto>>
        Mapper(string lang) => item => new CapabilitySimpleDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
