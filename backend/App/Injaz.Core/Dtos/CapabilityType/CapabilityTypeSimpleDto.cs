using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.CapabilityType;

public class CapabilityTypeSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.CapabilityType, CapabilityTypeSimpleDto>>
        Mapper(string lang) => item => new CapabilityTypeSimpleDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
