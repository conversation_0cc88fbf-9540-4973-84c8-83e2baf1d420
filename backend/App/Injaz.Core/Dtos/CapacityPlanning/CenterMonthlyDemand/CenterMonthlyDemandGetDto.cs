using System;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Dtos.Department;
using LinqKit;

namespace Injaz.Core.Dtos.CapacityPlanning.CenterMonthlyDemand;

public class CenterMonthlyDemandGetDto
{
    public Guid Id { get; set; }
    public DepartmentSimpleDto Department { get; set; }
    public TimeDimensionSimpleDto Time { get; set; }

    // Employee data
    public int EmployeesAvailable { get; set; }

    // Service data
    public int FastServicesCount { get; set; }
    public double FastServiceTime { get; set; }
    public int RegularServicesCount { get; set; }

    public double RegularServiceTime { get; set; }
    public int ComplexServicesCount { get; set; }

    public double ComplexServiceTime { get; set; }

    // Time parameters
    public double DailyWorkHoursPerEmployee { get; set; }
    public double WorkDaysPerMonth { get; set; }
    public double WorkHoursPerDay { get; set; }
    public int ReligiousAndNationalOccasionsDaysPerMonth { get; set; }

    // Actual usage data
    public int ActualTransactionsPerMonth { get; set; }
    public int ActualCustomersPerMonth { get; set; }

    // Facilities data
    public int AvailableSeatsInCenter { get; set; }
    public int AvailableParkingAtCenter { get; set; }

    // related tot the services channels
    public int HappinessCenterVolume { get; set; }
    public int MoiAppVolume { get; set; }
    public int AjpAppVolume { get; set; }
    public int WebsiteVolume { get; set; }

    public static Expression<Func<Models.DomainClasses.App.CapacityPlanningModel.CenterMonthlyDemand, CenterMonthlyDemandGetDto>> Mapper(string lang = "ar")
    {
        var departmentExp = DepartmentSimpleDto.Mapper(lang);
        var timeDimensionExp = TimeDimensionSimpleDto.Mapper();

        return e => new CenterMonthlyDemandGetDto
        {
            Id = e.Id,
            Department = e.Department != null ? departmentExp.Invoke(e.Department) : null,
            Time = e.Time != null ? timeDimensionExp.Invoke(e.Time) : null,

            // Employee data
            EmployeesAvailable = e.EmployeesAvailable,

            // Service data
            FastServicesCount = e.FastServicesCount,
            FastServiceTime = e.FastServiceTime,
            RegularServicesCount = e.RegularServicesCount,
            RegularServiceTime = e.RegularServiceTime,
            ComplexServicesCount = e.ComplexServicesCount,
            ComplexServiceTime = e.ComplexServiceTime,

            // Time parameters
            DailyWorkHoursPerEmployee = e.DailyWorkHoursPerEmployee,
            WorkDaysPerMonth = e.WorkDaysPerMonth,
            WorkHoursPerDay = e.WorkHoursPerDay,
            ReligiousAndNationalOccasionsDaysPerMonth = e.ReligiousAndNationalOccasionsDaysPerMonth,

            // Actual usage data
            ActualTransactionsPerMonth = e.ActualTransactionsPerMonth,
            ActualCustomersPerMonth = e.ActualCustomersPerMonth,

            // Facilities data - Get from CenterParameters if value is 0
            AvailableSeatsInCenter = e.AvailableSeatsInCenter == 0 && e.Department != null
                ? e.Department.CenterParameters.Where(cp => cp.Year == e.Time.Year)
                    .Select(cp => cp.AvailableSeatsCount)
                    .FirstOrDefault()
                : e.AvailableSeatsInCenter,

            AvailableParkingAtCenter = e.AvailableParkingAtCenter == 0 && e.Department != null
                ? e.Department.CenterParameters.Where(cp => cp.Year == e.Time.Year)
                    .Select(cp => cp.AvailableParkingSpaces)
                    .FirstOrDefault()
                : e.AvailableParkingAtCenter,

            // related tot the services channels
            HappinessCenterVolume = e.HappinessCenterVolume,
            MoiAppVolume = e.MoiAppVolume,
            AjpAppVolume = e.AjpAppVolume,
            WebsiteVolume = e.WebsiteVolume
        };
    }
}
