using System;
using System.Linq.Expressions;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;
using LinqKit;

namespace Injaz.Core.Dtos.CapacityPlanning.CenterParameters
{
    public class CenterParametersGetDto
    {
        public Guid Id { get; set; }
        public DepartmentSimpleDto Department { get; set; }
        public int Year { get; set; }

        public string Remarks { get; set; }

        // Station Information
        public int AvailableStationsCount { get; set; } = 0;
        public double StationUtilizationPercentageComputed { get; set; } = 0;
        public double IdealStationHourlyProductivityComputed { get; set; } = 0;
        public int AvailableSeatsCount { get; set; } = 0;
        public int AvailableParkingSpaces { get; set; } = 0;
        public int TotalStationAvailabilityDaysPerYearComputed { get; set; } = 0;
        public double TotalOfficialWorkingHoursForStation { get; set; } = 0;
        public double TotalStationAvailabilityHoursPerDay { get; set; } = 0;
        public int TotalStationShiftsComputed { get; set; } = 0;

        // Employee Parameters
        public double SickLeaveDaysPerEmployeeYearly { get; set; } = 0;
        public double TrainingDaysPerEmployeeYearly { get; set; } = 0;
        public double OfficialWorkDaysPerEmployeeYearly { get; set; } = 0;
        public double PersonalPermissionDaysYearly { get; set; } = 0;
        public double EffectiveWorkDaysYearlyComputed { get; set; } = 0;
        public double WorkHoursPerEmployeeYearlyComputed { get; set; } = 0;
        public double AvailableMinutesPerEmployeeYearlyComputed { get; set; } = 0;
        public double AvailableMinutesPerEmployeeMonthlyComputed { get; set; } = 0;
        public int ReceptionEmployeesCount { get; set; } = 0;
        public double AverageEmployeeActualHourlyProductivity { get; set; } = 0;
        public double EmployeeEfficiencyPercentageComputed { get; set; } = 0;
        public double AnnualEmployeeLeaves { get; set; } = 0;
        public double DailyWorkingMinutesComputed { get; set; } = 0;
        public double TotalActualEmployeeWorkingHoursPerDay { get; set; } = 0;

        // Service Parameters
        public double FastServiceTargetTime { get; set; } = 0;
        public int FastServicesCount { get; set; } = 0;
        public double NormalServiceTargetTime { get; set; } = 0;
        public int NormalServicesCount { get; set; } = 0;
        public double ComplexServiceTargetTime { get; set; } = 0;
        public int ComplexServicesCount { get; set; } = 0;
        public int TotalCenterServices { get; set; } = 0;
        public double AverageTargetTimeAllServices { get; set; } = 0;
        public double TotalStationWorkingDaysPerMonth { get; set; } = 0;
        public double TotalReligiousAndNationalEventDaysPerYearComputed { get; set; } = 0;

        public double TotalPeakHours { get; set; }


        public void Compute()
        {
            EffectiveWorkDaysYearlyComputed = OfficialWorkDaysPerEmployeeYearly
                                              - SickLeaveDaysPerEmployeeYearly
                                              - TrainingDaysPerEmployeeYearly
                                              - PersonalPermissionDaysYearly;

            WorkHoursPerEmployeeYearlyComputed = EffectiveWorkDaysYearlyComputed * TotalActualEmployeeWorkingHoursPerDay;

            AvailableMinutesPerEmployeeYearlyComputed = WorkHoursPerEmployeeYearlyComputed * 60;

            AvailableMinutesPerEmployeeMonthlyComputed = AvailableMinutesPerEmployeeYearlyComputed / 12;

            IdealStationHourlyProductivityComputed =
                AverageEmployeeActualHourlyProductivity * IdealStationHourlyProductivityComputed;

            EmployeeEfficiencyPercentageComputed = IdealStationHourlyProductivityComputed == 0
                ? 0
                : AverageEmployeeActualHourlyProductivity / IdealStationHourlyProductivityComputed;

            DailyWorkingMinutesComputed = TotalActualEmployeeWorkingHoursPerDay * 60;

            TotalStationShiftsComputed = TotalActualEmployeeWorkingHoursPerDay == 0
                ? 0
                : (int)Math.Round((double)TotalStationAvailabilityHoursPerDay / TotalActualEmployeeWorkingHoursPerDay);

            TotalReligiousAndNationalEventDaysPerYearComputed = OfficialWorkDaysPerEmployeeYearly - TotalStationAvailabilityDaysPerYearComputed;
        }

        public static Expression<Func<CenterParameter, CenterParametersGetDto>> Mapper(string languageCode)
        {
            var departmentMapper = DepartmentSimpleDto.Mapper(languageCode);

            return x => new CenterParametersGetDto
            {
                Id = x.Id,
                Department = departmentMapper.Invoke(x.Department),
                Year = x.Year,

                // Station Info
                AvailableStationsCount = x.AvailableStationsCount,
                StationUtilizationPercentageComputed = x.StationUtilizationPercentageComputed,
                IdealStationHourlyProductivityComputed = x.IdealStationHourlyProductivityComputed,
                AvailableSeatsCount = x.AvailableSeatsCount,
                AvailableParkingSpaces = x.AvailableParkingSpaces,
                TotalStationAvailabilityDaysPerYearComputed = x.TotalStationAvailabilityDaysPerYearComputed,
                TotalOfficialWorkingHoursForStation = x.TotalOfficialWorkingHoursForStation,
                TotalStationAvailabilityHoursPerDay = x.TotalStationAvailabilityHoursPerDay,

                // Employee Params
                SickLeaveDaysPerEmployeeYearly = x.SickLeaveDaysPerEmployeeYearly,
                TrainingDaysPerEmployeeYearly = x.TrainingDaysPerEmployeeYearly,
                OfficialWorkDaysPerEmployeeYearly = x.OfficialWorkDaysPerEmployeeYearly,
                PersonalPermissionDaysYearly = x.PersonalPermissionDaysYearly,
                ReceptionEmployeesCount = x.ReceptionEmployeesCount,
                AverageEmployeeActualHourlyProductivity = x.AverageEmployeeActualHourlyProductivity,
                AnnualEmployeeLeaves = x.AnnualEmployeeLeaves,
                TotalActualEmployeeWorkingHoursPerDay = x.TotalActualEmployeeWorkingHoursPerDay,

                // Services
                FastServiceTargetTime = x.FastServiceTargetTime,
                FastServicesCount = x.FastServicesCount,
                NormalServiceTargetTime = x.NormalServiceTargetTime,
                NormalServicesCount = x.NormalServicesCount,
                ComplexServiceTargetTime = x.ComplexServiceTargetTime,
                ComplexServicesCount = x.ComplexServicesCount,
                TotalCenterServices = x.TotalCenterServices,
                AverageTargetTimeAllServices = x.AverageTargetTimeAllServices,
                TotalStationWorkingDaysPerMonth = x.TotalStationWorkingDaysPerMonth,
                TotalReligiousAndNationalEventDaysPerYearComputed = x.TotalReligiousAndNationalEventDaysPerYearComputed,
                TotalPeakHours = x.TotalPeakHours,
            };
        }
    }
}
