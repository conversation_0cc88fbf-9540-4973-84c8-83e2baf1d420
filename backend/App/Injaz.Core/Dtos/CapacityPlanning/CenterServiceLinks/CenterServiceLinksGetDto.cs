using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Service;
using LinqKit;

namespace Injaz.Core.Dtos.CapacityPlanning.CenterServiceLinks;

public class UpdateCenterServiceLinkGetDto
{
    // selected department (represent police station or center)
    public DepartmentSimpleDto Department { get; set; }

    public ServiceWithSubServiceDto[] AllServices { get; set; }

    public Guid[] LinkedServicesIds { get; set; }

}
