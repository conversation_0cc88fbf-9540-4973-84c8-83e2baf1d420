using System;
using System.Linq.Expressions;
using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Dtos.Department;
using LinqKit;

namespace Injaz.Core.Dtos.CapacityPlanning.DataEntryPeriods;

public class DataEntryPeriodGetDto
{
    public Guid Id { get; set; }
    public DepartmentSimpleDto Department { get; set; }
    public TimeDimensionSimpleDto Time { get; set; }

    public bool IsActiveServiceChannelDemand { get; set; }
    public bool IsActiveCenterParameters { get; set; }

    public bool IsActiveCenterMonthlyDemand { get; set; }

    public bool IsActiveServiceDemand { get; set; }
    public DateTime CreationTime { get; set; }

    public static
        Expression<Func<Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.TimeDimensionEntryPeriod,
            DataEntryPeriodGetDto>> Mapper(string lang)
    {
        var departmentExp = DepartmentSimpleDto.Mapper(lang);
        var timeDimensionExp = TimeDimensionSimpleDto.Mapper();

        return p => new DataEntryPeriodGetDto
        {
            Id = p.Id,
            Department = p.Department != null ? departmentExp.Invoke(p.Department) : null,
            Time = p.Time != null ? timeDimensionExp.Invoke(p.Time) : null,
            IsActiveServiceChannelDemand = p.IsActiveServiceChannelDemand,
            IsActiveCenterParameters = p.IsActiveCenterParameters,
            IsActiveCenterMonthlyDemand = p.IsActiveCenterMonthlyDemand,
            IsActiveServiceDemand = p.IsActiveServiceDemand,
            CreationTime = p.CreationTime
        };
    }
}
