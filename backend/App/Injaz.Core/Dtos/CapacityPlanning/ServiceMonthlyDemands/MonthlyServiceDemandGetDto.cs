using System;
using System.Linq.Expressions;
using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Service;
using LinqKit;

namespace Injaz.Core.Dtos.CapacityPlanning.ServiceMonthlyDemands;

// Get DTO
public class ServiceMonthlyDemandGetDto
{
    public Guid Id { get; set; }
    public DepartmentSimpleDto Department { get; set; }
    public TimeDimensionSimpleDto Time { get; set; }
    public ServiceSimpleDto Service { get; set; }

    public int RequestsCount { get; set; }



    public static Expression<Func<Models.DomainClasses.App.CapacityPlanningModel.ServiceMonthlyDemand, ServiceMonthlyDemandGetDto>> Mapper(string lang)
    {
        var departmentExp = DepartmentSimpleDto.Mapper(lang);
        var timeDimensionExp = TimeDimensionSimpleDto.Mapper();
        var serviceExp = ServiceSimpleDto.Mapper(lang);

        return e => new ServiceMonthlyDemandGetDto
            {
                Id = e.Id,
                Department = e.Department != null ? departmentExp.Invoke(e.Department) : null,
                Time = e.TimeDimensions != null ? timeDimensionExp.Invoke(e.TimeDimensions) : null,
                Service = e.Service != null ? serviceExp.Invoke(e.Service) : null,
                RequestsCount = e.RequestsCount,
            };
    }
}
