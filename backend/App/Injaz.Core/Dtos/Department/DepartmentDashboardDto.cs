using System;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Models.SqlFunctions;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;
using BenchmarkModel = Injaz.Core.Models.DomainClasses.App.Benchmark;
using OperationModel = Injaz.Core.Models.DomainClasses.App.Operation;
using PlanModel = Injaz.Core.Models.DomainClasses.App.Plan;

namespace Injaz.Core.Dtos.Department;

public class DepartmentDashboardDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Department, DepartmentDashboardDto>> Mapper(
        string lang,
        int year,
        IQueryable<KpiModel> kpis,
        IQueryable<PlanModel> plans,
        IQueryable<BenchmarkModel> benchmarks,
        IQueryable<OperationModel> operations,
        bool canAchievedBeNegative
    )
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);

        return item => new DepartmentDashboardDto
        {
            Id = item.Id,
            Name = lang == "en" ? item.NameEn : item.NameAr,
            ChildCount = item.Children.Count,
            RecursiveKpiCount = kpis
                .Where(x => x.Status.Equals(KpiModel.StatusActive))
                .Count(x =>
                    x.DepartmentLinks.Any(y => y.Department.HierarchyCode.StartsWith(item.HierarchyCode))
                ),
            RecursivePlanCount = plans
                .Count(x =>
                    x.AssignedDepartment != null &&
                    x.AssignedDepartment.HierarchyCode.StartsWith(item.HierarchyCode)
                ),
            RecursiveBenchmarkCount = benchmarks
                .Count(x => x.Department.HierarchyCode.StartsWith(item.HierarchyCode)),
            RecursiveOperationCount = operations
                .Count(x => x.OwnerDepartment.HierarchyCode.StartsWith(item.HierarchyCode)),
            Achieved = ObtainDepartmentAchievedSqlFunction.Call(item.Id, year, canAchievedBeNegative),
            ParentDepartment = item.ParentDepartmentId == null
                ? null
                : departmentExpression.Invoke(item.ParentDepartment)
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public int ChildCount { get; set; }
    public int RecursiveKpiCount { get; set; }
    public int RecursivePlanCount { get; set; }
    public int RecursiveBenchmarkCount { get; set; }
    public int RecursiveOperationCount { get; set; }
    public double? Achieved { get; set; }
    public DepartmentSimpleDto ParentDepartment { get; set; }
}
