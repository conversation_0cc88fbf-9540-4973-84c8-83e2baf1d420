using System;
using System.Linq.Expressions;

public class DepartmentSearchDto
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string FullPath { get; set; }
    public string ParentNames { get; set; }
    public int LevelNumber { get; set; }
    public bool IsMain { get; set; }
    public bool IsPoliceStation { get; set; }

    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.Department, DepartmentSearchDto>> Mapper(string lang)
    {
        return item => new DepartmentSearchDto
        {
            Id = item.Id,
            Name = lang == "en" ? item.NameEn : item.NameAr,
            LevelNumber = item.LevelNumber ?? 1,
            IsMain = item.IsMain == 1,
            IsPoliceStation = item.IsPoliceStation,
            FullPath = string.Empty,
            ParentNames = string.Empty
        };
    }
}