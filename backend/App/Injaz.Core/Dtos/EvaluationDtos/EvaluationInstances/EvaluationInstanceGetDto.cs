using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;

namespace Injaz.Core.Dtos.EvaluationDtos.EvaluationInstances;

public class EvaluationInstanceGetDto
{
    public static Expression<Func<EvaluationInstance, EvaluationInstanceGetDto>> Mapper(string lang)
    {
        var recordExpression = EvaluationStandardRecordDto.Mapper(lang);

        return item => new EvaluationInstanceGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Description = lang == SupportedCultures.LanguageArabic ? item.DescriptionAr : item.DescriptionEn,
            CreationTime = item.CreationTime,
            Note = item.Note,
            Score = item.Records.Average(x => x.Value / x.Target),
            Records = item.Records.Select(x => recordExpression.Invoke(x)).ToList(),
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public DateTime CreationTime { get; set; }

    public double Score { get; set; }

    public string Note { get; set; }

    public IEnumerable<EvaluationStandardRecordDto> Records { get; set; }
}
