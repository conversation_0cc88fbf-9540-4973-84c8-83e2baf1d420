using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using Injaz.Core.Evaluate.Dtos;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;

namespace Injaz.Core.Dtos.EvaluationDtos.Evaluations;

public class EvaluationGetDto
{
    public Guid Id { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public string Type { get; set; }

    public IEnumerable<EvaluationStandardGetDto> Standards { get; set; }

    public IEnumerable<EvaluationScoreBandGetDto> ScoreBands { get; set; }

    public IEnumerable<EvaluationEntitySimpleDto> Entities { get; set; }
}

public class EvaluationStandardGetDto
{
    public string Name { get; set; }

    public double Target { get; set; }
}

public class EvaluationScoreBandGetDto
{
    public static Expression<Func<EvaluationScoreBand, EvaluationScoreBandGetDto>> Mapper(string lang)
    {
        return item => new EvaluationScoreBandGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            From = item.From,
            To = item.To,
            Color = item.Color
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public double? From { get; set; }

    public double? To { get; set; }

    public string Color { get; set; }
}
