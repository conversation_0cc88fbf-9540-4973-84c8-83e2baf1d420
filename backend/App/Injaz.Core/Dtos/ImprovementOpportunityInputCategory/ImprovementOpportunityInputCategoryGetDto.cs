using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.ImprovementOpportunityInputCategory;

public class ImprovementOpportunityInputCategoryGetDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.ImprovementOpportunityInputCategory, ImprovementOpportunityInputCategoryGetDto>> Mapper(string lang)
    {
        return item => new ImprovementOpportunityInputCategoryGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
