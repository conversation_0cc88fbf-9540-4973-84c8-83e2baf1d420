using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.ImprovementOpportunityInputSource;

public class ImprovementOpportunityInputSourceEditDto : ImprovementOpportunityInputSourceCreateDto
{
    public static
        Expression<Func<Core.Models.DomainClasses.App.ImprovementOpportunityInputSource,
            ImprovementOpportunityInputSourceEditDto>> Mapper(string lang)
    {
        return item => new ImprovementOpportunityInputSourceEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
        };
    }

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}
