using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using InnovatorModel = Injaz.Core.Models.DomainClasses.App.Innovator;

namespace Injaz.Core.Dtos.Innovator;

public class InnovatorDetailDto
{
    public static Expression<Func<InnovatorModel, InnovatorDetailDto>> Mapper(string lang)
    {
        return item => new InnovatorDetailDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
            EmployeeNumber = item.EmployeeNumber,
            Rank = item.Rank

        };
    }
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string EmployeeNumber { get; set; }
    public string Rank { get; set; }
}
