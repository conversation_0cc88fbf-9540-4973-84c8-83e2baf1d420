using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using InnovatorModel = Injaz.Core.Models.DomainClasses.App.Innovator;

namespace Injaz.Core.Dtos.Innovator;

public class InnovatorEditDto: InnovatorCreateDto
{
    public static Expression<Func<InnovatorModel, InnovatorEditDto>> Mapper()
    {
        return item => new InnovatorEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            EmployeeNumber = item.EmployeeNumber,
            Rank = item.Rank,
            HasALogo = item.HasALogo == 1
        };
    }

    [Required(ErrorMessage = "0_is_required")]
    [Display(Name="id")]
    public Guid Id { get; set; }
}
