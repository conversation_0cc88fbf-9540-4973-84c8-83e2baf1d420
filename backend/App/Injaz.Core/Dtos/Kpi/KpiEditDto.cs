using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.KpiBalancedBehaviorCard;
using Injaz.Core.Dtos.KpiTag;
using Injaz.Core.Dtos.KpiType;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.OperationProcedure;
using Injaz.Core.Dtos.StrategicGoal;

namespace Injaz.Core.Dtos.Kpi;

public class KpiEditDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Kpi, KpiEditDto>> Mapper(string lang)
    {
        var typeExpression = KpiTypeWithCodeDto.Mapper(lang);
        var balancedBehaviorCardExpression = KpiBalancedBehaviorCardSimpleDto.Mapper(lang);
        var tagExpression = KpiTagSimpleDto.Mapper(lang);
        var departmentExpression = DepartmentListDto.Mapper(lang);
        var departmentSimpleExpression = DepartmentSimpleDto.Mapper(lang);
        var operationExpression = OperationWithLevelDto.Mapper(lang);
        var operationProcedureExpression = OperationProcedureWithCodeDto.Mapper(lang);
        var strategicGoalsExpression = StrategicGoalSimpleDto.Mapper(lang);

        return item => new KpiEditDto
        {
            Id = item.Id,
            Code = item.Code,
            Type = typeExpression.Invoke(item.Type),
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            Units = item.Units,
            UnitsDescription = item.UnitsDescription,
            Formula = item.Formula,
            FormulaDescriptionAAr = item.FormulaDescriptionAAr,
            FormulaDescriptionAEn = item.FormulaDescriptionAEn,
            FormulaDescriptionBAr = item.FormulaDescriptionBAr,
            FormulaDescriptionBEn = item.FormulaDescriptionBEn,
            DecreaseIsBest = item.Direction == Models.DomainClasses.App.Kpi.DirectionDecrease,
            Source = item.Source,
            Entity = item.Entity,
            CreationYear = item.CreationYear,
            DescriptionAr = item.DescriptionAr,
            DescriptionEn = item.DescriptionEn,
            MeasurementCycle = item.MeasurementCycle,
            MeasurementMethod = item.MeasurementMethod,
            DataEntryMethod = item.DataEntryMethod,
            BalancedBehaviorCard = item.BalancedBehaviorCardId == null
                ? null
                : balancedBehaviorCardExpression.Invoke(item.BalancedBehaviorCard),
            OwningDepartment = departmentSimpleExpression.Invoke(item.OwningDepartment),
            MeasuringDepartment = item.MeasuringDepartment == null
                ? null
                : departmentSimpleExpression.Invoke(item.MeasuringDepartment),
            AggregationTypeA = item.AggregationTypeA,
            AggregationTypeB = item.AggregationTypeB,
            InitialResultSource = item.InitialResultSource,
            InitialResult = item.InitialResult,
            InitialResultDetails = item.InitialResultDetails,
            Tags = item.TagLinks.Select(x => tagExpression.Invoke(x.Tag)).ToList(),
            Departments = item.DepartmentLinks.Select(x => departmentExpression.Invoke(x.Department)).ToList(),
            Operations = item.OperationLinks.Select(x => operationExpression.Invoke(x.Operation)).ToList(),
            OperationProcedures = item.OperationProcedureLinks
                .Select(x => operationProcedureExpression.Invoke(x.Procedure))
                .ToList(),
            StrategicGoals = item.StrategicGoalLinks.Select(x => strategicGoalsExpression.Invoke(x.StrategicGoal))
                .ToList(),
            IsSpecial = item.IsSpecial == 1,
            IsTrend = item.IsTrend == 1,
            IsExemptFromEvaluation = item.IsExemptFromEvaluation == 1,
            DecimalPlaces = item.DecimalPlaces
        };
    }


    public Guid Id { get; set; }


    public string Code { get; set; }


    public KpiTypeWithCodeDto Type { get; set; }


    public string NameAr { get; set; }

    public string NameEn { get; set; }


    public string Units { get; set; }

    public string UnitsDescription { get; set; }


    public string Formula { get; set; }


    public string FormulaDescriptionAAr { get; set; }


    public string FormulaDescriptionAEn { get; set; }


    public string FormulaDescriptionBAr { get; set; }


    public string FormulaDescriptionBEn { get; set; }

    //
    //
    //
    // public string Direction { get; set; }
    //


    public bool DecreaseIsBest { get; set; }


    public string Source { get; set; }


    public int CreationYear { get; set; }


    public string DescriptionAr { get; set; }


    public string DescriptionEn { get; set; }

    public string CalculateAbType { get; set; }


    public string MeasurementCycle { get; set; }


    public string MeasurementMethod { get; set; }

    public string DataEntryMethod { get; set; }

    public string Entity { get; set; }


    public KpiBalancedBehaviorCardSimpleDto BalancedBehaviorCard { get; set; }


    public DepartmentSimpleDto OwningDepartment { get; set; }

    public DepartmentSimpleDto MeasuringDepartment { get; set; }
    public string AggregationTypeA { get; set; }


    public string AggregationTypeB { get; set; }


    public string InitialResultSource { get; set; }

    public double? InitialResult { get; set; }


    public string InitialResultDetails { get; set; }

    public IEnumerable<KpiTagSimpleDto> Tags { get; set; }


    public IEnumerable<DepartmentListDto> Departments { get; set; }

    public IEnumerable<OperationWithLevelDto> Operations { get; set; }


    public IEnumerable<OperationProcedureWithCodeDto> OperationProcedures { get; set; }

    public IEnumerable<StrategicGoalSimpleDto> StrategicGoals { get; set; }

    public bool IsSpecial { get; set; }

    public bool IsTrend { get; set; }

    public bool IsExemptFromEvaluation { get; set; }
    public int DecimalPlaces { get; set; }
}
