using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.KpiResult;
using Microsoft.EntityFrameworkCore;

namespace Injaz.Core.Dtos.Kpi;

public class KpiInfoForCreatingKpiResultDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Kpi, KpiInfoForCreatingKpiResultDto>> Mapper(
        string lang,
        int year,
        Guid departmentId
    )
    {
        var kpiResultExpression = KpiResultRespectingFieldsDto.Mapper();
        var isResultOwningDepartmentExpression = HelperExpression.IsKpiResultOwningDepartment();
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);

        return item => new KpiInfoForCreatingKpiResultDto
        {
            Units = item.Units,
            UnitsDescription = item.UnitsDescription,
            MeasurementCycle = item.MeasurementCycle,
            MeasurementMethod = item.MeasurementMethod,
            Formula = item.Formula,
            FormulaDescriptionAAr = item.FormulaDescriptionAAr,
            FormulaDescriptionAEn = item.FormulaDescriptionAEn,
            FormulaDescriptionBAr = item.FormulaDescriptionBAr,
            FormulaDescriptionBEn = item.FormulaDescriptionBEn,
            AggregationTypeA = item.AggregationTypeA,
            AggregationTypeB = item.AggregationTypeB,
            DataEntryMethod = item.DataEntryMethod,
            IsTrend = item.IsTrend == 1,
            // IsApproved = item.Results
            //     .Any(x =>
            //         x.Year.Equals(year) &&
            //         x.DepartmentId.Equals(departmentId) &&
            //         x.IsApproved == 1
            //     ),

            Result = item.Results
                .AsQueryable()
                .Include(x => x.Periods)
                .FirstOrDefault(x => x.Year.Equals(year) && x.DepartmentId.Equals(departmentId)),

            // Get a list of results that follow the owning results (this list could contain the current
            // result to be updated).
            RespectingResults = item.Results
                .AsQueryable()
                .Include(x => x.Periods)
                .Where(x => x.Year.Equals(year) && !isResultOwningDepartmentExpression.Invoke(x))
                .ToList(),

            // Tells whether the input mode of the result can be
            // switched between manual and auto.
            // CanChangeInputMode =
            //     hasMoreThanOneDepartmentExpression.Invoke(item) &&
            //     owningDepartmentExpression.Invoke(item, year).Id == departmentId,
            OwningDepartment = departmentExpression.Invoke(item.OwningDepartment),

            LinkedDepartmentWithKpiAndResultForYearCount = item.DepartmentLinks
                .Select(y => y.DepartmentId)
                .Union(item.Results.Where(y => y.Year.Equals(year)).Select(y => y.DepartmentId))
                .Count(),

            // Tells if the department is still linked to the kpi.
            IsDepartmentLinked = item.DepartmentLinks.Any(x => x.DepartmentId.Equals(departmentId)),

            // Tells if the kpi result is allowed to have whatever values, or
            // abide by the result of the owning department.
            // ShouldRespectOwning =
            //     hasMoreThanOneDepartmentExpression.Invoke(item) &&
            //     owningDepartmentExpression.Invoke(item, year).Id != departmentId,

            Direction = item.Direction,

            LinkedDepartmentsWithNoResultForYear = item.DepartmentLinks
                .Where(x =>
                    !item.Results.Where(y => y.Year == year).Any(y => y.DepartmentId == x.DepartmentId)
                )
                .Select(x => departmentExpression.Invoke(x.Department))
                .ToList(),

            // Default parameters.
            OwningKpiResultParams = item.Results
                .Where(
                    x => x.Year.Equals(year) &&
                         isResultOwningDepartmentExpression
                             .Invoke(x) /*x.DepartmentId.Equals(owningDepartmentExpression.Invoke(item).Id)*/)
                .Select(x => kpiResultExpression.Invoke(x))
                .FirstOrDefault()
        };
    }

    // public bool IsApproved { get; set; }
    public string MeasurementMethod { get; set; }
    public string MeasurementCycle { get; set; }
    public string Formula { get; set; }
    public string Units { get; set; }
    public string UnitsDescription { get; set; }
    public string FormulaDescriptionAAr { get; set; }
    public string FormulaDescriptionAEn { get; set; }
    public string FormulaDescriptionBAr { get; set; }
    public string FormulaDescriptionBEn { get; set; }
    public string AggregationTypeA { get; set; }
    public string AggregationTypeB { get; set; }
    public string DataEntryMethod { get; set; }

    public Core.Models.DomainClasses.App.KpiResult Result { get; set; }

    public IEnumerable<Core.Models.DomainClasses.App.KpiResult> RespectingResults { get; set; }

    // public bool CanChangeInputMode { get; set; }
    public bool IsDepartmentLinked { get; set; }

    public bool IsTrend { get; set; }

    // public bool ShouldRespectOwning { get; set; }
    public string Direction { get; set; }
    public KpiResultRespectingFieldsDto OwningKpiResultParams { get; set; }

    public DepartmentSimpleDto OwningDepartment { get; set; }

    public int LinkedDepartmentWithKpiAndResultForYearCount { get; set; }

    public IEnumerable<DepartmentSimpleDto> LinkedDepartmentsWithNoResultForYear { get; set; }
}
