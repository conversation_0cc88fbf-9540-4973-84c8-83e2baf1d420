using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Kpi;

public class KpiSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Kpi, KpiSimpleDto>> Mapper(string lang) => item => new KpiSimpleDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
