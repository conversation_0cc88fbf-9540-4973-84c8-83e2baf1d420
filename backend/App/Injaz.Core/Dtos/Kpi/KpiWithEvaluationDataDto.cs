using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Kpi;

public class KpiWithEvaluationDataDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Kpi, KpiWithEvaluationDataDto>>
        Mapper(string lang) => item => new KpiWithEvaluationDataDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        CreationYear = item.CreationYear,
        IsExemptFromEvaluation = item.IsExemptFromEvaluation == 1,
        DecimalPlaces = item.DecimalPlaces
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
    public int CreationYear { get; set; }
    public bool IsExemptFromEvaluation { get; set; }
    public int DecimalPlaces { get; set; }
}
