using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.KpiBalancedBehaviorCard;

public class KpiBalancedBehaviorCardEditDto
{
    public static Expression<Func<Models.DomainClasses.App.KpiBalancedBehaviorCard, KpiBalancedBehaviorCardEditDto>>
        Mapper()
    {
        return item => new KpiBalancedBehaviorCardEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
        };
    }

    public Guid Id { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }
}
