using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.LibraryFile;

namespace Injaz.Core.Dtos.KpiBenchmark;

public class KpiBenchmarkEditDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.KpiBenchmark, KpiBenchmarkEditDto>> Mapper(
        string lang)
    {
        var fileExpression = LibraryFileSimpleDto.Mapper(lang);

        return item => new KpiBenchmarkEditDto
        {
            Id = item.Id,
            EntityName = item.EntityName,
            Year = item.Year,
            Result = item.Result,
            LibraryFile = fileExpression.Invoke(item.LibraryFile),
            CompetitiveEffect = item.CompetitiveEffect
        };
    }


    public Guid Id { get; set; }

    public string EntityName { get; set; }

    public int? Year { get; set; }

    public double? Result { get; set; }

    public LibraryFileSimpleDto LibraryFile { get; set; }

    public string CompetitiveEffect { get; set; }
}
