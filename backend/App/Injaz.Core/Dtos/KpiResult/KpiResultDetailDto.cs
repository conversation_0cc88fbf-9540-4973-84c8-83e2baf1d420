using System;
using System.Collections.Generic;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.KpiResultTargetSettingMethod;
using Injaz.Core.Evaluate.Models;

namespace Injaz.Core.Dtos.KpiResult;

public class KpiResultDetailDto
{
    public Guid Id { get; set; }
    public KpiWithEvaluationDataDto Kpi { get; set; }
    public int Year { get; set; }
    public DepartmentSimpleDto Department { get; set; }
    public string Units { get; set; }
    public string UnitsDescription { get; set; }
    public string Formula { get; set; }
    public string FormulaDescriptionA { get; set; }
    public string FormulaDescriptionB { get; set; }
    public double? Target { get; set; }
    public double? TargetZero { get; set; }
    public double? Result { get; set; }
    public int CurrentPeriod { get; set; }
    public double? Achieved { get; set; }
    public double? AggregateA { get; set; }
    public double? AggregateB { get; set; }
    public string MeasurementCycle { get; set; }
    public string MeasurementMethod { get; set; }
    public int CapabilityCount { get; set; }
    public int LibraryFileCount { get; set; }
    public KpiResultTargetSettingMethodGetDto TargetSettingMethod { get; set; }
    public bool IsOwningDepartment { get; set; }
    public string AggregationTypeA { get; set; }
    public string AggregationTypeB { get; set; }
    public bool IsExemptFromEvaluation { get; set; }
    public int DecimalPlaces { get; set; }
    public IEnumerable<KpiResultPeriodWithEvaluationDto> Periods { get; set; }
    public EvaluationScoreDetail TotalPeriodsEvaluationScore { get; set; }
}
