using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.KpiResultCapabilityType;

namespace Injaz.Core.Dtos.KpiResultCapability;

public class KpiResultCapabilityEditDto : KpiResultCapabilityCreateDto
{
    public static
        Expression<Func<Core.Models.DomainClasses.App.KpiResultCapability, KpiResultCapabilityEditDto>> Mapper(
            string lang)
    {
        var typePredicate = KpiResultCapabilityTypeSimpleDto.Mapper(lang);

        return item => new KpiResultCapabilityEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            DescriptionAr = item.DescriptionAr,
            DescriptionEn = item.DescriptionEn,
            Type = typePredicate.Invoke(item.Type)
        };
    }

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}
