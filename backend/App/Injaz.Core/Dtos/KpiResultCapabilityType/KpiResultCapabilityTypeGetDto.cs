using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.KpiResultCapabilityType;

public class KpiResultCapabilityTypeGetDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.KpiResultCapabilityType, KpiResultCapabilityTypeGetDto>>
        Mapper(string lang) => item => new KpiResultCapabilityTypeGetDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
