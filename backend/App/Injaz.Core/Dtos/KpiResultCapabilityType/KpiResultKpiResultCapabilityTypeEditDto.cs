using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.KpiResultCapabilityType;

public class KpiResultKpiResultCapabilityTypeEditDto : KpiResultCapabilityTypeCreateDto
{
    public static
        Expression<Func<Core.Models.DomainClasses.App.KpiResultCapabilityType, KpiResultKpiResultCapabilityTypeEditDto>>
        Mapper() => item => new KpiResultKpiResultCapabilityTypeEditDto
    {
        Id = item.Id,
        NameAr = item.NameAr,
        NameEn = item.NameEn,
    };

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}
