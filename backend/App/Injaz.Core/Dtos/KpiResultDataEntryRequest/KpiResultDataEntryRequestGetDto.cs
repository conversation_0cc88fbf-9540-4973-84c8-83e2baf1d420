using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Models.DomainClasses.App;
using KpiResultDataEntryResponseModel = Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponse;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;

namespace Injaz.Core.Dtos.KpiResultDataEntryRequest;

public class KpiResultDataEntryRequestGetDto
{
    public static
        Expression<Func<Core.Models.DomainClasses.App.KpiResultDataEntryRequest, KpiResultDataEntryRequestGetDto>>
        Mapper(string lang)
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var kpiExpression = KpiSimpleDto.Mapper(lang);
        Expression<Func<KpiResultDataEntryResponseModel, KpiResultDataEntryResponseTransfer>> transfer =
            x => x.Transfers.OrderByDescending(y => y.CreationTime).First();

        return item => new KpiResultDataEntryRequestGetDto
        {
            Id = item.Id,
            Year = item.Year,
            StartTime = item.StartTime,
            EndTime = item.EndTime,
            MeasurementCycle = item.MeasurementCycle,
            Periods = item.Periods,
            Note = item.Note,
            Path = item.Path,
            Departments = item.DepartmentLinks.Select(x => departmentExpression.Invoke(x.Department)).ToList(),
            Kpis = item.KpiLinks.Select(x => kpiExpression.Invoke(x.Kpi)).ToList(),

            // Calculating the completion rate of the request. Check if there is no responses return 1
            // else it should calculate the ratio of `done` and `KpiManager` responses to the total responses
            CompletionRate =
                !item.Responses.Any(x => x.Result.Kpi.Status == KpiModel.StatusActive)
                    ? 1
                    : (double)(item
                          .Responses
                          .Where(x => x.Result.Kpi.Status == KpiModel.StatusActive)
                          .Count(x =>
                              transfer.Invoke(x).Assignee == KpiResultDataEntryResponseTransfer.AssigneeDone ||
                              transfer.Invoke(x).Assignee ==
                              KpiResultDataEntryResponseTransfer.AssigneeKpiManager)) /
                      (double)(item.Responses.Count(x => x.Result.Kpi.Status == KpiModel.StatusActive)),
            IsSpecial = item.IsSpecial == 1
        };
    }

    public Guid Id { get; set; }
    public int Year { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string MeasurementCycle { get; set; }
    public int[] Periods { get; set; }
    public string Note { get; set; }
    public string Path { get; set; }
    public double CompletionRate { get; set; }
    public bool IsSpecial { get; set; }
    public IEnumerable<DepartmentSimpleDto> Departments { get; set; }
    public IEnumerable<KpiSimpleDto> Kpis { get; set; }
}
