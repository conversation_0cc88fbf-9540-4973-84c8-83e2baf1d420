using System;
using Injaz.Core.Dtos.KpiResult;

namespace Injaz.Core.Dtos.KpiResultDataEntryResponse;

public class KpiResultDataEntryResponseExportDto
{
    public Guid Id { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public KpiResultDataEntryResponseTransferDto CurrentTransfer { get; set; }
    public KpiResultSummaryDto Result { get; set; }
}
