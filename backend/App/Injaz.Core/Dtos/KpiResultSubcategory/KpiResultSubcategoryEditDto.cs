using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.KpiResultCategory;

namespace Injaz.Core.Dtos.KpiResultSubcategory;

public class KpiResultSubcategoryEditDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.KpiResultSubcategory, KpiResultSubcategoryEditDto>>
        Mapper(string lang)
    {
        var categoryExpression = KpiResultCategorySimpleDto.Mapper(lang);

        return item => new KpiResultSubcategoryEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            Category = categoryExpression.Invoke(item.Category)
        };
    }

    public Guid Id { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }

    public KpiResultCategorySimpleDto Category { get; set; }
}
