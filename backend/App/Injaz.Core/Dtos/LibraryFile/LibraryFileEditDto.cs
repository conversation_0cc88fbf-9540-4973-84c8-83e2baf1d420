using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.LibraryTag;

namespace Injaz.Core.Dtos.LibraryFile;

public class LibraryFileEditDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.LibraryFile, LibraryFileEditDto>> Mapper(
        string lang)
    {
        var tagPredicate = LibraryTagSimpleDto.Mapper(lang);

        return item => new LibraryFileEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            Link = item.Link,
            Scopes = item.Scopes,
            Tags = item.TagLinks.Select(x => tagPredicate.Invoke(x.Tag)).ToList()
        };
    }

    public Guid Id { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }

    public string Link { get; set; }

    public byte[] File { get; set; }
    public string[]? Scopes { get; set; }
    public IEnumerable<LibraryTagSimpleDto> Tags { get; set; }
}
