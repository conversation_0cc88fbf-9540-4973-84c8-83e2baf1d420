using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.LibraryTag;
using Injaz.Core.Dtos.User;
using Injaz.Core.Models.SqlFunctions;

namespace Injaz.Core.Dtos.LibraryFile;

public class LibraryFileGetDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.LibraryFile, LibraryFileGetDto>> Mapper(string lang)
    {
        var userPredicate = UserSimpleDto.Mapper(lang);
        var tagPredicate = LibraryTagSimpleDto.Mapper(lang);
        var useCount = HelperExpression.LibraryFileUseCount();

        return item => new LibraryFileGetDto
        {
            Id = item.Id,
            Name = lang.Equals(SupportedCultures.LanguageArabic) ? item.NameAr : item.NameEn,
            FileSize = item.FileSize,
            ContentType = item.ContentType,
            LatestModificationTime = item.LatestModificationTime,
            LatestModificationBy = string.IsNullOrEmpty(item.ModificationHistory)
                ? null
                : ParseModificationsHistorySqlFunction.Call(item.ModificationHistory)
                    .OrderByDescending(x => x.Time)
                    .First()
                    .UserFullName
                    .Split('|', StringSplitOptions.None)
                    [SupportedCultures.LanguageArabic == lang ? 0 : 1]
                    .Trim(),
            Link = item.Link,
            UsageCount = useCount.Invoke(item),
            HasFile = !string.IsNullOrEmpty(item.FileName),
            Owner = userPredicate.Invoke(item.Owner),
            Tags = item.TagLinks.Select(x => tagPredicate.Invoke(x.Tag)).ToList(),
            CreationTime = item.CreationTime
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public int? FileSize { get; set; }
    public string ContentType { get; set; }
    public DateTime LatestModificationTime { get; set; }
    public string LatestModificationBy { get; set; }
    public string Link { get; set; }
    public int UsageCount { get; set; }
    public bool HasFile { get; set; }
    public UserSimpleDto Owner { get; set; }
    public IEnumerable<LibraryTagSimpleDto> Tags { get; set; }
    public DateTime CreationTime { get; set; }
}
