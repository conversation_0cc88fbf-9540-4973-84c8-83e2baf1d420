using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.MainOperationOwner;

public class MainOperationOwnerEditDto : MainOperationOwnerCreateDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.MainOperationOwner, MainOperationOwnerEditDto>> Mapper() => item =>
        new MainOperationOwnerEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn
        };

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}
