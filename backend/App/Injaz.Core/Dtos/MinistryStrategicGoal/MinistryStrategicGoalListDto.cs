using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.MinistryStrategicGoal;

public class MinistryStrategicGoalListDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.MinistryStrategicGoal, MinistryStrategicGoalListDto>>
        Mapper(string lang) => item =>
        new MinistryStrategicGoalListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
