using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using NationalAgendaModel = Injaz.Core.Models.DomainClasses.App.NationalAgenda;

namespace Injaz.Core.Dtos.NationalAgenda;

public class NationalAgendaEditDto : NationalAgendaCreateDto
{
    public static Expression<Func<NationalAgendaModel, NationalAgendaEditDto>> Mapper()
    {
        return item => new NationalAgendaEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
        };
    }
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}
