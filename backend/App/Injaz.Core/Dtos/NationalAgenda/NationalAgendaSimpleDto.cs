using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using NationalAgendaModel = Injaz.Core.Models.DomainClasses.App.NationalAgenda;

namespace Injaz.Core.Dtos.NationalAgenda;

public class NationalAgendaSimpleDto
{
    public static Expression<Func<NationalAgendaModel, NationalAgendaSimpleDto>> Mapper(string lang)
    {
        return item => new NationalAgendaSimpleDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr
        };
    }
    public Guid Id { get; set; }
    public string Name { get; set; }
}
