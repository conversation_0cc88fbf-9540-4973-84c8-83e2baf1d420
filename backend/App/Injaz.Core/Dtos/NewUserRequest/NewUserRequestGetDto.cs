using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.User;

namespace Injaz.Core.Dtos.NewUserRequest;

public class NewUserRequestGetDto
{
    public static Expression<Func<Models.DomainClasses.App.NewUserRequest, NewUserRequestGetDto>> Mapper(
        string lang,
        Guid userId,
        Expression<Func<Core.Models.DomainClasses.App.NewUserRequest, Guid, object>> flowActionAvailabilityExpression)
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);
        
        return item => new NewUserRequestGetDto
        {
            Id = item.Id,
            CreationTime = item.CreationTime,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            Email = item.Email,
            Gender = item.Gender,
            EmployeeNumber = item.EmployeeNumber,
            Rank = item.Rank,
            RequestedPermissions = item.RequestedPermissions,
            Department = departmentExpression.Invoke(item.Department),
            User = item.User == null ? null : userExpression.Invoke(item.User),
            FlowState = item.FlowState,
            FlowActionAvailability = flowActionAvailabilityExpression.Invoke(item, userId),
        };
    }

    public Guid Id { get; set; }

    public DateTime CreationTime { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }

    public string Email { get; set; }

    public string Gender { get; set; }

    public string EmployeeNumber { get; set; }

    public string Rank { get; set; }

    public string RequestedPermissions { get; set; }

    public DepartmentSimpleDto Department { get; set; }
    
    public UserSimpleDto User { get; set; }

    public string FlowState { get; set; }
    
    public object FlowActionAvailability { get; set; }
}