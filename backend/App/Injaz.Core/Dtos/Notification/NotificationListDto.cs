using System;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using NotificationModel = Injaz.Core.Models.DomainClasses.App.Notification;

namespace Injaz.Core.Dtos.Notification;

public class NotificationListDto
{
    public static Expression<Func<NotificationModel, NotificationListDto>> Mapper(string lang)
    {
        return item => new NotificationListDto
        {
            Id = item.Id,
            CreationTime = item.CreationTime,
            Title = lang == SupportedCultures.LanguageArabic ? item.TitleAr : item.TitleEn,
            Description = lang == SupportedCultures.LanguageArabic ? item.DescriptionAr : item.DescriptionEn,
            UserCount = item.UserLinks.Count,
            ReadCount = item.UserLinks.Count(x => x.ReadTime != null),
            TargetType = item.TargetType,
            TargetId = item.TargetId,
            TargetMetadata = item.TargetMetadata,
        };
    }

    public Guid Id { get; set; }
    public DateTime CreationTime { get; set; }
    public string Title { get; set; }
    public string Description { get; set; }
    public string TargetType { get; set; }
    public Guid? TargetId { get; set; }
    public string TargetMetadata { get; set; }
    public int UserCount { get; set; }
    public int ReadCount { get; set; }
}
