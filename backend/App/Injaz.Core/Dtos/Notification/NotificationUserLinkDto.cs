using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.User;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Notification;

public class NotificationUserLinkDto
{
    public static Expression<Func<NotificationUserLink, NotificationUserLinkDto>> Mapper(string lang)
    {
        var expression = UserSimpleDto.Mapper(lang);

        return item => new NotificationUserLinkDto
        {
            ReadTime = item.ReadTime,
            User = expression.Invoke(item.User)
        };
    }

    public UserSimpleDto User { get; set; }
    public DateTime? ReadTime { get; set; }
}