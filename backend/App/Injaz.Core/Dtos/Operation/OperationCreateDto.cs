using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.MainOperationOwner;
using Injaz.Core.Dtos.OperationSpecification;
using Injaz.Core.Dtos.Partner;
using Injaz.Core.Dtos.Policy;
using Injaz.Core.Dtos.StrategicGoal;
using Injaz.Core.Dtos.SuccessFactor;
using OperationModel = Injaz.Core.Models.DomainClasses.App.Operation;

namespace Injaz.Core.Dtos.Operation;

public class OperationCreateDto
{
    [MaxLength(1024)]
    [Display(Name = "code")]
    [Required(ErrorMessage = "0_is_required")]
    public string Code { get; set; }

    [Display(Name = "number")]
    [Required(ErrorMessage = "0_is_required")]
    public int Number { get; set; }

    [MaxLength(1024)]
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [MaxLength(1024)]
    [Display(Name = "description_in_arabic")]
    public string DescriptionAr { get; set; }

    [MaxLength(1024)]
    [Display(Name = "name_in_english")]
    public string NameEn { get; set; }

    [MaxLength(1024)]
    [Display(Name = "description_in_arabic")]
    public string DescriptionEn { get; set; }

    [Display(Name = "output")] public string Output { get; set; }

    [Display(Name = "version")] public string? Version { get; set; }

    [Display(Name = "operation_level")]
    [Required(ErrorMessage = "0_is_required")]
    public int Level { get; set; }

    [Display(Name = "sustainability_impacts")]
    public string[] SustainabilityImpact { get; set; }

    [Display(Name = "purpose")] public string Purpose { get; set; }

    [Display(Name = "weight")] public double? Weight { get; set; }


    [Display(Name = "supplier_name")] public string SupplierName { get; set; }

    [Display(Name = "ministerial_code")] public string MinisterialCode { get; set; }

    [Display(Name = "local_code")] public string LocalCode { get; set; }

    [Display(Name = "duration")] public string Duration { get; set; }

    [Display(Name = "supplier_category")]
    [RegularExpression("(?:"
                       + OperationModel.SupplierTypeSilver + "|"
                       + OperationModel.SupplierTypeBronze + "|"
                       + OperationModel.SupplierTypeGolden
                       + ")", ErrorMessage = "0_is_invalid")]
    public string SupplierCategory { get; set; }

    [Display(Name = "danger_number")] public string DangerNumber { get; set; }

    [Display(Name = "terminologies")] public string Terminologies { get; set; }

    // OBJECT PROPERTIES.
    [Display(Name = "parent")] public OperationWithLevelDto ParentOperation { get; set; }

    [Display(Name = "owner_department")]
    [Required(ErrorMessage = "0_is_required")]
    public DepartmentListDto OwnerDepartment { get; set; }

    [Display(Name = "main_operation_owner")]
    public MainOperationOwnerSimpleDto MainOperationOwner { get; set; }

    public IEnumerable<StrategicGoalSimpleDto> StrategicGoals { get; set; }

    public IEnumerable<SuccessFactorDto> SuccessFactors { get; set; }
    public IEnumerable<OperationSpecificationSimpleDto> Specifications { get; set; }
    public IEnumerable<PolicySimpleDto> Policies { get; set; }
    public IEnumerable<PartnerSimpleDto> Partners { get; set; }
}
