using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Dtos.MainOperationOwner;
using Injaz.Core.Dtos.OperationRuleAndRegulation;
using Injaz.Core.Dtos.OperationSpecification;
using Injaz.Core.Dtos.OperationUpdateRequest;
using Injaz.Core.Dtos.Partner;
using Injaz.Core.Dtos.Policy;
using Injaz.Core.Dtos.Service;
using Injaz.Core.Dtos.StrategicGoal;
using Injaz.Core.Dtos.SuccessFactor;
using Injaz.Core.ValidationAttributes;
using OperationModel = Injaz.Core.Models.DomainClasses.App.Operation;

namespace Injaz.Core.Dtos.Operation;

public class OperationEditDto : OperationCreateDto
{
    public static Expression<Func<OperationModel, OperationEditDto>> Mapper(
        string lang)
    {
        var successFactoriesLinkExpression = SuccessFactorDto.Mapper(lang);
        var goalExpression = StrategicGoalSimpleDto.Mapper(lang);
        var mainOperationOwnerExpression = MainOperationOwnerSimpleDto.Mapper(lang);
        var operationExpression = OperationWithLevelDto.Mapper(lang);
        var departmentExpression = DepartmentListDto.Mapper(lang);
        var partnersLinkExpression = PartnerSimpleDto.Mapper(lang);

        var updateRequestExpression = OperationUpdateRequestGetDto.Mapper(lang);
        var hasPendingUpdateRequestExpression = HelperExpression.HasPendingUpdateRequest();
        var rejectedUpdateRequestExpression = HelperExpression.RejectedUpdateRequest();

        // level 4
        var libraryFileExpression = LibraryFileSimpleDto.Mapper(lang);
        var executorExpression = OperationExecutorDto.Mapper();
        var formFileExpression = OperationFormFileDto.Mapper(lang);
        var policyExpression = PolicySimpleDto.Mapper(lang);
        var ruleAndRegulationExpression = OperationRuleAndRegulationSimpleDto.Mapper(lang);
        var specificationExpression = OperationSpecificationSimpleDto.Mapper(lang);
        var serviceExpression = ServiceSimpleDto.Mapper(lang);

        return item => new OperationEditDto
        {
            Id = item.Id,
            Code = item.Code,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            DescriptionAr = item.DescriptionAr,
            DescriptionEn = item.DescriptionEn,
            Output = item.Output,
            Purpose = item.Purpose,
            Level = item.Level,
            SustainabilityImpact = item.SustainabilityImpact,
            Weight = item.Weight,
            DangerNumber = item.DangerNumber,
            ParentOperation = item.ParentId != null ? operationExpression.Invoke(item.Parent) : null,
            OwnerDepartment = item.OwnerDepartment != null
                ? departmentExpression.Invoke(item.OwnerDepartment)
                : null,
            SuccessFactors = item.SuccessFactorOperationLinks
                .Select(a => successFactoriesLinkExpression.Invoke(a.SuccessFactor)).ToList(),
            StrategicGoals = item.GoalLinks.Select(x => goalExpression.Invoke(x.StrategicGoal)).ToList(),
            MainOperationOwner = item.MainOperationOwner != null
                ? mainOperationOwnerExpression.Invoke(item.MainOperationOwner)
                : null,
            Partners = item.PartnerLinks.Select(a => partnersLinkExpression.Invoke(a.Partner))
                .ToList(),

            // level 4
            Inputs = item.Inputs,
            InputType = item.InputType,
            Outputs = item.Outputs,
            OutputType = item.OutputType,
            Version = item.Version,
            SupplierName = item.SupplierName,
            MinisterialCode = item.MinisterialCode,
            LocalCode = item.LocalCode,
            Duration = item.Duration,
            SupplierCategory = item.SupplierCategory,
            TechnicalSolutions = item.TechnicalSolutions,
            Terminologies = item.Terminologies,
            MainChartFlow = item.MainFlowChartFile != null
                ? libraryFileExpression.Invoke(item.MainFlowChartFile)
                : null,
            Beneficiaries = item.Beneficiaries,
            Executors = item.Executors.Select(x => executorExpression.Invoke(x)).ToList(),
            FormFiles = item.FormFileLinks.Select(x => formFileExpression.Invoke(x)).ToList(),
            RulesAndRegulations = item.RuleAndRegulationLinks
                .Select(x => ruleAndRegulationExpression.Invoke(x.OperationRuleAndRegulation)).ToList(),
            Policies = item.PolicyLinks.Select(x => policyExpression.Invoke(x.Policy)).ToList(),
            Types = item.Types,
            Specifications = item.SpecificationLinks.Select(x => specificationExpression.Invoke(x.Specification))
                .ToList(),
            Services = item.ServiceLinks.Select(x => serviceExpression.Invoke(x.Service)),
            HasPendingUpdateRequest = hasPendingUpdateRequestExpression.Invoke(item),
            RejectedUpdateRequest = rejectedUpdateRequestExpression.Invoke(item) == null
                ? null
                : updateRequestExpression.Invoke(rejectedUpdateRequestExpression.Invoke(item)),
        };
    }

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }

    // level 4
    [Display(Name = "inputs")] public string Inputs { get; set; }

    [Display(Name = "input_type")] public string InputType { get; set; }

    [Display(Name = "outputs")] public string Outputs { get; set; }

    [Display(Name = "output_type")] public string OutputType { get; set; }

    [Display(Name = "technical_solutions")]
    public string TechnicalSolutions { get; set; }

    [MaxLength(1024)]
    [Display(Name = "operation_types")]
    [ItemsInList(OperationModel.TypeElectronic, OperationModel.TypeManual, OperationModel.TypeSmart)]
    public IEnumerable<string> Types { get; set; }

    [Display(Name = "main_chart_flow_id")] public LibraryFileSimpleDto MainChartFlow { get; set; }

    public IEnumerable<OperationWithLevelDto> ChildOperations { get; set; }

    public IEnumerable<string> Beneficiaries { get; set; }

    public IEnumerable<OperationExecutorDto> Executors { get; set; }

    public IEnumerable<OperationFormFileDto> FormFiles { get; set; }

    public IEnumerable<OperationRuleAndRegulationSimpleDto> RulesAndRegulations { get; set; }
    public IEnumerable<ServiceSimpleDto> Services { get; set; }

    public bool HasPendingUpdateRequest { get; set; }

    public OperationUpdateRequestGetDto? RejectedUpdateRequest { get; set; }
}
