using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Department;

namespace Injaz.Core.Dtos.Operation;

public class OperationProjectionDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Operation, OperationProjectionDto>> Mapper(string lang)
    {
        var operationExpression = OperationWithLevelDto.Mapper(lang);
        var departmentExpression = DepartmentListDto.Mapper(lang);
        var operationSimpleExpression = OperationWithLevelDto.Mapper(lang);
        return item => new OperationProjectionDto
        {
            Id = item.Id,
            Code = item.Code,
            Order = item.Number,
            Name = lang == "en" ? item.NameEn : item.NameAr,
            Level = item.Level,
            Weight = item.Weight,
            ParentOperation =
                item.Parent == null ? null : operationExpression.Invoke(item.Parent),
            OwnerDepartment = item.OwnerDepartment != null
                ? departmentExpression.Invoke(item.OwnerDepartment)
                : null,
            Children = item.Children.Select(a => operationSimpleExpression.Invoke(a)).ToList(),
            LastEnhancementDate = item.Enhancements.OrderByDescending(x => x.Number).Select(x => x.Date)
                .FirstOrDefault()
        };
    }


    public Guid Id { get; set; }
    public int Order { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public int Level { get; set; }
    public double? Weight { get; set; }
    public DateTime? LastEnhancementDate { get; set; }


    public OperationWithLevelDto ParentOperation { get; set; }
    public DepartmentListDto OwnerDepartment { get; set; }
    public IEnumerable<OperationWithLevelDto> Children { get; set; }
}
