using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Operation;

public class OperationWithCodeDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Operation, OperationWithCodeDto>> Mapper(string lang)
    {
        return item => new OperationWithCodeDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
            Code = item.Code,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }

    public string Code { get; set; }
}
