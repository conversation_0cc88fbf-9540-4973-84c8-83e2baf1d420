using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;

namespace Injaz.Core.Dtos.Operation;

public class OperationWithOwnerAndLevelDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Operation, OperationWithOwnerAndLevelDto>> Mapper(
        string lang
    )
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);

        return item => new OperationWithOwnerAndLevelDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
            Level = item.Level,
            OwnerDepartment = item.OwnerDepartment != null
                ? departmentExpression.Invoke(item.OwnerDepartment)
                : null,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public int Level { get; set; }
    public DepartmentSimpleDto OwnerDepartment { get; set; }
}
