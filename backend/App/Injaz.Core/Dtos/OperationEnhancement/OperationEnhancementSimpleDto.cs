using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.OperationEnhancement;

public class OperationEnhancementSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.OperationEnhancement, OperationEnhancementSimpleDto>> Mapper(string lang)
    {
        return item => new OperationEnhancementSimpleDto
        {
            Id = item.Id,
            EnhancementDate = item.Date,
            EnhancementNumber = item.Number,
        };
    }

    public Guid Id { get; set; }
    public int EnhancementNumber { get; set; }
    public DateTime EnhancementDate  { get; set; }
}
