using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.OperationEnhancementType;

public class OperationEnhancementTypeSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.OperationEnhancementType, OperationEnhancementTypeSimpleDto>> Mapper(string lang)
    {
        return item => new OperationEnhancementTypeSimpleDto
        {
            Id = item.Id,
            Name = lang == "en" ? item.NameEn : item.NameAr,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
