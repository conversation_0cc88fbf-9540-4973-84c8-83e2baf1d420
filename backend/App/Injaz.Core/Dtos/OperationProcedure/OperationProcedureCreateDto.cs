using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.LibraryFile;
using OperationProcedureModel = Injaz.Core.Models.DomainClasses.App.OperationProcedure;

namespace Injaz.Core.Dtos.OperationProcedure;

public class OperationProcedureCreateDto
{
    [Display(Name = "code")]
    [Required(ErrorMessage = "0_is_required")]
    public string Code { get; set; }

    [Display(Name = "name_ar")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_en")] public string NameEn { get; set; }

    [Display(Name = "goal")]
    [Required(ErrorMessage = "0_is_required")]
    public string Goal { get; set; }

    [Display(Name = "version")]
    [Required(ErrorMessage = "0_is_required")]
    public int Version { get; set; }

    [Display(Name = "versionTime")] public DateTime? VersionTime { get; set; }

    [Display(Name = "in_charge")]
    [Required(ErrorMessage = "0_is_required")]
    [MinLength(1)]
    public Models.DomainClasses.App.OperationProcedure.OperationProcedureInCharge[] InCharge { get; set; }

    [Display(Name = "duration")] public int Duration { get; set; }

    [Display(Name = "duration_type")]
    [RegularExpression(
        "(?:"
        + OperationProcedureModel.DurationTypeSecond + "|"
        + OperationProcedureModel.DurationTypeMinute + "|"
        + OperationProcedureModel.DurationTypeHour + "|"
        + OperationProcedureModel.DurationTypeDay + "|"
        + OperationProcedureModel.DurationTypeMonth + ")",
        ErrorMessage = "0_is_invalid")
    ]
    public string DurationType { get; set; }

    [Display(Name = "is_duration_calculated_from_steps")]
    public bool IsDurationCalculatedFromSteps { get; set; }

    [Display(Name = "periodicity")]
    [RegularExpression(
        "(?:"
        + OperationProcedureModel.PeriodicityDaily + "|"
        + OperationProcedureModel.PeriodicityWeekly + "|"
        + OperationProcedureModel.PeriodicityMonthly + "|"
        + OperationProcedureModel.PeriodicityQuarterly + "|"
        + OperationProcedureModel.PeriodicitySemiAnnually + "|"
        + OperationProcedureModel.PeriodicityAnnually + ")",
        ErrorMessage = "0_is_invalid")
    ]
    public string Periodicity { get; set; }

    [Display(Name = "type")]
    [RegularExpression(
        "^(?:" +
        Models.DomainClasses.App.OperationProcedure.TypeManual + "|" +
        Models.DomainClasses.App.OperationProcedure.TypeElectronic +
        ")$",
        ErrorMessage = "0_is_invalid")]
    public string Type { get; set; }

    [Display(Name = "risk")] public string Risk { get; set; }

    [Display(Name = "flowchart_fie")] public LibraryFileSimpleDto FlowchartFile { get; set; }

    [Display(Name = "steps")] public IEnumerable<OperationProcedureStepCreateDto> Steps { get; set; }

    [Display(Name = "procedures")] public IEnumerable<OperationProcedureWithCodeDto> Procedures { get; set; }
}
