using System;
using LinqKit;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Models.DomainClasses.App;


namespace Injaz.Core.Dtos.OperationProcedure;

public class OperationProcedureStepCreateDto
{
    public static Expression<Func<OperationProcedureStep, OperationProcedureStepCreateDto>>
        Mapper(string lang)
    {
        var fileExpression = LibraryFileSimpleDto.Mapper(lang);

        return item => new OperationProcedureStepCreateDto
        {
            Order = item.Order,
            Title = item.Title,
            Responsibility = item.Responsibility,
            Notes = item.Notes,
            UsedModelFile = item.UsedModelFileId == null ? null : fileExpression.Invoke(item.UsedModelFile),
            Duration = item.Duration
        };
    }

    [Display(Name = "order")] public int? Order { get; set; }

    [Display(Name = "title")] public string Title { get; set; }

    [Display(Name = "responsibility")] public string Responsibility { get; set; }

    [Display(Name = "notes")] public string Notes { get; set; }

    [Display(Name = "duration")] public int Duration { get; set; }

    [Display(Name = "used_model_fie")] public LibraryFileSimpleDto UsedModelFile { get; set; }
}
