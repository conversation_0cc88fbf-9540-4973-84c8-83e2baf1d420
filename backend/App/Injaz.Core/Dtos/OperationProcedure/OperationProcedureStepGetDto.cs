using System;
using System.Linq.Expressions;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Models.DomainClasses.App;
using LinqKit;

namespace Injaz.Core.Dtos.OperationProcedure;

public class OperationProcedureStepGetDto
{
    public static Expression<Func<OperationProcedureStep, OperationProcedureStepGetDto>>
        Mapper(string lang)
    {
        var fileExpression = LibraryFileWithLinkAndFileDto.Mapper(lang);

        return item => new OperationProcedureStepGetDto
        {
            Id = item.Id,
            Order = item.Order,
            Title = item.Title,
            Responsibility = item.Responsibility,
            Notes = item.Notes,
            UsedModelFile = item.UsedModelFileId == null ? null : fileExpression.Invoke(item.UsedModelFile),
            Duration = item.Duration
        };
    }

    public Guid Id { get; set; }
    public int? Order { get; set; }
    public string Title { get; set; }
    public string Responsibility { get; set; }
    public string Notes { get; set; }
    public int Duration { get; set; }
    public LibraryFileWithLinkAndFileDto UsedModelFile { get; set; }
}
