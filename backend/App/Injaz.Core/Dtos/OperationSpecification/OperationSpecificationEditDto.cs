using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.OperationSpecification;

public class OperationSpecificationEditDto : OperationSpecificationCreateDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.OperationSpecification, OperationSpecificationEditDto>> Mapper() => item => new OperationSpecificationEditDto
    {
        Id = item.Id,
        NameAr = item.NameAr,
        NameEn = item.NameEn,
    };

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}
