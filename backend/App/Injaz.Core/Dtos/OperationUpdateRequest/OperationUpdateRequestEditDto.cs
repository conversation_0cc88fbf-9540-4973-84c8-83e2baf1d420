using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;

namespace Injaz.Core.Dtos.OperationUpdateRequest;

public class OperationUpdateRequestEditDto : OperationUpdateRequestCreateDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.OperationUpdateRequest, OperationUpdateRequestEditDto>>
        Mapper(string lang)
    {
        var itemsExpression = OperationUpdateRequestItemDto.Mapper(lang);

        return item => new OperationUpdateRequestEditDto
        {
            Id = item.Id,
            Items = item.Items.Select(x => itemsExpression.Invoke(x)).ToList()
        };
    }

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
}
