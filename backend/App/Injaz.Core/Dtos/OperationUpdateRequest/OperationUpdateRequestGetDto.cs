using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text.Json.Nodes;
using LinqKit;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.User;

namespace Injaz.Core.Dtos.OperationUpdateRequest;

public class OperationUpdateRequestGetDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.OperationUpdateRequest, OperationUpdateRequestGetDto>>
        Mapper(string lang,
            Guid? userId = null,
            Expression<Func<Core.Models.DomainClasses.App.OperationUpdateRequest, Guid, object>>
                flowActionAvailabilityExpression = null)
    {
        var operationExpression = OperationSimpleDto.Mapper(lang);
        var itemsExpression = OperationUpdateRequestItemDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);

        flowActionAvailabilityExpression = flowActionAvailabilityExpression == null || userId == null
            ? (r, u) => new { }
            : flowActionAvailabilityExpression;
        userId ??= Guid.Empty;

        return item => new OperationUpdateRequestGetDto
        {
            Id = item.Id,
            CreationTime = item.CreationTime,
            CreatedBy = userExpression.Invoke(item.CreatedBy),
            OldData = item.OldData,
            NewData = item.NewData,
            FlowState = item.FlowState,
            Operation = operationExpression.Invoke(item.Operation),
            Items = item.Items.Select(x => itemsExpression.Invoke(x)).ToList(),
            FlowActionAvailability = flowActionAvailabilityExpression.Invoke(item, userId.Value),
        };
    }

    public Guid Id { get; set; }

    public DateTime CreationTime { get; set; }

    public UserSimpleDto CreatedBy { get; set; }

    public JsonNode OldData { get; set; }

    public JsonNode NewData { get; set; }

    public string FlowState { get; set; }

    public OperationSimpleDto Operation { get; set; }

    public IEnumerable<OperationUpdateRequestItemDto> Items { get; set; }

    public object FlowActionAvailability { get; set; }
}
