using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.User;

namespace Injaz.Core.Dtos.OperationUpdateRequest;

public class OperationUpdateRequestListDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.OperationUpdateRequest, OperationUpdateRequestListDto>>
        Mapper(string lang)
    {
        var operationExpression = OperationSimpleDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);

        return item => new OperationUpdateRequestListDto
        {
            Id = item.Id,
            CreationTime = item.CreationTime,
            CreatedBy = userExpression.Invoke(item.CreatedBy),
            FlowState = item.FlowState,
            Operation = operationExpression.Invoke(item.Operation),
        };
    }

    public Guid Id { get; set; }

    public DateTime CreationTime { get; set; }

    public UserSimpleDto CreatedBy { get; set; }

    public string FlowState { get; set; }

    public OperationSimpleDto Operation { get; set; }
}
