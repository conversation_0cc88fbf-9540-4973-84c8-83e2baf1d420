using System;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.User;
using PartnerModel = Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner;

namespace Injaz.Core.Dtos.Partner;

public class PartnerListDto
{
    public static Expression<Func<PartnerModel, PartnerListDto>> Mapper(string lang)
    {
        var userExpression = UserSimpleDto.Mapper(lang);
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);

        return item => new PartnerListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
            PartnershipContractCount = item.PartnershipContracts.Count,
            TotalEvaluationValue = item.PartnershipContracts
                .SelectMany(pc => pc.PartnershipPartnerEvaluations)
                .Sum(e => e.Value),

            TotalEvaluationTarget = item.PartnershipContracts
                .SelectMany(pc => pc.PartnershipPartnerEvaluations)
                .Sum(e => e.Target),
            CreatedByUser = item.CreatedById != null ? userExpression.Invoke(item.CreatedBy) : null,
            CreationTime = item.CreationTime,
            CreatedByUserDepartment = item.CreatedBy.DepartmentLinks
                .Select(x => departmentExpression.Invoke(x.Department))
                .FirstOrDefault()

        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public int PartnershipContractCount { get; set; }

    public UserSimpleDto CreatedByUser { get; set; }

    public DateTime CreationTime { get; set; }

    public double? TotalEvaluationPercentage => TotalEvaluationTarget is > 0
        ? (TotalEvaluationValue ?? 0) / TotalEvaluationTarget * 100
        : null;

    private double? TotalEvaluationValue { get; set; }

    private double? TotalEvaluationTarget { get; set; }

    public DepartmentSimpleDto CreatedByUserDepartment { get; set; }
}
