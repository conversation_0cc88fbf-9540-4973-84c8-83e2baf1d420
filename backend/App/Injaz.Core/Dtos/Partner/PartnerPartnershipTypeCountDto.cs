using System;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using LinqKit;

namespace Injaz.Core.Dtos.Partner;

public class PartnerPartnershipTypeCountDto
{
    public static Expression<Func<PartnershipType, Guid, PartnerPartnershipTypeCountDto>> Mapper(string lang)
    {
        var expression = PartnershipTypeGetDto.Mapper(lang);

        return (item, partnerId) => new PartnerPartnershipTypeCountDto
        {
            PartnershipType = expression.Invoke(item),
            Count = item.PartnershipContracts.Count(x => x.PartnerId == partnerId)
        };
    }

    public PartnershipTypeGetDto PartnershipType { get; set; }

    public int Count { get; set; }
}
