using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using PartnerModel = Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner;

namespace Injaz.Core.Dtos.Partner;

public class PartnerSimpleDto
{
    public static Expression<Func<PartnerModel, PartnerSimpleDto>> Mapper(string lang)
    {
        return item => new PartnerSimpleDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
