using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.Partner;

public class PartnershipPartnerEvaluationListDto
{
    public static Expression<Func<
        Models.DomainClasses.App.PartnershipModel.PartnershipPartnerEvaluation,
        PartnershipPartnerEvaluationListDto>> Mapper(string lang)
    {
        return item => new PartnershipPartnerEvaluationListDto
        {
            Id = item.Id,
            Label = item.Label,
            Target = item.Target,
            Value = item.Value,
        };
    }

    public Guid Id { get; set; }
    public string Label { get; set; }
    public double Target { get; set; }
    public double? Value { get; set; }
}
