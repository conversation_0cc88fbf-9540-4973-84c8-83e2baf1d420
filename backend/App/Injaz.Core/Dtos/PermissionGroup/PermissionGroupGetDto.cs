using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.PermissionGroup;

public class PermissionGroupGetDto
{
    public static Expression<Func<Core.Models.DomainClasses.Permission.PermissionGroup, PermissionGroupGetDto>> Mapper(
        string lang)
    {
        return item => new PermissionGroupGetDto
        {
            Id = item.Id,
            Name = lang.Equals(SupportedCultures.LanguageEnglish) ? item.NameEn : item.NameAr,
            Description = lang.Equals(SupportedCultures.LanguageEnglish) ? item.DescriptionEn : item.DescriptionAr,
            PermissionList = item.PermissionLinks.Select(x => x.PermissionId).ToList()
        };
    }


    public Guid Id { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public IEnumerable<string> PermissionList { get; set; }
}
