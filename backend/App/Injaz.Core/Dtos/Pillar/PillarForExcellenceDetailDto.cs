using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Standard;
using Injaz.Core.Dtos.Tournament;

namespace Injaz.Core.Dtos.Pillar;

public class PillarForExcellenceDetailDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Pillar, PillarForExcellenceDetailDto>> Mapper(
        string lang,
        IQueryable<Core.Models.DomainClasses.App.Kpi> kpis,
        IQueryable<Core.Models.DomainClasses.App.Capability> capabilities,
        IQueryable<Core.Models.DomainClasses.App.LibraryFile> libraryFiles
    )
    {
        var tournamentExpression = TournamentSimpleDto.Mapper(lang);
        var standardExpression = StandardForExcellenceDto.Mapper(lang, libraryFiles);

        var kpisExpression = HelperExpression.PillarKpis(kpis);
        var capabilityExpression = HelperExpression.PillarCapabilities(capabilities);
        var filesExpression = HelperExpression.PillarLibraryFiles(libraryFiles);

        return item => new PillarForExcellenceDetailDto
        {
            Id = item.Id,
            Name = SupportedCultures.LanguageArabic == lang ? item.NameAr : item.NameEn,
            Weight = item.Weight,
            KpiCount = kpisExpression.Invoke(item).Count(),
            CapabilityCount = capabilityExpression.Invoke(item).Count(),
            LibraryFileCount = filesExpression.Invoke(item).Count(),
            Achieved = 0, // CustomDbFunctions.ObtainPillarAchieved(item.Id, null),
            Tournament = tournamentExpression.Invoke(item.Tournament),
            Standards = item
                .Standards
                .Select(x => standardExpression.Invoke(x))
                .ToList()
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public double Weight { get; set; }

    public int KpiCount { get; set; }

    public int CapabilityCount { get; set; }

    public int LibraryFileCount { get; set; }

    public double? Achieved { get; set; }

    public TournamentSimpleDto Tournament { get; set; }

    public IEnumerable<StandardForExcellenceDto> Standards { get; set; }
}
