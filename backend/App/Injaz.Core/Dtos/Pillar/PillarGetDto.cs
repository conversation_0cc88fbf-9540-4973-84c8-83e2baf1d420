using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Standard;
using Injaz.Core.Dtos.Tournament;

namespace Injaz.Core.Dtos.Pillar;

public class PillarGetDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Pillar, PillarGetDto>>
        Mapper(
            string lang,
            Guid userId,
            bool hasTournamentPermission
        )
    {
        var tournamentExpression = TournamentSimpleDto.Mapper(lang);
        var standardExpression = StandardGetDto.Mapper(
            lang,
            userId,
            hasTournamentPermission
        );

        return item => new PillarGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Order = item.Order,
            Weight = item.Weight,
            Tournament = tournamentExpression.Invoke(item.Tournament),
            Standards = item.Standards
                .Select(x => standardExpression.Invoke(x))
                .OrderBy(x => x.Order)
                .ToList()
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public int Order { get; set; }
    public double Weight { get; set; }
    public TournamentSimpleDto Tournament { get; set; }
    public IEnumerable<StandardGetDto> Standards { get; set; }
}
