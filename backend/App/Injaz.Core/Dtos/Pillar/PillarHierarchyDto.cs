using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Tournament;

namespace Injaz.Core.Dtos.Pillar;

public class PillarHierarchyDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Pillar, PillarHierarchyDto>>
        Mapper(string lang)
    {
        var expression = TournamentSimpleDto.Mapper(lang);

        return item => new PillarHierarchyDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Tournament = expression.Invoke(item.Tournament)
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public TournamentSimpleDto Tournament { get; set; }
}
