using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Pillar;

public class PillarSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Pillar, PillarSimpleDto>>
        Mapper(string lang) => item => new PillarSimpleDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
