using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.PlanDtos.PlanCategories;

public class PlanCategoryEditDto
{
    public static Expression<Func<Models.DomainClasses.App.PlanCategory, PlanCategoryEditDto>> Mapper() =>
        item => new PlanCategoryEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            DescriptionAr = item.DescriptionAr,
            DescriptionEn = item.DescriptionEn,
            Type = item.Type,
        };

    public Guid Id { get; set; }
    public string NameAr { get; set; }
    public string NameEn { get; set; }
    public string DescriptionAr { get; set; }
    public string DescriptionEn { get; set; }
    public string Type { get; set; }
}
