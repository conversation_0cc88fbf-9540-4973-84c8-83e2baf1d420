using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.PlanDtos.PlanCategories;

public class PlanCategoryGetDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PlanCategory, PlanCategoryGetDto>>
        Mapper(string lang) => item => new PlanCategoryGetDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        Description = lang == SupportedCultures.LanguageArabic ? item.DescriptionAr : item.DescriptionEn,
        Type = item.Type,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Type { get; set; }
}
