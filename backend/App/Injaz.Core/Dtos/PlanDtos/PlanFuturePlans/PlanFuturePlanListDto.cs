using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.PlanDtos.PlanFuturePlans;

public class PlanFuturePlanListDto
{
    public static Expression<Func<Models.DomainClasses.App.PlanFuturePlan, PlanFuturePlanListDto>> Mapper(string lang)
    {
        return item => new PlanFuturePlanListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }
}
