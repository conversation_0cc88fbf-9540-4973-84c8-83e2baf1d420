using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.PlanDtos.PlanInputs;

public class PlanInputEditDto
{
    public static Expression<Func<Models.DomainClasses.App.PlanInput, PlanInputEditDto>> Mapper() => item =>
        new PlanInputEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
        };

    public Guid Id { get; set; }
    public string NameAr { get; set; }
    public string NameEn { get; set; }
}
