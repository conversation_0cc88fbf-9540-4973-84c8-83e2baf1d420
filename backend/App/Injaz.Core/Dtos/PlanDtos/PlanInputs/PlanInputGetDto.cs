using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.PlanDtos.PlanInputs;

public class PlanInputGetDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PlanInput, PlanInputGetDto>> Mapper(string lang) => item => new PlanInputGetDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
