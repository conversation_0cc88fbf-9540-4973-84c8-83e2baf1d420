using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.PlanDtos.Plans;
using Injaz.Core.Dtos.PlanDtos.PlanSubtasks;
using Injaz.Core.Dtos.PlanDtos.PlanTasks;

namespace Injaz.Core.Dtos.PlanDtos.PlanSubsubtasks;

public class PlanSubsubtaskDto
{
    public static Expression<Func<Models.DomainClasses.App.PlanSubsubtask, PlanSubsubtaskDto>> Mapper(
        string lang,
        Guid userId,
        IQueryable<Injaz.Core.Models.DomainClasses.App.Department> departments
    )
    {
        var weightExpression = HelperExpression.PlanSubsubtaskWeight();
        var statusExpression = HelperExpression.PlanSubsubtaskStatus();
        var approvalExpression = PlanSubsubtaskApprovalDto.Mapper(lang);

        var planExpression = PlanSimpleDto.Mapper(lang);
        var taskExpression = PlanTaskSimpleDto.Mapper(lang);
        var subtaskExpression = PlanSubtaskSimpleDto.Mapper(lang);

        var canSubmitPredicate = HelperExpression.CanUserSubmitPlanSubsubtask(departments);
        var canRejectPredicate = HelperExpression.CanUserRejectPlanSubsubtask(departments);
        var canApprovePredicate = HelperExpression.CanUserApprovePlanSubsubtask();
        var canFinalizePredicate = HelperExpression.CanUserFinalizePlanSubsubtask(departments);

        return item => new PlanSubsubtaskDto
        {
            Id = item.Id,
            From = item.From,
            To = item.To,
            Weight = weightExpression.Invoke(item),
            Status = statusExpression.Invoke(item),
            NotPerformingReason = item.NotPerformingReason,
            Alternative = item.Alternative,

            CanSubmit = canSubmitPredicate.Invoke(item, userId),
            CanReject = canRejectPredicate.Invoke(item, userId),
            CanApprove = canApprovePredicate.Invoke(item, userId),
            CanFinalize = canFinalizePredicate.Invoke(item, userId),

            Plan = planExpression.Invoke(item.Subtask.Task.Plan),
            Task = taskExpression.Invoke(item.Subtask.Task),
            Subtask = subtaskExpression.Invoke(item.Subtask),

            Approvals = item.Approvals.OrderByDescending(x => x.CreationTime)
                .Select(x => approvalExpression.Invoke(x))
                .ToList(),

            AttachmentCount = item.LibraryFileLinks.Count
        };
    }

    public Guid Id { get; set; }
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public double? Weight { get; set; }
    public string Status { get; set; }

    public string NotPerformingReason { get; set; }

    public string Alternative { get; set; }

    public bool CanSubmit { get; set; }
    public bool CanReject { get; set; }
    public bool CanApprove { get; set; }
    public bool CanFinalize { get; set; }

    public PlanSimpleDto Plan { get; set; }

    public PlanTaskSimpleDto Task { get; set; }

    public PlanSubtaskSimpleDto Subtask { get; set; }

    public IEnumerable<PlanSubsubtaskApprovalDto> Approvals { get; set; }

    public int AttachmentCount { get; set; }
}
