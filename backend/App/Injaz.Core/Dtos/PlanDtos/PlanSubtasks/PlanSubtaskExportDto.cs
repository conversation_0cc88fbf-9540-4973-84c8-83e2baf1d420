using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.PlanDtos.PlanSubsubtasks;
using Injaz.Core.Dtos.Team;
using Injaz.Core.Dtos.User;

namespace Injaz.Core.Dtos.PlanDtos.PlanSubtasks;

public class PlanSubtaskExportDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PlanSubtask, PlanSubtaskExportDto>> Mapper(
        string lang
    )
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var teamExpression = TeamSimpleDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);
        var weightExpression = HelperExpression.PlanSubtaskWeight();
        var subsubtaskExpression = PlanSubsubtaskExportDto.Mapper(lang);

        return item => new PlanSubtaskExportDto
        {
            Id = item.Id,
            Name = SupportedCultures.LanguageArabic.Equals(lang) ? item.NameAr : item.NameEn,
            From = item.From,
            To = item.To,
            AssignedDepartment = item.AssignedDepartmentId == null
                ? null
                : departmentExpression.Invoke(item.AssignedDepartment),
            AssignedTeam = item.AssignedTeamId == null ? null : teamExpression.Invoke(item.AssignedTeam),
            AssignedUser = item.AssignedUserId == null ? null : userExpression.Invoke(item.AssignedUser),
            Weight = weightExpression.Invoke(item),
            Subsubtasks = item.Subsubtasks.Select(x => subsubtaskExpression.Invoke(x)).ToList()
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public DateTime? From { get; set; }

    public DateTime? To { get; set; }


    public DepartmentSimpleDto AssignedDepartment { get; set; }

    public TeamSimpleDto AssignedTeam { get; set; }

    public UserSimpleDto AssignedUser { get; set; }

    public double? Weight { get; set; }

    public IEnumerable<PlanSubsubtaskExportDto> Subsubtasks { get; set; }
}
