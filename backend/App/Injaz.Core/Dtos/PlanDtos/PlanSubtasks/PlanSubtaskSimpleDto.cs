using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.PlanDtos.PlanSubtasks;

public class PlanSubtaskSimpleDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PlanSubtask, PlanSubtaskSimpleDto>> Mapper(string lang)
    {
        return item => new PlanSubtaskSimpleDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
