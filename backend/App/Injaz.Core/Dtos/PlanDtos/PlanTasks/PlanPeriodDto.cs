using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.PlanDtos.Plans;
using Injaz.Core.Dtos.Team;
using Injaz.Core.Dtos.User;

namespace Injaz.Core.Dtos.PlanDtos.PlanTasks;

public class PlanTaskPeriodWithAssignedDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PlanTask, PlanTaskPeriodWithAssignedDto>> Mapper(
        string lang)
    {
        var planExpression = PlanSimpleDto.Mapper(lang);
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var teamExpression = TeamSimpleDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);

        return item => new PlanTaskPeriodWithAssignedDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            From = item.From,
            To = item.To,
            Plan = planExpression.Invoke(item.Plan),
            AssignedDepartment = item.AssignedDepartment == null
                ? null
                : departmentExpression.Invoke(item.AssignedDepartment),
            AssignedTeam = item.AssignedTeam == null ? null : teamExpression.Invoke(item.AssignedTeam),
            AssignedUser = item.AssignedUser == null ? null : userExpression.Invoke(item.AssignedUser),
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public PlanSimpleDto Plan { get; set; }
    public DepartmentSimpleDto AssignedDepartment { get; set; }
    public TeamSimpleDto AssignedTeam { get; set; }
    public UserSimpleDto AssignedUser { get; set; }
}
