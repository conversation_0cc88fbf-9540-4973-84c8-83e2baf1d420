using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.PlanDtos.Plans;
using Injaz.Core.Dtos.Team;
using Injaz.Core.Dtos.User;
using Injaz.Core.Flow.Services;

namespace Injaz.Core.Dtos.PlanDtos.PlanTasks;

public class PlanTaskListDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PlanTask, PlanTaskListDto>> Mapper(
        string lang,
        Expression<Func<Injaz.Core.Models.DomainClasses.App.Plan, PlanUserAbility>> abilityExpression
    )
    {
        var planExpression = PlanSimpleDto.Mapper(lang);
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var teamExpression = TeamSimpleDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);
        var isApprovedExpression = HelperExpression.IsPlanTaskApproved();
        var progressExpression = HelperExpression.PlanTaskProgress();

        return item => new PlanTaskListDto
        {
            Id = item.Id,
            Name = SupportedCultures.LanguageArabic.Equals(lang) ? item.NameAr : item.NameEn,
            Weight = item.Weight,
            From = item.From,
            To = item.To,
            Plan = planExpression.Invoke(item.Plan),
            AssignedDepartment = item.AssignedDepartment == null
                ? null
                : departmentExpression.Invoke(item.AssignedDepartment),
            AssignedTeam = item.AssignedTeam == null ? null : teamExpression.Invoke(item.AssignedTeam),
            AssignedUser = item.AssignedUser == null ? null : userExpression.Invoke(item.AssignedUser),
            IsApproved = isApprovedExpression.Invoke(item),
            Progress = progressExpression.Invoke(item),
            IsPlanInitiallyApproved = item.Plan.FlowState == DefaultFlowState.ApprovedFinal,
            PlanUserAbility = abilityExpression.Invoke(item.Plan),
            SubtasksCount = item.Subtasks.Count
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public double Weight { get; set; }

    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public PlanSimpleDto Plan { get; set; }

    public DepartmentSimpleDto AssignedDepartment { get; set; }

    public TeamSimpleDto AssignedTeam { get; set; }

    public UserSimpleDto AssignedUser { get; set; }

    public bool IsApproved { get; set; }

    public double? Progress { get; set; }

    public int SubtasksCount { get; set; }

    public bool IsPlanInitiallyApproved { get; set; }

    public PlanUserAbility PlanUserAbility { get; set; }
}
