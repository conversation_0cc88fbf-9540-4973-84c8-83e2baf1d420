using System.ComponentModel.DataAnnotations;

namespace Injaz.Core.Dtos.PlanDtos.Plans;

public class PlanCommunicationProcessDto
{
    [Display(Name = "activity")] public string Activity { get; set; }

    [Display(Name = "from")] public string From { get; set; }

    [Display(Name = "to")] public string To { get; set; }

    [Display(Name = "communication_method")]
    public string CommunicationMethod { get; set; }

    [Display(Name = "communication_method_repetition")]
    public string CommunicationMethodRepetition { get; set; }

    [Display(Name = "communication_goal")] public string CommunicationGoal { get; set; }

    [Display(Name = "importance_level")] public string ImportanceLevel { get; set; }
}
