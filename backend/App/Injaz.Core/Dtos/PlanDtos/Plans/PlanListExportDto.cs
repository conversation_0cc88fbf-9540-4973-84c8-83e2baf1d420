using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.GovernmentStrategicGoal;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.MinistryStrategicGoal;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.Partner;
using Injaz.Core.Dtos.PlanDtos.PlanCategories;
using Injaz.Core.Dtos.StrategicGoal;
using Injaz.Core.Dtos.Team;
using Injaz.Core.Dtos.User;

namespace Injaz.Core.Dtos.PlanDtos.Plans;

public class PlanListExportDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.Plan, PlanListExportDto>> Mapper(string lang)
    {
        var strategicGoalsExpression = StrategicGoalSimpleDto.Mapper(lang);
        var ministryStrategicGoalsExpression = MinistryStrategicGoalSimpleDto.Mapper(lang);
        var kpiExpression = KpiWithCodeAndTypeDto.Mapper(lang);
        var partnerExpression = PartnerSimpleDto.Mapper(lang);
        var operationExpression = OperationWithCodeDto.Mapper(lang);
        var categoryExpression = PlanCategorySimpleDto.Mapper(lang);
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var teamExpression = TeamSimpleDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);
        var governmentStrategicGoalExpression = GovernmentStrategicGoalSimpleDto.Mapper(lang);
        var progressExpression = HelperExpression.PlanProgress();

        return item => new PlanListExportDto
        {
            Id = item.Id,
            Code = item.Code,
            Year = item.Year,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Description = item.Description,
            StrategicGoals = item.StrategicGoalLinks
                .OrderBy(x => x.Goal.Order)
                .Select(x => strategicGoalsExpression.Invoke(x.Goal))
                .ToList(),
            MinistryStrategicGoals = item.MinistryStrategicGoalLinks
                .Select(x => ministryStrategicGoalsExpression.Invoke(x.MinistryStrategicGoal))
                .ToList(),
            Kpis = item.KpiLinks.Select(x => kpiExpression.Invoke(x.Kpi)).ToList(),
            GovernmentStrategicGoal = item.GovernmentStrategicGoalId == null
                ? null
                : governmentStrategicGoalExpression.Invoke(item.GovernmentStrategicGoal),
            Partners = item.PartnerLinks.Select(x => partnerExpression.Invoke(x.Partner)).ToList(),
            Operations = item.OperationLinks.Select(x => operationExpression.Invoke(x.Operation)).ToList(),
            TasksCount = item.Tasks.Count(),
            SubTasksCount = item.Tasks.SelectMany(x => x.Subtasks).Count(),
            Category = categoryExpression.Invoke(item.Category),
            From = item.From,
            To = item.To,
            AssignedDepartment = item.AssignedDepartmentId == null
                ? null
                : departmentExpression.Invoke(item.AssignedDepartment),
            AssignedTeam = item.AssignedTeamId == null ? null : teamExpression.Invoke(item.AssignedTeam),
            AssignedUser = item.AssignedUserId == null ? null : userExpression.Invoke(item.AssignedUser),
            FlowState = item.FlowState,
            Progress = progressExpression.Invoke(item) ?? 0,
        };
    }

    public Guid Id { get; set; }
    public string Code { get; set; }
    public int Year { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }

    public IEnumerable<StrategicGoalSimpleDto> StrategicGoals { get; set; }
    public IEnumerable<MinistryStrategicGoalSimpleDto> MinistryStrategicGoals { get; set; }
    public IEnumerable<KpiWithCodeAndTypeDto> Kpis { get; set; }
    public GovernmentStrategicGoalSimpleDto GovernmentStrategicGoal { get; set; }
    public IEnumerable<PartnerSimpleDto> Partners { get; set; }
    public IEnumerable<OperationWithCodeDto> Operations { get; set; }
    public int TasksCount { get; set; }
    public int SubTasksCount { get; set; }
    public PlanCategorySimpleDto Category { get; set; }
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public DepartmentSimpleDto AssignedDepartment { get; set; }
    public TeamSimpleDto AssignedTeam { get; set; }
    public UserSimpleDto AssignedUser { get; set; }
    public string FlowState { get; set; }
    public double Progress { get; set; }
}
