using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.PlanDtos.Plans;

public class PlanResourceDto
{
    public static Expression<Func<PlanResource, PlanResourceDto>> Mapper()
    {
        return item => new PlanResourceDto
        {
            Name = item.Name,
            Count = item.Count
        };
    }


    [Display(Name = "name")]
    [Required(ErrorMessage = "0_is_required")]
    public string Name { get; set; }

    [Display(Name = "count")]
    [Required(ErrorMessage = "0_is_required")]
    public int Count { get; set; }
}
