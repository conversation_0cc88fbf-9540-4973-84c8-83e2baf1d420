using System.ComponentModel.DataAnnotations;
using Injaz.Core.Models.Misc;

namespace Injaz.Core.Dtos.PlanDtos.Plans;

public class PlanRiskDto
{
    [Display(Name = "description")] public string Description { get; set; }

    [Display(Name = "occurring_probability")]
    [RegularExpression("^(?:" +
                       PlanRisk.OccurringProbabilityHigh + "|" +
                       PlanRisk.OccurringProbabilityMedium + "|" +
                       PlanRisk.OccurringProbabilityLow + ")$",
        ErrorMessage = "0_is_invalid")]
    public string OccurringProbability { get; set; }

    [Display(Name = "impact")]
    [RegularExpression("^(?:" +
                       PlanRisk.ImpactHigh + "|" +
                       PlanRisk.ImpactMedium + "|" +
                       PlanRisk.ImpactLow + ")$",
        ErrorMessage = "0_is_invalid")]
    public string Impact { get; set; }

    [Display(Name = "responsible")] public string Responsible { get; set; }
    [Display(Name = "actions")] public string Actions { get; set; }
}
