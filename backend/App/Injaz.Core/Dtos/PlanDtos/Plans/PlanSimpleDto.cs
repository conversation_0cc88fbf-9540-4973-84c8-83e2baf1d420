using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.PlanDtos.Plans;

public class PlanSimpleDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.Plan, PlanSimpleDto>> Mapper(string lang)
    {
        return item => new PlanSimpleDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
