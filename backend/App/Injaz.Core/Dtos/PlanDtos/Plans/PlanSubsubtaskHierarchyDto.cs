using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.PlanDtos.Plans;

public class PlanSubsubtaskHierarchyDto
{
    public static Expression<Func<Models.DomainClasses.App.PlanSubsubtask, PlanSubsubtaskHierarchyDto>> Mapper()
    {
        var now = DateTime.UtcNow;
        return item => new PlanSubsubtaskHierarchyDto
        {
            Id = item.Id,
            From = item.From,
            To = item.To,
            Progress = item.Progress,
            ExpectedProgress = item.To < now ? 1 : 0
        };
    }

    public Guid Id { get; set; }
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public double? Progress { get; set; }
    public double? ExpectedProgress { get; set; }
}
