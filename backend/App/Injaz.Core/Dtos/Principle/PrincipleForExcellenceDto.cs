using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Principle;

public class PrincipleForExcellenceDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Principle, PrincipleForExcellenceDto>> Mapper(
        string lang
    )
    {
        return item => new PrincipleForExcellenceDto
        {
            Id = item.Id,
            Name = SupportedCultures.LanguageArabic == lang ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public int KpiCount { get; set; }

    public int CapabilityCount { get; set; }

    public int LibraryFileCount { get; set; }

    public double? Achieved { get; set; }
}
