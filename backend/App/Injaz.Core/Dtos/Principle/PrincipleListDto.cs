using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Principle;

public class PrincipleListDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Principle, PrincipleListDto>>
        Mapper(string lang)
    {
        return item => new PrincipleListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Description = lang == SupportedCultures.LanguageArabic ? item.DescriptionAr : item.DescriptionEn,
            Order = item.Order,
            Weight = item.Weight,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public int Order { get; set; }
    public double Weight { get; set; }
}
