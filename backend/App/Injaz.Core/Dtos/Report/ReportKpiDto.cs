using System;
using System.Collections.Generic;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.KpiBalancedBehaviorCard;
using Injaz.Core.Dtos.KpiResult;
using Injaz.Core.Dtos.KpiType;
using Injaz.Core.Dtos.Operation;

namespace Injaz.Core.Dtos.Report;

public class ReportKpiDto
{
    public Guid Id { get; set; }
    public string Code { get; set; }
    public KpiTypeWithCodeDto Type { get; set; }
    public string Name { get; set; }
    public string Units { get; set; }
    public string Formula { get; set; }
    public string FormulaDescriptionA { get; set; }
    public string FormulaDescriptionB { get; set; }
    public string Direction { get; set; }
    public string Source { get; set; }
    public int? CreationYear { get; set; }
    public string MeasurementCycle { get; set; }
    public bool IsTrend { get; set; }
    public bool IsSpecial { get; set; }
    public DepartmentSimpleDto OwningDepartment { get; set; }
    public DepartmentSimpleDto MeasuringDepartment { get; set; }
    public DepartmentSimpleDto Level1Department { get; set; }

    public DepartmentSimpleDto Level2Department { get; set; }
    public KpiBalancedBehaviorCardSimpleDto BalancedBehaviorCard { get; set; }
    public IEnumerable<OperationWithLevelDto> Operations { get; set; }
    public IEnumerable<KpiResultDetailDto> Results { get; set; }
}
