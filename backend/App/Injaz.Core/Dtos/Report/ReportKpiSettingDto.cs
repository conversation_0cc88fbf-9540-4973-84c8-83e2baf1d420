namespace Injaz.Core.Dtos.Report;

public class ReportKpiSettingColumnDto
{
    public bool Units { get; set; }
    public bool Formula { get; set; }
    public bool CreationYear { get; set; }
    public bool MeasurementCycle { get; set; }
    public bool BalancedBehaviorCard { get; set; }
    public bool MeasuringDepartment { get; set; }
    public bool FormulaDescriptionA { get; set; }
    public bool FormulaDescriptionB { get; set; }
    public bool Source { get; set; }
    public bool Direction { get; set; }
    public bool Type { get; set; }
    public bool IsTrend { get; set; }
    public bool IsSpecial { get; set; }
}

public class ReportKpiSettingDto
{
    public ReportKpiSettingColumnDto Columns { get; set; }
    public bool IsGrouped { get; set; }
}
