using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App.RiskModel;

namespace Injaz.Core.Dtos.RiskDtos.RiskAcceptanceLevelCategories;

public class RiskAcceptanceLevelCategoryGetDto
{
    public static Expression<Func<RiskAcceptanceLevelCategory, RiskAcceptanceLevelCategoryGetDto>> Mapper(string lang)
    {
        return item => new RiskAcceptanceLevelCategoryGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Description = lang == SupportedCultures.LanguageArabic ? item.DescriptionAr : item.DescriptionEn,
            Degree = item.Degree,
            Color = item.Color,
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public int Degree { get; set; }

    public string Color { get; set; }
}
