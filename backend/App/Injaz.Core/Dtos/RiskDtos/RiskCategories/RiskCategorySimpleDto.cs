using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App.RiskModel;

namespace Injaz.Core.Dtos.RiskDtos.RiskCategories;

public class RiskCategorySimpleDto
{
    public static Expression<Func<RiskCategory, RiskCategorySimpleDto>> Mapper(string lang)
    {
        return item => new RiskCategorySimpleDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
