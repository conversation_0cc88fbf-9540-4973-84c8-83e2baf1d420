using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.RiskDtos.Risks;
using Injaz.Core.Models.DomainClasses.App.RiskModel;

namespace Injaz.Core.Dtos.RiskDtos.RiskManagementProcedures;

public class RiskManagementProcedureDto
{
    public static Expression<Func<RiskManagementProcedure, RiskManagementProcedureDto>> Mapper(string lang)
    {
        var riskExpression = RiskSimpleDto.Mapper(lang);
        return item => new RiskManagementProcedureDto
        {
            Id = item.Id,
            Description = item.Description,
            Owner = item.Owner,
            ExpectedClosureTime = item.ExpectedClosureTime,
            ExecutionSteps = item.ExecutionSteps,
            Resources = item.Resources,
            Risk = riskExpression.Invoke(item.Risk),
        };
    }

    public Guid Id { get; set; }

    public string Description { get; set; }

    public string Owner { get; set; }

    public DateTime ExpectedClosureTime { get; set; }

    public string ExecutionSteps { get; set; }

    public string Resources { get; set; }

    public RiskSimpleDto Risk { get; set; }
}
