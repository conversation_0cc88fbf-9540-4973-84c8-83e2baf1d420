using System;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App.RiskModel;

namespace Injaz.Core.Dtos.RiskDtos.RiskManagementStrategies;

public class RiskManagementStrategyEditDto
{
    public static Expression<Func<RiskManagementStrategy, RiskManagementStrategyEditDto>> Mapper()
    {
        return item => new RiskManagementStrategyEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            HasRiskAcceptanceLevelCategory = item.HasRiskAcceptanceLevelCategory
        };
    }

    public Guid Id { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }

    public bool HasRiskAcceptanceLevelCategory { get; set; }
}
