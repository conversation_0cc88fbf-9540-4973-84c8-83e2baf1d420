using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App.RiskModel;

namespace Injaz.Core.Dtos.RiskDtos.RiskManagementStrategies;

public class RiskManagementStrategyGetDto
{
    public static Expression<Func<RiskManagementStrategy, RiskManagementStrategyGetDto>> Mapper(string lang)
    {
        return item => new RiskManagementStrategyGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            HasRiskAcceptanceLevelCategory = item.HasRiskAcceptanceLevelCategory
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public bool HasRiskAcceptanceLevelCategory { get; set; }
}
