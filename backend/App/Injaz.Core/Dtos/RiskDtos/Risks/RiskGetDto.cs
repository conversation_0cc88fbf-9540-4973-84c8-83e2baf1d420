using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.PlanDtos.Plans;
using Injaz.Core.Dtos.RiskDtos.RiskCategories;
using Injaz.Core.Dtos.RiskDtos.RiskImpacts;
using Injaz.Core.Dtos.RiskDtos.RiskManagementStrategies;
using Injaz.Core.Dtos.RiskDtos.RiskProbabilities;
using Injaz.Core.Dtos.StrategicGoal;
using Injaz.Core.Models.DomainClasses.App.RiskModel;
using LinqKit;
using Injaz.Core.Dtos.RiskDtos.RiskAcceptanceLevelCategories;
using Injaz.Core.Evaluate.Models;

namespace Injaz.Core.Dtos.RiskDtos.Risks;

public class RiskGetDto
{
    public static Expression<Func<Risk, RiskGetDto>> Mapper(
        string lang,
        Expression<Func<Risk, EvaluateActionAbility>> evaluateActionAbilityExpression)
    {
        var categoryExpression = RiskCategorySimpleDto.Mapper(lang);
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var managementStrategyExpression = RiskManagementStrategyListDto.Mapper(lang);
        var impactExpression = RiskImpactListDto.Mapper(lang);
        var probabilityExpression = RiskProbabilityListDto.Mapper(lang);
        var strategicGaolExpression = StrategicGoalSimpleDto.Mapper(lang);
        var planExpression = PlanSimpleDto.Mapper(lang);
        var operationExpression = OperationSimpleDto.Mapper(lang);
        var acceptanceLevelCategoryExpression = RiskAcceptanceLevelCategoryListDto.Mapper(lang);

        return item => new RiskGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Description = lang == SupportedCultures.LanguageArabic ? item.DescriptionAr : item.DescriptionEn,
            Order = item.Order,
            Code = item.Code,
            Causes = item.Causes,
            ImpactDetails = item.ImpactDetails,
            ProbabilityDetails = item.ProbabilityDetails,
            Owner = item.Owner,
            Type = item.Type,
            ManagementProcedureDetails = item.ManagementProcedureDetails,
            ManagementProcedureSteps = item.ManagementProcedureSteps,
            Category = categoryExpression.Invoke(item.Category),
            Department = departmentExpression.Invoke(item.Department),
            ManagementStrategy = managementStrategyExpression.Invoke(item.ManagementStrategy),
            Impact = impactExpression.Invoke(item.Impact),
            Probability = probabilityExpression.Invoke(item.Probability),
            EvaluateActionAbility = evaluateActionAbilityExpression.Invoke(item),
            Goals = item.GoalLinks.Select(x => strategicGaolExpression.Invoke(x.Goal)).ToList(),
            Plans = item.PlanLinks.Select(x => planExpression.Invoke(x.Plan)).ToList(),
            Operations = item.OperationLinks.Select(x => operationExpression.Invoke(x.Operation)).ToList(),
            AcceptanceLevelCategory = item.AcceptanceLevelCategory != null
                ? acceptanceLevelCategoryExpression.Invoke(item.AcceptanceLevelCategory)
                : null,
            ImpactAfterMitigation = item.ImpactAfterMitigation != null
                ? impactExpression.Invoke(item.ImpactAfterMitigation)
                : null,
            ProbabilityAfterMitigation = item.ProbabilityAfterMitigation != null
                ? probabilityExpression.Invoke(item.ProbabilityAfterMitigation)
                : null,
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public int Order { get; set; }

    public string Code { get; set; }

    public string Causes { get; set; }

    public string ImpactDetails { get; set; }

    public string ProbabilityDetails { get; set; }

    public string Owner { get; set; }

    public string Type { get; set; }

    public string ManagementProcedureDetails { get; set; }

    public string ManagementProcedureSteps { get; set; }

    public RiskCategorySimpleDto Category { get; set; }

    public DepartmentSimpleDto Department { get; set; }

    public RiskManagementStrategyListDto ManagementStrategy { get; set; }

    public RiskImpactListDto Impact { get; set; }

    public RiskProbabilityListDto Probability { get; set; }

    public EvaluateActionAbility EvaluateActionAbility { get; set; }

    public RiskAcceptanceLevelCategoryListDto AcceptanceLevelCategory { get; set; }

    public RiskImpactListDto ImpactAfterMitigation { get; set; }

    public RiskProbabilityListDto ProbabilityAfterMitigation { get; set; }

    public IEnumerable<StrategicGoalSimpleDto> Goals { get; set; }

    public IEnumerable<PlanSimpleDto> Plans { get; set; }

    public IEnumerable<OperationSimpleDto> Operations { get; set; }
}
