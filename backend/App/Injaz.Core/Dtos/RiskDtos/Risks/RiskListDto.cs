using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.RiskDtos.RiskCategories;
using Injaz.Core.Dtos.RiskDtos.RiskImpacts;
using Injaz.Core.Dtos.RiskDtos.RiskManagementStrategies;
using Injaz.Core.Dtos.RiskDtos.RiskProbabilities;
using Injaz.Core.Evaluate.Models;
using Injaz.Core.Models.DomainClasses.App.RiskModel;

namespace Injaz.Core.Dtos.RiskDtos.Risks;

public class RiskListDto
{
    public static Expression<Func<Risk, RiskListDto>> Mapper(
        string lang,
        Expression<Func<Guid, EvaluationScoreDetail>> evaluationScoreExpression,
        Expression<Func<Risk, EvaluateActionAbility>> evaluateActionAbilityExpression)
    {
        var categoryExpression = RiskCategorySimpleDto.Mapper(lang);
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var managementStrategyExpression = RiskManagementStrategyListDto.Mapper(lang);
        var impactExpression = RiskImpactListDto.Mapper(lang);
        var probabilityExpression = RiskProbabilityListDto.Mapper(lang);

        return item => new RiskListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Order = item.Order,
            Code = item.Code,
            Owner = item.Owner,
            Type = item.Type,
            EvaluationScoreDetail = evaluationScoreExpression.Invoke(item.Id),
            EvaluateActionAbility = evaluateActionAbilityExpression.Invoke(item),
            Category = categoryExpression.Invoke(item.Category),
            Department = departmentExpression.Invoke(item.Department),
            ManagementStrategy = managementStrategyExpression.Invoke(item.ManagementStrategy),
            Impact = impactExpression.Invoke(item.Impact),
            Probability = probabilityExpression.Invoke(item.Probability),
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public int Order { get; set; }

    public string Code { get; set; }

    public string Owner { get; set; }

    public string Type { get; set; }
    public EvaluationScoreDetail EvaluationScoreDetail { get; set; }

    public EvaluateActionAbility EvaluateActionAbility { get; set; }

    public RiskCategorySimpleDto Category { get; set; }

    public DepartmentSimpleDto Department { get; set; }

    public RiskManagementStrategyListDto ManagementStrategy { get; set; }

    public RiskImpactListDto Impact { get; set; }

    public RiskProbabilityListDto Probability { get; set; }
}
