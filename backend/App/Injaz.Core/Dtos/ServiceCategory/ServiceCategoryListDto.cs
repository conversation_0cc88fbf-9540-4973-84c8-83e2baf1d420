using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.ServiceCategory;

public class ServiceCategoryListDto
{
    public static
        Expression<Func<Models.DomainClasses.App.ServiceModel.ServiceCategory,
            ServiceCategoryListDto>> Mapper(string lang)
    {
        return item => new ServiceCategoryListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
