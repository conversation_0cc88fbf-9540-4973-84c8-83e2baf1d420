using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.ServiceClientCategory;

public class ServiceClientCategoryListDto
{
    public static Expression<Func<Models.DomainClasses.App.ServiceModel.ServiceClientCategory, ServiceClientCategoryListDto>> Mapper(string lang)
    {
        return item => new ServiceClientCategoryListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }
}
