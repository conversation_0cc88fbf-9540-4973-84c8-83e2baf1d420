using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Setting;

public class AppSettingGeneralDto
{
    public static Expression<Func<AppSetting, AppSettingGeneralDto>> Mapper(string lang)
    {
        return item => new AppSettingGeneralDto
        {
            AppName = SupportedCultures.LanguageArabic == lang ? item.AppNameAr : item.AppNameEn,
            AppId = item.AppId,
        };
    }

    public string AppName { get; set; }
    public string AppId { get; set; }
}