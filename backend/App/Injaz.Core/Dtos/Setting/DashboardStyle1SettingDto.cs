using System;
using System.Linq;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Setting;

public class DashboardStyle1SettingDto
{
    public static Func<DashboardSetting.Style1Setting, DashboardStyle1SettingDto> Mapper()
    {
        return setting => new DashboardStyle1SettingDto
        {
            BannerFile = string.IsNullOrEmpty(setting?.BannerFileName)
                ? null
                : new File
                {
                    Name = setting.BannerFileName
                },
            Sections = setting?.Sections?.Select(section => new Section
            {
                NameAr = section.NameAr,
                NameEn = section.NameEn,
                Style = section.Style,
                IsHidden = section.IsHidden,
                Items = section.Items?.Select(item => new Section.Item
                    {
                        NameAr = item.NameAr,
                        NameEn = item.NameEn,
                        Link = item.Link,
                        IsHidden = item.IsHidden,
                        IconFile = string.IsNullOrEmpty(item.IconFileName)
                            ? null
                            : new File
                            {
                                Name = item.IconFileName,
                                Hash = item.IconFileHash
                            }
                    })
                    .ToArray() ?? Array.Empty<Section.Item>()
            }).ToArray() ?? Array.Empty<Section>()
        };
    }

    public File BannerFile { get; set; }

    public Section[] Sections { get; set; } = Array.Empty<Section>();

    public class Section
    {
        public string NameAr { get; set; }

        public string NameEn { get; set; }

        public string Style { get; set; }

        public bool IsHidden { get; set; }

        public Item[] Items { get; set; } = Array.Empty<Item>();

        public class Item
        {
            public string NameAr { get; set; }

            public string NameEn { get; set; }

            public string Link { get; set; }

            public bool IsHidden { get; set; }

            public File IconFile { get; set; }
        }
    }

    public class File
    {
        public string Name { get; set; }

        public string Hash { get; set; }

        public byte[] Bytes { get; set; }
    }
}
