using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Pillar;

namespace Injaz.Core.Dtos.Standard;

public class StandardHierarchyWithMembersDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Standard, StandardHierarchyWithMembersDto>>
        Mapper(string lang)
    {
        var expression = PillarHierarchyDto.Mapper(lang);
        var memberExpression = StandardMemberDto.Mapper(lang);

        return item => new StandardHierarchyWithMembersDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Pillar = expression.Invoke(item.Pillar),
            Members = item.UserLinks.Select(x => memberExpression.Invoke(x)).ToList(),
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public PillarHierarchyDto Pillar { get; set; }

    public IEnumerable<StandardMemberDto> Members { get; set; }
}
