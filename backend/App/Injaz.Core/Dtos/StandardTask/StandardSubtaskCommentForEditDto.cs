using System;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.StandardTask;

public class StandardSubtaskCommentForEditDto
{
    public static Expression<Func<StandardSubtaskComment, StandardSubtaskCommentForEditDto>> Mapper()
    {
        return item => new StandardSubtaskCommentForEditDto
        {
            Id = item.Id,
            Content = item.Content
        };
    }

    public Guid Id { get; set; }

    public string Content { get; set; }
}
