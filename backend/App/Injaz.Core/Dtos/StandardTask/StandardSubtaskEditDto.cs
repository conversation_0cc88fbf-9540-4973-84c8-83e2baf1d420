using System;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.StandardTask;

public class StandardSubtaskEditDto
{
    public static Expression<Func<StandardSubtask, StandardSubtaskEditDto>> Mapper()
    {
        return item => new StandardSubtaskEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            From = item.From,
            To = item.To,
        };
    }

    public Guid Id { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }

    public DateTime From { get; set; }

    public DateTime To { get; set; }
}
