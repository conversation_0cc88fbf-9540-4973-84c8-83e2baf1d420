using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.StandardTask;

public class StandardSubtaskHierarchyDto
{
    public static Expression<Func<StandardSubtask, StandardSubtaskHierarchyDto>>
        Mapper(string lang)
    {
        var expression = StandardTaskHierarchyDto.Mapper(lang);
        return item => new StandardSubtaskHierarchyDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Task = expression.Invoke(item.Task)
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public StandardTaskHierarchyDto Task { get; set; }
}
