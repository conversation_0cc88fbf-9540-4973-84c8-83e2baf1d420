using System;
using System.Linq.Expressions;
using StandardTaskModel = Injaz.Core.Models.DomainClasses.App.StandardTask;


namespace Injaz.Core.Dtos.StandardTask;

public class StandardTaskEditDto
{
    public static Expression<Func<StandardTaskModel, StandardTaskEditDto>> Mapper()
    {
        return item => new StandardTaskEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            From = item.From,
            To = item.To,
        };
    }

    public Guid Id { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }

    public DateTime From { get; set; }

    public DateTime To { get; set; }
}
