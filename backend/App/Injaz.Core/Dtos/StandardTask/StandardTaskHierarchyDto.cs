using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Standard;

namespace Injaz.Core.Dtos.StandardTask;

public class StandardTaskHierarchyDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.StandardTask, StandardTaskHierarchyDto>>
        Mapper(string lang)
    {
        var expression = StandardHierarchyDto.Mapper(lang);
        return item => new StandardTaskHierarchyDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Standard = expression.Invoke(item.Standard)
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public StandardHierarchyDto Standard { get; set; }
}
