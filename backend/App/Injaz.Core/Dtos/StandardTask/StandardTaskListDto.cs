using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Standard;
using StandardTaskModel = Injaz.Core.Models.DomainClasses.App.StandardTask;

namespace Injaz.Core.Dtos.StandardTask;

public class StandardTaskListDto
{
    public static Expression<Func<StandardTaskModel, StandardTaskListDto>> Mapper(
        string lang,
        Guid userId,
        bool hasTournamentPermission
    )
    {
        var progressExpression = HelperExpression.StandardTaskProgress();
        var standardExpression = StandardHierarchyDto.Mapper(lang);
        var isSubtaskCreatorExpression = HelperExpression.IsUserStandardSubtaskCreator(userId);

        return item => new StandardTaskListDto
        {
            Id = item.Id,
            Name = SupportedCultures.LanguageArabic == lang ? item.NameAr : item.NameEn,
            From = item.From,
            To = item.To,
            Progress = progressExpression.Invoke(item),
            Standard = standardExpression.Invoke(item.Standard),
            CanCreateSubtask = hasTournamentPermission || isSubtaskCreatorExpression.Invoke(item.Standard)
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public DateTime From { get; set; }

    public DateTime To { get; set; }

    public double? Progress { get; set; }

    public StandardHierarchyDto Standard { get; set; }

    public bool CanCreateSubtask { get; set; }
}
