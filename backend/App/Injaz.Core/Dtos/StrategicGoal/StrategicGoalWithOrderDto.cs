using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.StrategicGoal;

public class StrategicGoalWithOrderDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.StrategicGoal, StrategicGoalWithOrderDto>>
        Mapper(string lang) => item =>
        new StrategicGoalWithOrderDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Order = item.Order
        };

    public Guid Id { get; set; }
    public string Name { get; set; }
    public int? Order { get; set; }
}
