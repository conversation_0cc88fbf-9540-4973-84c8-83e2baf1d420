using System;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Team;

public class TeamListDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Team, TeamListDto>> Mapper(string lang)
    {
        return item => new TeamListDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            UserCount = item.UserLinks.Count(),
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public int UserCount { get; set; }
}
