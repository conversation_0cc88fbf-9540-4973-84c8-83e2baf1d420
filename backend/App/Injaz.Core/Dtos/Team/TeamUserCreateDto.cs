using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.User;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Team;

public class TeamUserCreateDto
{
    [Display(Name = "team")]
    [Required(ErrorMessage = "0_is_required")]
    public TeamSimpleDto Team { get; set; }

    [Display(Name = "user")]
    [Required(ErrorMessage = "0_is_required")]
    public UserSimpleDto User { get; set; }

    [Display(Name = "position")]
    [RegularExpression("^(?:" +
                       TeamUserLink.PositionPresident + "|" +
                       TeamUserLink.PositionVicePresident + "|" +
                       TeamUserLink.PositionReporter +
                       ")$",
        ErrorMessage = "0_is_invalid")]
    public string Position { get; set; }

    [Display(Name = "role")] public string Role { get; set; }

    [Display(Name = "experience")] public string Experience { get; set; }
}
