using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.User;

public class UserSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.User, UserSimpleDto>> Mapper(string lang) => item => new UserSimpleDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
