using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core.Dtos.EvaluationDtos.Evaluations;
using Injaz.Core.Evaluate.Cqrs.Commands.Evaluations;
using Injaz.Core.Evaluate.DtoMappingStrategies;
using Injaz.Core.Evaluate.Interfaces;
using Injaz.Core.Evaluate.Utils;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.Core.Evaluate.Cqrs.Handlers.Evaluations;

public class CreateOrUpdateEvaluationHandler
    : IRequestHandler<CreateEvaluationCommand, EvaluationGetDto>,
        IRequestHandler<UpdateEvaluationCommand, EvaluationGetDto>
{
    private readonly IEvaluateDbContext _db;
    private readonly ValidationService _validationService;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IServiceProvider _serviceProvider;

    public CreateOrUpdateEvaluationHandler(
        IEvaluateDbContext db,
        ValidationService validationService,
        IStringLocalizer<SharedResource> localizer,
        IServiceProvider serviceProvider
    )
    {
        _db = db;
        _validationService = validationService;
        _localizer = localizer;
        _serviceProvider = serviceProvider;
    }

    public Task<EvaluationGetDto> Handle(CreateEvaluationCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<EvaluationGetDto> Handle(UpdateEvaluationCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private Task<EvaluationGetDto> CreateOrUpdate(CreateEvaluationCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
            throw new GenericException { Messages = errors.Select(x => x.ErrorMessage).ToArray() };

        Validate(command);

        Evaluation item;

        if (command is UpdateEvaluationCommand updateCommand)
        {
            item = _db.Evaluations.Find(updateCommand.Id);
            if (item == null) throw new ItemNotFoundException();

            _db.EvaluationStandards.RemoveRange(
                _db.EvaluationStandards.Where(x => x.EvaluationId == updateCommand.Id)
            );
            _db.EvaluationScoreBands.RemoveRange(
                _db.EvaluationScoreBands.Where(x => x.EvaluationId == updateCommand.Id)
            );
            _db.EvaluationEntityLinks.RemoveRange(
                _db.EvaluationEntityLinks.Where(x => x.EvaluationId == updateCommand.Id)
            );
        }
        else
        {
            item = new Evaluation();
            _db.Evaluations.Add(item);
        }

        item.NameAr = command.NameAr.Trim();
        item.NameEn = HelperFunctions.Default(command.NameEn?.Trim(), command.NameAr);
        item.DescriptionAr = command.DescriptionAr;
        item.DescriptionEn = command.DescriptionEn;
        item.Type = command.Type;
        item.Standards = command.Standards?.Select(x => new EvaluationStandard
        {
            NameAr = x.NameAr,
            NameEn = string.IsNullOrEmpty(x.NameEn) ? x.NameAr : x.NameEn,
            Target = x.Target
        }).ToList();
        item.ScoreBands = command.ScoreBands?.Select(x => new EvaluationScoreBand
        {
            NameAr = x.NameAr,
            NameEn = string.IsNullOrEmpty(x.NameEn) ? x.NameAr : x.NameEn,
            From = x.From,
            To = x.To,
            Color = x.Color
        }).ToList();
        item.EntityLinks = command
            .Entities?
            .Select(x => new EvaluationEntityLink { EntityId = x.Id })
            .ToList();

        _db.SaveChanges();

        return Task.FromResult(
            _db
                .Evaluations
                .AsExpandable()
                .Where(x => x.Id.Equals(item.Id))
                .Map(new EvaluationGetDtoMappingStrategy(_serviceProvider, _localizer))
                .First()
        );
    }

    private void Validate(CreateEvaluationCommand command)
    {
        if (command.Standards == null || !command.Standards.Any())
        {
            throw new GenericException
            {
                Messages = new[] { _localizer["evaluation_standards_are_required"].Value }
            };
        }

        if (command.Standards.Any(x => x.Target <= 0))
        {
            throw new GenericException
            {
                Messages = new[] { _localizer["evaluation_standards_target_greater_than_zero"].Value }
            };
        }

        if (command.Entities != null && command.Entities.Any()) ValidateEntities(command);
    }

    private void ValidateEntities(CreateEvaluationCommand command)
    {
        var entitiesQuery = GetEvaluationEntityQueryMethod.Run(_serviceProvider, _localizer, command.Type);

        var entityIds = command.Entities.Select(x => x.Id).ToList();

        var entitiesCount = entitiesQuery.Count(x => entityIds.Contains(x.Id));

        if (entitiesCount != command.Entities.Count())
            throw new GenericException { Messages = new[] { _localizer["invalid_entities"].Value } };
    }
}
