using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Injaz.Core.Evaluate.Cqrs.Commands.Evaluations;
using Injaz.Core.Evaluate.Interfaces;
using Injaz.Core.Exceptions;
using MediatR;

namespace Injaz.Core.Evaluate.Cqrs.Handlers.Evaluations;

public class DeleteEvaluationHandler : IRequestHandler<DeleteEvaluationCommand>
{
    private readonly IEvaluateDbContext _db;

    public DeleteEvaluationHandler(IEvaluateDbContext db) => _db = db;

    public Task<Unit> Handle(DeleteEvaluationCommand command, CancellationToken cancellationToken)
    {
        var item = _db
            .Evaluations
            .FirstOrDefault(x => x.Id == command.Id);

        if (item == null) throw new ItemNotFoundException();


        _db.Evaluations.Remove(item);

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
