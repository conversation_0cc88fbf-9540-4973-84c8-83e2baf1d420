using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core.Dtos.EvaluationDtos.Evaluations;
using Injaz.Core.Evaluate.Cqrs.Commands.Evaluations;
using Injaz.Core.Evaluate.DtoMappingStrategies;
using Injaz.Core.Evaluate.Interfaces;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Injaz.Core.Resources.Shared;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

namespace Injaz.Core.Evaluate.Cqrs.Handlers.Evaluations;

public class DuplicateEvaluationHandler : IRequestHandler<DuplicateEvaluationCommand, EvaluationGetDto>
{
    private readonly IEvaluateDbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IServiceProvider _serviceProvider;

    public DuplicateEvaluationHandler(
        IEvaluateDbContext db,
        IStringLocalizer<SharedResource> localizer,
        IServiceProvider serviceProvider)
    {
        _db = db;
        _localizer = localizer;
        _serviceProvider = serviceProvider;
    }

    public Task<EvaluationGetDto> Handle(DuplicateEvaluationCommand command, CancellationToken cancellationToken)
    {
        var item = _db
            .Evaluations
            .Include(x => x.Standards)
            .Include(x => x.ScoreBands)
            .Include(x => x.EntityLinks)
            .FirstOrDefault(x => x.Id == command.Id);

        if (item == null) throw new ItemNotFoundException();


        var duplicate = new Evaluation
        {
            NameAr = item.NameAr + " تكرار",
            NameEn = (item.NameEn ?? item.NameAr) + " duplicated",
            DescriptionAr = item.DescriptionAr,
            DescriptionEn = item.DescriptionEn,
            Type = item.Type,
            Standards = item.Standards.Select(s => new EvaluationStandard
                { NameAr = s.NameAr, NameEn = s.NameEn, Target = s.Target }).ToList(),
            ScoreBands = item.ScoreBands.Select(sb => new EvaluationScoreBand
                { NameAr = sb.NameAr, NameEn = sb.NameEn, Color = sb.Color, From = sb.From, To = sb.To }).ToList(),
            EntityLinks = item.EntityLinks.Select(el => new EvaluationEntityLink { EntityId = el.EntityId }).ToList()
        };
        _db.Evaluations.Add(duplicate);
        _db.SaveChanges();

        return Task.FromResult(
            _db
                .Evaluations
                .AsExpandable()
                .Where(x => x.Id.Equals(item.Id))
                .Map(new EvaluationGetDtoMappingStrategy(_serviceProvider, _localizer))
                .First()
        );
    }
}
