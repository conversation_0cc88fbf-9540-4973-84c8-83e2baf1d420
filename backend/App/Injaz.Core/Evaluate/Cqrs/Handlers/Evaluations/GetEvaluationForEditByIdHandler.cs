using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core.Dtos.EvaluationDtos.Evaluations;
using Injaz.Core.Evaluate.Cqrs.Queries.Evaluations;
using Injaz.Core.Evaluate.DtoMappingStrategies;
using Injaz.Core.Evaluate.Interfaces;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;
using Injaz.Core.Resources.Shared;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.Core.Evaluate.Cqrs.Handlers.Evaluations;

public class GetEvaluationForEditByIdHandler : IRequestHandler<GetEvaluationForEditByIdQuery, EvaluationEditDto>
{
    private readonly IEvaluateDbContext _db;
    private readonly IServiceProvider _serviceProvider;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public GetEvaluationForEditByIdHandler(
        IEvaluateDbContext db,
        IServiceProvider serviceProvider,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _serviceProvider = serviceProvider;
        _localizer = localizer;
    }

    public Task<EvaluationEditDto> Handle(GetEvaluationForEditByIdQuery query, CancellationToken cancellationToken)
    {
        var item = _db
            .Evaluations
            .AsExpandable()
            .Where(x => x.Id.Equals(query.Id))
            .Map(new EvaluationEditDtoMappingStrategy(_serviceProvider, _localizer))
            .FirstOrDefault();

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
