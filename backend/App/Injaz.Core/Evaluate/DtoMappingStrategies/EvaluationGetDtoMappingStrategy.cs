using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Abstraction;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.EvaluationDtos.Evaluations;
using Injaz.Core.Evaluate.Dtos;
using Injaz.Core.Evaluate.Utils;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Injaz.Core.Resources.Shared;
using Microsoft.Extensions.Localization;

namespace Injaz.Core.Evaluate.DtoMappingStrategies;

public class EvaluationGetDtoMappingStrategy : IDtoMappingStrategy<Evaluation, EvaluationGetDto>
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public EvaluationGetDtoMappingStrategy(
        IServiceProvider serviceProvider,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _serviceProvider = serviceProvider;
        _localizer = localizer;
    }

    public IEnumerable<EvaluationGetDto> Execute(IQueryable<Evaluation> source)
    {
        var evaluations = source
            .Select(Mapper(HelperFunctions.GetLanguageCode()))
            .ToList();

        foreach (var evaluation in evaluations)
        {
            if (!evaluation.Entities.Any()) continue;

            var evaluationEntityIds = evaluation.Entities.Select(x => x.Id).ToList();

            var entitiesQuery = GetEvaluationEntityQueryMethod.Run(_serviceProvider, _localizer, evaluation.Type);

            var selectedEntities = entitiesQuery
                .Where(x => evaluationEntityIds.Contains(x.Id))
                .ToList();

            evaluation.Entities = evaluation.Entities
                .Where(x => selectedEntities.Exists(y => x.Id == y.Id))
                .ToList();

            foreach (var entity in evaluation.Entities)
            {
                entity.Name = selectedEntities.Find(x => x.Id == entity.Id)?.Name;
            }
        }

        return evaluations;
    }

    private static Expression<Func<Evaluation, EvaluationGetDto>> Mapper(string lang)
    {
        var scoreBandExpression = EvaluationScoreBandGetDto.Mapper(lang);
        var entityExpression = EvaluationEntitySimpleDto.Mapper();

        return item => new EvaluationGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Description = lang == SupportedCultures.LanguageArabic ? item.DescriptionAr : item.DescriptionEn,
            Type = item.Type,
            Standards = item.Standards.Select(x => new EvaluationStandardGetDto
            {
                Name = lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn,
                Target = x.Target
            }).ToList(),
            ScoreBands = item.ScoreBands.Select(x => scoreBandExpression.Invoke(x)).ToList(),
            Entities = item.EntityLinks.Select(x => entityExpression.Invoke(x)).ToList()
        };
    }
}
