using System;
using System.Linq;
using System.Reflection;
using Humanizer;
using Injaz.Core.Evaluate.Attributes;
using Injaz.Core.Evaluate.Cqrs.Commands.Evaluations;
using Injaz.Core.Evaluate.Cqrs.Queries.Evaluations;
using Injaz.Core.Evaluate.Interfaces;
using Injaz.Core.Evaluate.Models;
using Injaz.Core.Evaluate.Services;
using Injaz.Core.Evaluate.Utils;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers.Models;
using Newtonsoft.Json;

namespace Injaz.Core.Evaluate.Extensions;

public static class EvaluateEndpointRouteBuilderExtension
{
    public static IEndpointRouteBuilder AddEvaluateRoutes(this IEndpointRouteBuilder builder)
    {
        builder.AddEvaluationRoutes();
        builder.AddEvaluationInstanceRoutes();

        return builder;
    }

    private static void AddEvaluationRoutes(this IEndpointRouteBuilder builder)
    {
        builder.MapGet("/evaluation", async (ISender mediator, HttpContext context) =>
        {
            var response = await mediator.Send(new GetEvaluationListQuery()
            {
                Keyword = context.Request.Query["keyword"],
                PageNumber = int.TryParse(context.Request.Query["pageNumber"], out var pageNumber) ? pageNumber : 0,
                PageSize = int.TryParse(context.Request.Query["pageSize"], out var pageSize) ? pageSize : 0,
            });

            return new Result
            {
                Success = 1,
                Code = 0x00,
                Extra = response
            };
        }).RequireAuthorization(PermissionNameList.FullAccess);

        builder.MapGet("/evaluation/{id:guid}", async (ISender mediator, HttpContext context, Guid id) =>
        {
            var forEdit = bool.TryParse(context.Request.Query["forEdit"], out var tmp) && tmp;

            var item = await mediator.Send(forEdit
                ? new GetEvaluationForEditByIdQuery { Id = id }
                : new GetEvaluationByIdQuery { Id = id }
            );

            return new Result
            {
                Success = 1,
                Code = 0x00,
                Extra = item
            };
        }).RequireAuthorization(PermissionNameList.FullAccess);

        builder.MapPost("/evaluation", async (ISender mediator, HttpContext context) =>
        {
            var command = JsonConvert.DeserializeObject<CreateEvaluationCommand>(context.Request.Form["evaluation"]);
            return new Result
            {
                Success = 1,
                Code = 0x00,
                Extra = await mediator.Send(command)
            };
        }).RequireAuthorization(PermissionNameList.FullAccess);

        builder.MapPut("/evaluation", async (ISender mediator, HttpContext context) =>
        {
            var command = JsonConvert.DeserializeObject<UpdateEvaluationCommand>(context.Request.Form["evaluation"]);
            return new Result
            {
                Success = 1,
                Code = 0x00,
                Extra = await mediator.Send(command)
            };
        }).RequireAuthorization(PermissionNameList.FullAccess);

        builder.MapDelete("/evaluation/{id:guid}",
            async (ISender mediator, IStringLocalizer<SharedResource> localizer, Guid id) =>
            {
                await mediator.Send(new DeleteEvaluationCommand { Id = id });
                return new Result
                {
                    Success = 1,
                    Code = 0x00,
                    Messages = new string[] { localizer["item_removed_successfully"] }
                };
            }).RequireAuthorization(PermissionNameList.FullAccess);

        builder.MapGet("/evaluation/type", (IStringLocalizer<SharedResource> localizer) =>
        {
            return new Result
            {
                Success = 1,
                Code = 0x00,
                Extra = GetEvaluationTypes.Run().Select(x => new
                {
                    Id = x,
                    Name = localizer["evaluation_type_" + x].Value
                })
            };
        }).RequireAuthorization(PermissionNameList.FullAccess);

        builder.MapPut("/evaluation/{id:guid}/toggle-default/{isDefault:bool}",
            async (ISender mediator, IStringLocalizer<SharedResource> localizer, Guid id, bool isDefault) =>
            {
                await mediator.Send(new ToggleEvaluationDefaultCommand { Id = id, IsDefault = isDefault });
                return new Result
                {
                    Success = 1,
                    Code = 0x00,
                    Messages = new string[] { localizer["operation_successful"] }
                };
            }).RequireAuthorization(PermissionNameList.FullAccess);

        builder.MapPut("/evaluation/{id:guid}/toggle-disabled/{isDisabled:bool}",
            async (ISender mediator, IStringLocalizer<SharedResource> localizer, Guid id, bool isDisabled) =>
            {
                await mediator.Send(new ToggleEvaluationDisabledCommand { Id = id, IsDisabled = isDisabled });
                return new Result
                {
                    Success = 1,
                    Code = 0x00,
                    Messages = new string[] { localizer["operation_successful"] }
                };
            }).RequireAuthorization(PermissionNameList.FullAccess);

        builder.MapPost("/evaluation/{id:guid}/duplicate",
            async (ISender mediator, Guid id) =>
            {
                var item = await mediator.Send(new DuplicateEvaluationCommand { Id = id });
                return new Result
                {
                    Success = 1,
                    Code = 0x00,
                    Extra = item
                };
            }).RequireAuthorization(PermissionNameList.FullAccess);
    }

    private static void AddEvaluationInstanceRoutes(this IEndpointRouteBuilder builder)
    {
        var entityTypes = Assembly.GetAssembly(typeof(EvaluateEndpointRouteBuilderExtension))!
            .GetTypes()
            .Where(x => x.GetCustomAttribute<EvaluateAttribute>() != null)
            .ToList();

        var routesMethod =
            typeof(EvaluateEndpointRouteBuilderExtension).GetMethod(
                nameof(AddEvaluationInstanceRoutesForType),
                BindingFlags.Static | BindingFlags.NonPublic)!;

        entityTypes.ForEach(entityType =>
        {
            routesMethod.MakeGenericMethod(entityType).Invoke(null, new object[] { builder });
        });
    }

    private static void AddEvaluationInstanceRoutesForType<TEntity>(this IEndpointRouteBuilder builder)
        where TEntity : class, IEvaluatableEntity
    {
        var attribute = typeof(TEntity).GetCustomAttribute<EvaluateAttribute>()!;
        var type = attribute.Type;
        var route = type.Kebaberize();

        // Get Misc items by type
        builder.MapGet($"/evaluation/{route}/misc",
            (EvaluationService<TEntity> service, HttpContext context) =>
            {
                var keyword = context.Request.Query["keyword"];
                var pageSize = int.TryParse(context.Request.Query["pageSize"], out var tmp) ? tmp : 0;

                var result = service.GetEntityList(keyword, pageSize);

                return new Result { Success = 1, Code = 0x00, Extra = result };
            }
        );

        // Get evaluation list by type
        builder.MapGet($"/evaluation/{route}",
            async (EvaluationService<TEntity> service, HttpContext context) =>
            {
                Guid? savedEntityId = Guid.TryParse(context.Request.Query["savedEntityId"], out var tmp) ? tmp : null;

                var result = await service.GetEvaluationList(savedEntityId);
                return new Result { Success = 1, Code = 0x00, Extra = result };
            });

        // Get evaluation standards
        builder.MapGet($"/evaluation/{route}/{{id:guid}}/standard",
            async (EvaluationService<TEntity> service, Guid id) =>
            {
                var result = await service.GetEvaluationStandardList(id);
                return new Result { Success = 1, Code = 0x00, Extra = result };
            });

        // Get evaluation instances for a give entity id
        builder.MapGet($"/evaluation/instance/{route}/{{entityId:guid}}",
            async (EvaluationService<TEntity> service, Guid entityId) =>
            {
                var result = await service.GetInstanceList(entityId);
                return new Result { Success = 1, Code = 0x00, Extra = result };
            });

        // Get evaluation instance by id
        builder.MapGet($"/evaluation/instance/{{id:guid}}/{route}",
            async (EvaluationService<TEntity> service, Guid id) =>
            {
                var result = await service.GetInstance(id);
                return new Result { Success = 1, Code = 0x00, Extra = result };
            });

        // Get evaluation instance records
        builder.MapGet($"/evaluation/instance/{{id:guid}}/{route}/record",
            async (EvaluationService<TEntity> service, Guid id) =>
            {
                var result = await service.GetInstanceRecordList(id);
                return new Result { Success = 1, Code = 0x00, Extra = result };
            });

        // Create instance
        builder.MapPost($"/evaluation/{{id:guid}}/instance/{route}/{{entityId:guid}}",
            async (EvaluationService<TEntity> service, HttpContext context, Guid id, Guid entityId) =>
            {
                var data = new CreateInstanceRecordValueListData(
                    context.Request.Form["note"],
                    JsonConvert.DeserializeObject<CreateStandardRecordValueData[]>(context.Request.Form["values"])
                );

                var result = await service.CreateInstance(id, entityId, data);

                return new Result { Success = 1, Code = 0x00, Extra = result };
            });

        // Update instance records
        builder.MapPut($"/evaluation/instance/{{id:guid}}/{route}",
            async (EvaluationService<TEntity> service, HttpContext context, Guid id) =>
            {
                var data = new UpdateInstanceRecordValueListData(id,
                    context.Request.Form["note"],
                    JsonConvert.DeserializeObject<StandardRecordValueData[]>(context.Request.Form["values"]));

                var result = await service.UpdateInstanceRecordValueList(data);

                return new Result { Success = 1, Code = 0x00, Extra = result };
            });


        // Delete instance
        builder.MapDelete($"evaluation/instance/{{id:guid}}/{route}",
            async (EvaluationService<TEntity> service, IStringLocalizer<SharedResource> localizer,
                Guid id) =>
            {
                await service.DeleteInstance(id);
                return new Result
                {
                    Success = 1, Code = 0x00, Messages = new string[] { localizer["operation_successful"] }
                };
            });

        // Score bands
        builder.MapGet($"evaluation/{{evaluationId:guid}}/score-band/{route}",
            (EvaluationService<TEntity> service, Guid evaluationId) => new Result
            {
                Success = 1, Code = 0x00, Extra = service.GetScoreBandList(evaluationId)
            });

        // Statistics
        builder.MapGet($"evaluation/{{id:guid}}/statistics/{route}",
            (EvaluationService<TEntity> service, Guid id) => new Result
            {
                Success = 1, Code = 0x00, Extra = service.GetOverallStatistics(id)
            });

        // Score band statistics
        builder.MapGet($"evaluation/{{id:guid}}/statistics/{route}/score-band",
            (EvaluationService<TEntity> service, Guid id) => new Result
            {
                Success = 1, Code = 0x00, Extra = service.GetScoreBandStatistics(id)
            });
    }
}
