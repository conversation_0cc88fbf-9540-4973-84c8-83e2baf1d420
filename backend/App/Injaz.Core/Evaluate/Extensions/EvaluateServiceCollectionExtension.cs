using System.Linq;
using System.Reflection;
using Injaz.Core.Evaluate.Cqrs.Handlers.Evaluations;
using Injaz.Core.Evaluate.Interfaces;
using Injaz.Core.Evaluate.Services;
using MediatR;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.Core.Evaluate.Extensions;

public static class EvaluateServiceCollectionExtension
{
    public static IServiceCollection AddEvaluateServices<TDbContext>(this IServiceCollection services,
        Assembly assembly = null)
        where TDbContext : class, IEvaluateDbContext
    {
        services.AddMediatR(
            typeof(CreateOrUpdateEvaluationHandler),
            typeof(DeleteEvaluationHandler),
            typeof(GetEvaluationByIdHandler),
            typeof(GetEvaluationForEditByIdHandler),
            typeof(GetEvaluationListHandler)
        );

        services.AddScoped<IEvaluateDbContext, TDbContext>();

        services.AddEvaluationConfigurations(assembly);

        return services;
    }

    public static IServiceCollection AddEvaluationConfigurations(
        this IServiceCollection services,
        Assembly assembly = null)
    {
        assembly ??= Assembly.GetCallingAssembly();

        assembly
            .GetTypes()
            .Where(x => (x.BaseType?.IsGenericType ?? false) &&
                        x.BaseType?.GetGenericTypeDefinition() == typeof(EvaluationService<>))
            .Where(x => !x.IsInterface && !x.IsAbstract && !x.IsGenericType)
            .ToList()
            .ForEach(type =>
            {
                var baseType = typeof(EvaluationService<>).MakeGenericType(type.BaseType!.GetGenericArguments()[0]);
                services.AddScoped(baseType, type);
            });


        return services;
    }
}
