using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Microsoft.EntityFrameworkCore;

namespace Injaz.Core.Evaluate.Interfaces;

public interface IEvaluateDbContext
{
    public DbSet<Evaluation> Evaluations { get; set; }
    public DbSet<EvaluationStandard> EvaluationStandards { get; set; }
    public DbSet<EvaluationInstance> EvaluationInstances { get; set; }
    public DbSet<EvaluationStandardRecord> EvaluationStandardRecords { get; set; }
    public DbSet<EvaluationScoreBand> EvaluationScoreBands { get; set; }
    public DbSet<EvaluationEntityLink> EvaluationEntityLinks { get; set; }
    public DbSet<Department> Departments { get; set; }

    public DbSet<T> Set<T>() where T : class;

    public int SaveChanges();
}
