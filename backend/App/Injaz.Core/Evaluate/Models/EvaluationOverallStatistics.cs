namespace Injaz.Core.Evaluate.Models;

public class EvaluationOverallStatistics
{
    public int Count { get; set; }

    public int EvaluatedCount { get; set; }

    public int NotEvaluatedCount { get; set; }

    public double? Average { get; set; }

    public static EvaluationOverallStatistics Empty => new()
    {
        Count = 0,
        EvaluatedCount = 0,
        NotEvaluatedCount = 0,
        Average = null
    };
}
