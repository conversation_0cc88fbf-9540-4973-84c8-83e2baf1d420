using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Injaz.Core.Abstraction;
using Injaz.Core.Evaluate.Models;

namespace Injaz.Core.Evaluate.SqlFunctions;

public class GetEvaluationScoreDetailsSqlFunction : ISqlFunction
{
    public string GetName()
    {
        return "fn_get_evaluation_score_detail";
    }

    public MethodInfo GetBinding()
    {
        return GetType().GetMethod(nameof(Call));
    }

    public IEnumerable<Type> GetDependencies()
    {
        return new[]
        {
            typeof(GetEvaluationScoreBasicSqlFunction)
        };
    }

    public string GetDefinition()
    {
        // language=sql
        return $@"
          CREATE OR ALTER FUNCTION {GetName()}(
                                                @entity_id UNIQUEIDENTIFIER,
                                                @type NVARCHAR(MAX),
                                                @evaluation_id UNIQUEIDENTIFIER)
            RETURNS TABLE
                AS
                RETURN(SELECT esb.id                 AS score_band_id,
                               @entity_id             AS entity_id,
                               e.id                   AS id,
                               esb.name_ar            AS name_ar,
                               esb.name_en            AS name_en,
                               esb.color              AS color,
                               scores.value           AS value,
                               scores.instance_id     AS instance_id,
                               scores.evaluation_time AS evaluation_time
                        FROM dbo.fn_get_evaluation_score_basic(@entity_id, @evaluation_id) AS scores
                                 INNER JOIN evaluations e
                                            ON type = @type AND e.is_deleted = 0 AND
                                               (e.id = (SELECT TOP 1 ei.evaluation_id
                                                        FROM evaluation_instances ei
                                                        WHERE ei.entity_id = @entity_id
                                                          AND ei.is_deleted = 0
                                                          AND (ei.evaluation_id = @evaluation_id OR @evaluation_id IS NULL)
                                                        ORDER BY ei.creation_time DESC))
                                 INNER JOIN evaluation_score_bands esb
                                            ON e.id = esb.evaluation_id AND (
                                                (esb.[from] IS NULL AND scores.value < esb.[to]) OR
                                                (esb.[from] <= scores.value AND esb.[to] IS NULL) OR
                                                (esb.[from] <= scores.value AND scores.value < esb.[to])));
        ";
    }

    public static IQueryable<EvaluationScoreDetail> Call(Guid entityId, string type, Guid? evaluationId)
    {
        throw new NotSupportedException();
    }
}
