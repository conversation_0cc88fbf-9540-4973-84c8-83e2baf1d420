using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Injaz.Core.Evaluate.Attributes;

namespace Injaz.Core.Evaluate.Utils;

public static class GetEvaluationTypes
{
    public static IEnumerable<string> Run()
    {
        return typeof(GetEvaluationTypes).Assembly.GetTypes()
            .Where(x => x.GetCustomAttribute<EvaluateAttribute>() != null)
            .Select(x => x.GetCustomAttribute<EvaluateAttribute>()!.Type);
    }
}
