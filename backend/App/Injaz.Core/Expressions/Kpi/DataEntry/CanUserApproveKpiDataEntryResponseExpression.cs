using System;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App;
using LinqKit;

namespace Injaz.Core.Expressions.Kpi.DataEntry;

public static class CanUserApproveKpiDataEntryResponseExpression
{
    public static Expression<Func<KpiResultDataEntryResponse, KpiResultDataEntryResponseTransfer, bool>> Run(
        IQueryable<Department> departments,
        Guid userId,
        bool hasKpiResultFlowPermission,
        bool hasKpiPermission
    )
    {
        var isInvolvedWithKpiResultExpression = HelperExpression.IsInvolvedWithKpiResult(userId, departments, true);

        return (x, y) =>
            // Either the response at the data entry stage, at which
            // only users that have the appropriate permission list the
            // response if they fall within the department attached to
            // the result...
            (
                y.Assignee == KpiResultDataEntryResponseTransfer.AssigneeDataEntry &&
                hasKpiResultFlowPermission &&
                isInvolvedWithKpiResultExpression.Invoke(x.Result)
            ) ||

            // ...Or the response is at the signatory phase, where the user has
            // to be in the list of allowed users stored in the transfer.
            // no need to check here if the user is still the signatory of
            // the department since this check has already been made when
            // the user was added to the transfer.
            (
                (
                    y.Assignee == KpiResultDataEntryResponseTransfer.AssigneeLevel1 ||
                    y.Assignee == KpiResultDataEntryResponseTransfer.AssigneeLevel2
                ) &&
                y.UserLinks.Any(z => z.UserId == userId)
            ) ||

            // ...Or the response is with at the last stage, at which only
            // users with kpi permissions can act on them.
            (
                (
                    y.Assignee == KpiResultDataEntryResponseTransfer.AssigneeKpiManager ||
                    y.Assignee == KpiResultDataEntryResponseTransfer.AssigneeDone
                ) &&
                hasKpiPermission
            );
    }
}
