using System.Threading.Tasks;
using Injaz.Core.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.Core.Extensions;

public static class ControllerExtension
{
    public static async Task<bool> EnsureUserHasPermission(this Controller controller, string permissionName)
    {
        var permissionEnsurer = controller
            .HttpContext
            .RequestServices
            .GetRequiredService<PermissionEnsurerService>();

        return await permissionEnsurer.Ensure(permissionName);

        // var authorizationOptions = controller
        //     .HttpContext
        //     .RequestServices
        //     .GetRequiredService<IOptions<AuthorizationOptions>>()
        //     .Value;
        // var policy = authorizationOptions.GetPolicy(permissionName);
        //
        // if (policy == null) return false;
        //
        // var policyEvaluator = controller.HttpContext.RequestServices.GetRequiredService<IPolicyEvaluator>();
        //
        // var authenticationResult = await policyEvaluator.AuthenticateAsync(policy, controller.HttpContext);
        // var result =
        //     await policyEvaluator.AuthorizeAsync(policy, authenticationResult, controller.HttpContext, null);
        //
        // return result.Succeeded;
    }
}
