using System;
using System.Linq.Expressions;
using ExpressionVisitor = LinqKit.ExpressionVisitor;

namespace Injaz.Core.Extensions;

public static class ExpressionExtension
{
    public static Expression<Func<T, S, bool>> Or<T, S>(
        this Expression<Func<T, S, bool>> expr1,
        Expression<Func<T, S, bool>> expr2)
    {
        var right =
            new RebindParameterVisitor(expr2.Parameters[0], expr1.Parameters[0])
                .Visit(new RebindParameterVisitor(expr2.Parameters[1], expr1.Parameters[1]).Visit(expr2.Body));
        return Expression.Lambda<Func<T, S, bool>>(
            Expression.OrElse(expr1.Body, right),
            expr1.Parameters
        );
    }

    public static Expression<Func<T, S, bool>> And<T, S>(
        this Expression<Func<T, S, bool>> expr1,
        Expression<Func<T, S, bool>> expr2)
    {
        var right =
            new RebindParameterVisitor(expr2.Parameters[0], expr1.Parameters[0])
                .Visit(new RebindParameterVisitor(expr2.Parameters[1], expr1.Parameters[1]).Visit(expr2.Body));
        return Expression.Lambda<Func<T, S, bool>>(
            Expression.And(expr1.Body, right),
            expr1.Parameters
        );
    }

    private class RebindParameterVisitor : ExpressionVisitor
    {
        private readonly ParameterExpression _oldParameter;
        private readonly ParameterExpression _newParameter;

        public RebindParameterVisitor(
            ParameterExpression oldParameter,
            ParameterExpression newParameter)
        {
            _oldParameter = oldParameter;
            _newParameter = newParameter;
        }

        protected override Expression VisitParameter(ParameterExpression node) => node == _oldParameter
            ? _newParameter
            : base.VisitParameter(node);
    }
}
