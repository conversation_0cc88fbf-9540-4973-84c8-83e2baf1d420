using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using Injaz.Core.Abstraction;

namespace Injaz.Core.Extensions;

public static class QueryableExtension
{
    public static IEnumerable<TDestination> Map<T, TDestination>(this IQueryable<T> source,
        IDtoMappingStrategy<T, TDestination> strategy)
    {
        return strategy.Execute(source);
    }

    public static IQueryable<T> ApplySort<T>(this IQueryable<T> entities, string orderByQueryString)
    {
        if (string.IsNullOrWhiteSpace(orderByQueryString)) return entities;

        try
        {
            return entities.OrderBy(orderByQueryString);
        }
        catch
        {
            return entities;
        }
    }
}
