namespace Injaz.Core.Extensions;

public static class StringExtension
{
    public static string Last(this string str, int count)
    {
        return str.Length < count ? str : str.Substring(str.Length - count);
    }

    public static string First(this string str, int count)
    {
        return str.Length < count ? str : str.Substring(0, count);
    }

    public static string CapitalizeFirstLetter(this string str)
    {
        return str.Length == 0 ? str : str.Substring(0, 1).ToUpper() + str.Substring(1).ToLower();
    }
}
