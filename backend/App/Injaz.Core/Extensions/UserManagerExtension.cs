using System.Linq;
using System.Threading.Tasks;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.Identity;
using Microsoft.AspNetCore.Identity;

namespace Injaz.Core.Extensions;

public static class UserManagerExtension
{
    public static async Task<IdentityResult> Create2Async(
        this UserManager<User> userManager,
        User user,
        string password
    )
    {
        AddClaims(user);
        var result = string.IsNullOrEmpty(password)
            ? await userManager.CreateAsync(user)
            : await userManager.CreateAsync(user, password);
        return result;
    }

    public static async Task<IdentityResult> UpdateAsync(this UserManager<User> userManager, User user,
        string password)
    {
        // Remove name claims.
        var claims = await userManager.GetClaimsAsync(user);
        await userManager.RemoveClaimsAsync(user, claims.Where(x => new[]
        {
            ClaimTypes.FullName,
            ClaimTypes.FullNameAr,
            ClaimTypes.FullNameEn
        }.Contains(x.Type)));

        // Add new claims
        AddClaims(user);

        // Update the user.
        var result = await userManager.UpdateAsync(user);
        if (!result.Succeeded) return result;

        if (!string.IsNullOrEmpty(password))
        {
            var token = await userManager.GeneratePasswordResetTokenAsync(user);
            result = await userManager.ResetPasswordAsync(user, token, password);
        }

        return result;
    }

    private static void AddClaims(User user)
    {
        user.Claims = new[]
        {
            new UserClaim
            {
                ClaimType = ClaimTypes.FullName,
                ClaimValue = $"{user.NameAr} | {user.NameEn}",
            },
            new UserClaim
            {
                ClaimType = ClaimTypes.FullNameAr,
                ClaimValue = user.NameAr,
            },
            new UserClaim
            {
                ClaimType = ClaimTypes.FullNameEn,
                ClaimValue = user.NameEn,
            },
        }.ToList();
    }
}
