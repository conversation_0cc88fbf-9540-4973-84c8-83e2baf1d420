using System;
using Injaz.Core.Flow.Interfaces;
using Injaz.Core.Flow.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.Core.Flow.Extensions;

public static class FlowServiceProviderExtension
{
    public static IFlowService GetFlowServiceForEntityType(this IServiceProvider serviceProvider, Type entityType)
    {
        var genericFlowServiceType = typeof(FlowService<>);
        var flowServiceType = genericFlowServiceType.MakeGenericType(entityType);
        return (IFlowService)serviceProvider.GetRequiredService(flowServiceType);
    }
}
