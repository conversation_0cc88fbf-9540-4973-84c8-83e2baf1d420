using System;

namespace Injaz.Core.Flow.Models;

public class FlowResult
{
    public static FlowResult Success()
    {
        return new FlowResult(true, Array.Empty<string>());
    }

    public static FlowResult Error(string[] errors)
    {
        return new FlowResult(false, errors);
    }

    private FlowResult(bool isSucceeded, string[] errors)
    {
        IsSucceeded = isSucceeded;
        Errors = errors;
    }

    public bool IsSucceeded { get; }

    public string[] Errors { get; }
}
