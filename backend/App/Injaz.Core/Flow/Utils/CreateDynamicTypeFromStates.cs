using System;
using System.Reflection;
using System.Reflection.Emit;

namespace Injaz.Core.Flow.Utils;

public static class CreateDynamicTypeFromStates
{
    public static Type Run(string[] states)
    {
        var assemblyName = new AssemblyName { Name = "ApprovalFlowTempAssembly" };
        var assemblyBuilder = AssemblyBuilder.DefineDynamicAssembly(assemblyName, AssemblyBuilderAccess.RunAndCollect);
        var moduleBuilder = assemblyBuilder.DefineDynamicModule("ApprovalFlowTempModule");
        var typeBuilder = moduleBuilder.DefineType("ApprovalFlowAvailableActions", TypeAttributes.Public);

        foreach (var state in states)
        {
            var fieldBuilder = typeBuilder.DefineField(state,
                typeof(bool),
                FieldAttributes.Private);

            var propertyBuilder = typeBuilder.DefineProperty(
                state,
                PropertyAttributes.HasDefault,
                typeof(bool),
                null
            );

            // The property set and property get methods require a special
            // set of attributes.
            var getSetAttr =
                MethodAttributes.Public |
                MethodAttributes.SpecialName |
                MethodAttributes.HideBySig;

            // Define the "get" accessor method for CustomerName.
            var getPropMethodBuilder =
                typeBuilder.DefineMethod($"get_{state}",
                    getSetAttr,
                    typeof(bool),
                    Type.EmptyTypes);

            var getIL = getPropMethodBuilder.GetILGenerator();

            getIL.Emit(OpCodes.Ldarg_0); // cspell:ignore Ldarg
            getIL.Emit(OpCodes.Ldfld, fieldBuilder); // cspell:ignore Ldfld
            getIL.Emit(OpCodes.Ret);

            // Define the "set" accessor method for CustomerName.
            var setPropMethodBuilder =
                typeBuilder.DefineMethod($"set_{state}",
                    getSetAttr,
                    null,
                    new[] { typeof(bool) });

            var setIL = setPropMethodBuilder.GetILGenerator();

            setIL.Emit(OpCodes.Ldarg_0); // cspell:ignore Ldarg
            setIL.Emit(OpCodes.Ldarg_1); // cspell:ignore Ldarg
            setIL.Emit(OpCodes.Stfld, fieldBuilder); // cspell:ignore Stfld
            setIL.Emit(OpCodes.Ret);

            // Last, we must map the two methods created above to our PropertyBuilder to
            // their corresponding behaviors, "get" and "set" respectively.
            propertyBuilder.SetGetMethod(getPropMethodBuilder);
            propertyBuilder.SetSetMethod(setPropMethodBuilder);
        }

        return typeBuilder.CreateType()!;
    }
}
