using System;
using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using MNMWebApp.Models.Abstraction;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Injaz.Core.Models.Abstraction;

public class AppBaseDbContext : BaseDbContext<User>
{
    public class JsonArrayConverter<T> : ValueConverter<T[], string>
    {
        public JsonArrayConverter() : base(Serialize, Deserialize, null)
        {
        }

        private static Expression<Func<string, T[]>> Deserialize = value =>
            HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<T[]>(value) : null;

        private static Expression<Func<T[], string>> Serialize = value => JsonConvert.SerializeObject(value,
            new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            });
    }

    public AppBaseDbContext(DbContextOptions options, IServiceProvider serviceProvider)
        : base(options, serviceProvider)
    {
    }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        FluentApis.Setup(builder);

        var dateTimeConverter = new ValueConverter<DateTime, DateTime>(
            v => v, v => DateTime.SpecifyKind(v, DateTimeKind.Utc));

        foreach (var entityType in builder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                    property.SetValueConverter(dateTimeConverter);
            }
        }
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder.Properties<string[]>()
            .HaveConversion<JsonArrayConverter<string>>();
    }
}
