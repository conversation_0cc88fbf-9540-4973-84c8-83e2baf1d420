using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Injaz.Core.Models.DomainClasses.App.IntegrationModel;
using Injaz.Core.Models.DomainClasses.App.PartnerModel;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using Injaz.Core.Models.DomainClasses.App.RiskModel;
using Injaz.Core.Models.DomainClasses.App.ServiceModel;
using Injaz.Core.Models.DomainClasses.Identity;
using Injaz.Core.Models.DomainClasses.Permission;
using Microsoft.EntityFrameworkCore;
using MNMWebApp.OAuth.Models;

namespace Injaz.Core.Models;

public class DatabaseInitializer : DbContext
{
    public DatabaseInitializer(DbContextOptions<DatabaseInitializer> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        FluentApis.Setup(modelBuilder);
    }

    // Kpi
    public DbSet<Kpi> Kpis { get; set; }
    public DbSet<KpiResult> KpiResults { get; set; }
    public DbSet<KpiKpiTagLink> KpisKpiTags { get; set; }
    public DbSet<KpiTag> KpiTags { get; set; }
    public DbSet<KpiBalancedBehaviorCard> KpiBalancedBehaviorCards { get; set; }
    public DbSet<KpiStrategicGoalLink> KpiStrategicGoalLinks { get; set; }
    public DbSet<KpiResultPeriod> KpiResultPeriods { get; set; }
    public DbSet<KpiBenchmark> KpiBenchmarks { get; set; }
    public DbSet<KpiResultRequest> KpiResultRequest { get; set; }
    public DbSet<KpiResultRequestComment> KpiResultRequestComments { get; set; }
    public DbSet<KpiType> KpiTypes { get; set; }
    public DbSet<KpiResultCategory> KpiResultCategories { get; set; }
    public DbSet<KpiResultTargetSettingMethod> KpiResultTargetSettingMethods { get; set; }
    public DbSet<KpiResultSubcategory> KpiResultSubcategories { get; set; }
    public DbSet<KpiResultBreakdown> KpiResultBreakdowns { get; set; }
    public DbSet<KpiResultPeriodBreakdown> KpiResultPeriodBreakdowns { get; set; }

    // Kpi dynamic data entry request.
    public DbSet<KpiDynamicDataEntryRequest> KpiDynamicDataEntryRequests { get; set; }
    public DbSet<KpiDynamicDataEntryRequestLibraryFileLink> KpiDynamicDataEntryRequestLibraryFileLinks { get; set; }

    // Kpi result data entry request.
    public DbSet<KpiResultDataEntryRequest> KpiResultDataEntryRequests { get; set; }
    public DbSet<KpiResultDataEntryRequestDepartmentLink> KpiResultDataEntryRequestDepartmentLinks { get; set; }
    public DbSet<KpiResultDataEntryRequestKpiLink> KpiResultDataEntryRequestKpiLinks { get; set; }

    // Kpi result data entry response.
    public DbSet<KpiResultDataEntryResponse> KpiResultDataEntryResponses { get; set; }
    public DbSet<KpiResultDataEntryResponseTransfer> KpiResultDataEntryResponseTransfers { get; set; }

    public DbSet<KpiResultDataEntryResponseTransferUserLink> KpiResultDataEntryResponseTransferUserLinks { get; set; }

    public DbSet<KpiResultDataEntryResponsePeriod> KpiResultDataEntryResponsePeriods { get; set; }
    public DbSet<KpiResultDataEntryRequestNotification> KpiResultDataEntryResponseNotifications { get; set; }

    //Users Requests
    public DbSet<UserRequest> UsersRequests { get; set; }
    public DbSet<UserRequestComment> UserRequestComments { get; set; }

    // Kpi Result Capability
    public DbSet<KpiResultCapability> KpiResultCapabilities { get; set; }
    public DbSet<KpiResultCapabilityType> KpiResultCapabilityTypes { get; set; }

    // Library
    public DbSet<LibraryFile> LibraryFiles { get; set; }
    public DbSet<KpiResultCapabilityLibraryFileLink> KpiResultCapabilityLibraryFileLinks { get; set; }
    public DbSet<KpiResultAttachment> KpiResultAttachments { get; set; }
    public DbSet<LibraryTag> LibraryTags { get; set; }
    public DbSet<LibraryFileLibraryTagLink> LibraryFilesLibraryTags { get; set; }

    // strategic plan
    public DbSet<StrategicGoal> StrategicGoals { get; set; }
    public DbSet<StrategicPerspective> StrategicPerspectives { get; set; }
    public DbSet<StrategicPerspectiveGoalsLink> StrategicPerspectiveGoalsLinks { get; set; }
    public DbSet<StrategicPillar> StrategicPillars { get; set; }
    public DbSet<StrategicPillarGoalsLink> StrategicPillarGoalsLinks { get; set; }
    public DbSet<StrategicPlan> StrategicPlans { get; set; }
    public DbSet<StrategicValue> StrategicValues { get; set; }

    // Hierarchy
    public DbSet<Department> Departments { get; set; }
    public DbSet<OrganizationType> DepartmentLevels { get; set; }
    public DbSet<DepartmentKpiLink> DepartmentKpiLinks { get; set; }
    public DbSet<OrganizationType> OrganizationTypes { get; set; }
    public DbSet<DepartmentUserLink> DepartmentUserLinks { get; set; }

    // Identity
    public DbSet<Role> Roles { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<UserClaim> UserClaims { get; set; }
    public DbSet<RoleClaim> RoleClaims { get; set; }
    public DbSet<RefreshToken> RefreshTokens { get; set; }
    public DbSet<NewUserRequest> NewUserRequests { get; set; }

    // operations
    public DbSet<Operation> Operations { get; set; }
    public DbSet<OperationExecutor> OperationExecutors { get; set; }
    public DbSet<OperationEnhancement> OperationEnhancements { get; set; }
    public DbSet<OperationEnhancementFileLink> OperationEnhancementFiles { get; set; }
    public DbSet<OperationEnhancementType> OperationEnhancementTypes { get; set; }
    public DbSet<OperationExecutor> OperationExecutorLinks { get; set; }
    public DbSet<OperationFormFileLink> OperationFormFiles { get; set; }
    public DbSet<Policy> Policies { get; set; }
    public DbSet<OperationPolicyLink> OperationPolicyLinks { get; set; }
    public DbSet<OperationSpecification> OperationSpecifications { get; set; }
    public DbSet<OperationSpecificationLink> OperationSpecificationLinks { get; set; }
    public DbSet<OperationRuleAndRegulation> RulesAndRegulations { get; set; }
    public DbSet<OperationRuleAndRegulationLink> RulesAndRegulationLinks { get; set; }
    public DbSet<OperationStrategicGoalLink> OperationStrategicGoalLinks { get; set; }
    public DbSet<OperationPartnerLink> OperationPartnersLinks { get; set; }
    public DbSet<OperationKpiLink> OperationKpiLinks { get; set; }
    public DbSet<OperationProcedureKpiLink> OperationProcedureKpiLinks { get; set; }
    public DbSet<SuccessFactor> SuccessFactors { get; set; }
    public DbSet<OperationSuccessFactorLink> SuccessFactorOperationLinks { get; set; }
    public DbSet<OperationProcedure> OperationProcedures { get; set; }
    public DbSet<OperationProcedureStep> OperationProcedureSteps { get; set; }
    public DbSet<OperationServiceLink> OperationServiceLinks { get; set; }
    public DbSet<OperationUpdateRequest> OperationUpdateRequests { get; set; }
    public DbSet<OperationUpdateRequestItem> OperationUpdateRequestItems { get; set; }
    public DbSet<OperationUpdateRequestItemType> OperationUpdateRequestItemTypes { get; set; }

    // Plans.
    public DbSet<Plan> Plans { get; set; }
    public DbSet<PlanInput> PlanInputs { get; set; }
    public DbSet<PlanResource> PlanResources { get; set; }
    public DbSet<PlanStrategicGoalLink> PlanStrategicGoalLinks { get; set; }
    public DbSet<PlanMinistryStrategicGoalLink> PlanMinistryStrategicGoalLinks { get; set; }
    public DbSet<PlanPlanInputLink> PlanInputLinks { get; set; }
    public DbSet<PlanPartnerLink> PlanPartnerLinks { get; set; }
    public DbSet<PlanPartneringDepartmentLink> PlanPartneringDepartmentLinks { get; set; }
    public DbSet<PlanOperationLink> PlanOperationLinks { get; set; }
    public DbSet<PlanKpiLink> PlanKpiLinks { get; set; }
    public DbSet<PlanTask> PlanTasks { get; set; }
    public DbSet<PlanSubtask> PlanSubtasks { get; set; }
    public DbSet<PlanSubsubtask> PlanSubsubtasks { get; set; }
    public DbSet<PlanTaskKpiLink> PlanTaskKpiLinks { get; set; }
    public DbSet<PlanTaskOperationLink> PlanTaskOperationLinks { get; set; }
    public DbSet<PlanSubsubtaskLibraryFileLink> PlanSubsubtaskLibraryFileLinks { get; set; }
    public DbSet<PlanApproval> PlanApprovals { get; set; }
    public DbSet<PlanCategory> PlanCategories { get; set; }
    public DbSet<PlanSubsubtaskApproval> PlanSubsubtaskApprovals { get; set; }
    public DbSet<PlanDependency> PlanDependencies { get; set; }
    public DbSet<PlanPolicyLink> PlanPolicyLinks { get; set; }

    // Teams
    public DbSet<Team> Teams { get; set; }
    public DbSet<TeamUserLink> TeamUserLinks { get; set; }

    // Permissions
    public DbSet<Injaz.Core.Models.DomainClasses.Permission.Permission> Permissions { get; set; }
    public DbSet<PermissionOverrider> PermissionOverriders { get; set; }
    public DbSet<PermissionUserLink> PermissionUserLinks { get; set; }
    public DbSet<PermissionGroup> PermissionGroups { get; set; }
    public DbSet<PermissionGroupUserLink> PermissionGroupUserLinks { get; set; }
    public DbSet<PermissionPermissionGroupLink> PermissionPermissionGroupLinks { get; set; }

    // Partner
    public DbSet<Partner> Partners { get; set; }

    // Partnership Contract
    public DbSet<PartnershipContract> PartnershipContracts { get; set; }
    public DbSet<PartnershipTerminationRequest> PartnershipTerminationRequests { get; set; }
    public DbSet<PartnershipPartnerEvaluation> PartnershipPartnerEvaluations { get; set; }
    public DbSet<PartnershipType> PartnershipTypes { get; set; }
    public DbSet<PartnershipScope> PartnershipScopes { get; set; }
    public DbSet<PartnershipScopeLink> PartnershipScopeLinks { get; set; }
    public DbSet<PartnershipFramework> PartnershipFrameworks { get; set; }
    public DbSet<PartnershipFrameworkLink> PartnershipFrameworkLinks { get; set; }
    public DbSet<PartnerStandard> PartnerStandards { get; set; }
    public DbSet<PartnershipPartnerStandardLink> PartnershipPartnerStandardLinks { get; set; }
    public DbSet<PartnershipPartnershipFieldLink> PartnershipPartnershipFieldLinks { get; set; }
    public DbSet<PartnerEvaluationStandard> PartnerEvaluationStandards { get; set; }
    public DbSet<PartnershipActivity> PartnershipActivities { get; set; }
    public DbSet<PartnershipActivityCommunicationTool> PartnershipActivityCommunicationTools { get; set; }
    public DbSet<PartnershipActivityPeriod> PartnershipActivityPeriods { get; set; }
    public DbSet<PartnershipActivityPeriodAttachment> PartnershipActivityPeriodAttachments { get; set; }
    public DbSet<PartnershipGoal> PartnershipGoals { get; set; }
    public DbSet<PartnershipGoalNationalAgendaLink> PartnershipGoalNationalAgendaLinks { get; set; }
    public DbSet<PartnershipGoalOperationLink> PartnershipGoalOperationLinks { get; set; }
    public DbSet<PartnershipGoalServiceLink> PartnershipGoalServiceLinks { get; set; }
    public DbSet<PartnershipGoalActivity> PartnershipGoalActivities { get; set; }
    public DbSet<PartnershipGoalActivityKpiLink> PartnershipGoalActivityKpiLinks { get; set; }
    public DbSet<PartnershipGoalInitiative> PartnershipGoalInitiatives { get; set; }
    public DbSet<NationalAgenda> NationalAgendas { get; set; }

    // Capability
    public DbSet<Capability> Capabilities { get; set; }
    public DbSet<CapabilityType> CapabilityTypes { get; set; }
    public DbSet<CapabilityKpiLink> CapabilityKpiLinks { get; set; }
    public DbSet<CapabilityLibraryFileLink> CapabilityLibraryFileLinks { get; set; }

    // Tournament
    public DbSet<Tournament> Tournaments { get; set; }
    public DbSet<Pillar> Pillars { get; set; }
    public DbSet<Standard> Standards { get; set; }
    public DbSet<Principle> Principles { get; set; }
    public DbSet<StandardKpiLink> StandardKpiLinks { get; set; }
    public DbSet<StandardCapabilityLink> StandardCapabilityLinks { get; set; }
    public DbSet<StandardLibraryFileLink> StandardLibraryFileLinks { get; set; }
    public DbSet<StandardSubtask> StandardSubtasks { get; set; }
    public DbSet<StandardSubtaskApproval> StandardSubtaskApprovals { get; set; }
    public DbSet<StandardSubtaskLibraryFileLink> StandardSubtaskLibraryFileLinks { get; set; }
    public DbSet<StandardSubtaskStandardUserLink> StandardSubtaskStandardUserLinks { get; set; }
    public DbSet<StandardTask> StandardTasks { get; set; }
    public DbSet<StandardUserLink> StandardUserLinks { get; set; }
    public DbSet<StandardSubtaskComment> StandardSubtaskComments { get; set; }

    //Service
    public DbSet<Service> Services { get; set; }
    public DbSet<ServicePartnerLink> ServicePartnerLinks { get; set; }
    public DbSet<ServiceDepartmentLink> ServiceDepartmentLinks { get; set; }
    public DbSet<ServiceKpiLink> ServiceKpiLinks { get; set; }

    // Benchmark
    public DbSet<Benchmark> Benchmarks { get; set; }
    public DbSet<BenchmarkBenchmarkRequestReasonLink> BenchmarkBenchmarkRequestReasonLinks { get; set; }
    public DbSet<BenchmarkBenchmarkSelectionReasonLink> BenchmarkBenchmarkSelectionReasonLinks { get; set; }
    public DbSet<BenchmarkKpiResultLink> BenchmarkKpiLinks { get; set; }
    public DbSet<BenchmarkOperationLink> BenchmarkOperationLinks { get; set; }
    public DbSet<BenchmarkOtherManagement> BenchmarkOtherManagements { get; set; }
    public DbSet<BenchmarkRequestReason> BenchmarkRequestReasons { get; set; }
    public DbSet<BenchmarkSelectionReason> BenchmarkSelectionReasons { get; set; }
    public DbSet<BenchmarkStrategicGoalLink> BenchmarkStrategicGoalLinks { get; set; }
    public DbSet<BenchmarkVisitor> BenchmarkVisitors { get; set; }
    public DbSet<BenchmarkLibraryFileLink> BenchmarkLibraryFileLinks { get; set; }


    //Improvement Opportunity
    public DbSet<ImprovementOpportunity> ImprovementOpportunities { get; set; }
    public DbSet<ImprovementOpportunityStandardLink> ImprovementOpportunityStandardLinks { get; set; }
    public DbSet<ImprovementOpportunityPrincipleLink> ImprovementOpportunityPrincipleLinks { get; set; }
    public DbSet<ImprovementOpportunityInputSource> ImprovementOpportunityInputSources { get; set; }
    public DbSet<ImprovementOpportunityInputSourceLink> ImprovementOpportunityInputSourceLinks { get; set; }

    //Innovations
    public DbSet<Innovation> Innovations { get; set; }
    public DbSet<Innovator> Innovators { get; set; }
    public DbSet<Activity> Activities { get; set; }
    public DbSet<TrainingProgram> TrainingPrograms { get; set; }
    public DbSet<Award> Awards { get; set; }
    public DbSet<InnovationActivityLink> InnovationActivityLinks { get; set; }

    // Statistical report
    public DbSet<StatisticalReport> StatisticalReports { get; set; }
    public DbSet<StatisticalReportCategory> StatisticalReportCategories { get; set; }
    public DbSet<StatisticalReportCategoryDepartmentLink> StatisticalReportCategoriesDepartmentLinks { get; set; }
    public DbSet<StatisticalReportCategoryResult> StatisticalReportCategoryResults { get; set; }
    public DbSet<StatisticalReportCategoryResultAttachment> StatisticalReportCategoryResultAttachments { get; set; }

    // Risk
    public DbSet<Risk> Risks { get; set; }
    public DbSet<RiskCategory> RiskCategories { get; set; }
    public DbSet<RiskImpact> RiskImpacts { get; set; }
    public DbSet<RiskManagementProcedure> RiskManagementProcedures { get; set; }
    public DbSet<RiskManagementStrategy> RiskManagementStrategies { get; set; }
    public DbSet<RiskOperationLink> RiskOperationLinks { get; set; }
    public DbSet<RiskPlanLink> RiskPlanLinks { get; set; }
    public DbSet<RiskProbability> RiskProbabilities { get; set; }
    public DbSet<RiskStrategicGoalLink> RiskStrategicGoalLinks { get; set; }
    public DbSet<RiskManagementProcedureLibraryFileLink> RiskManagementProcedureLibraryFileLinks { get; set; }
    public DbSet<RiskAcceptanceLevelCategory> RiskAcceptanceLevelCategories { get; set; }

    // Evaluation
    public DbSet<Evaluation> Evaluations { get; set; }
    public DbSet<EvaluationStandard> EvaluationStandards { get; set; }
    public DbSet<EvaluationInstance> EvaluationInstances { get; set; }
    public DbSet<EvaluationStandardRecord> EvaluationStandardRecords { get; set; }
    public DbSet<EvaluationScoreBand> EvaluationScoreBands { get; set; }
    public DbSet<EvaluationEntityLink> EvaluationEntityLinks { get; set; }

    public DbSet<Year> Years { get; set; }
    public DbSet<GovernmentStrategicGoal> GovernmentStrategicGoals { get; set; }
    public DbSet<Notification> Notifications { get; set; }
    public DbSet<NotificationUserLink> NotificationUserLinks { get; set; }
    public DbSet<AppSetting> AppSettings { get; set; }
    public DbSet<LinkedApplication> LinkedApplications { get; set; }
    public DbSet<Email> Emails { get; set; }
    public DbSet<FlowTransaction> ApprovalFlowTransactions { get; set; }
    public DbSet<MinistryStrategicGoal> MinistryStrategicGoals { get; set; }
    public DbSet<MainOperationOwner> MainOperationOwners { get; set; }
    public DbSet<ServiceProviderChannel> ServiceProviderChannels { get; set; }
    public DbSet<ServiceDeliveryChannel> ServiceDeliveryChannels { get; set; }
    public DbSet<ServiceClientCategory> ServiceClientCategories { get; set; }
    public DbSet<PlanFuturePlan> PlanFuturePlans { get; set; }

    public DbSet<PlanPlanFuturePlanLink> PlanPlanFuturePlanLinks { get; set; }
    public DbSet<ServiceServiceProviderChannelLink> ServiceServiceProviderChannelLinks { get; set; }
    public DbSet<ServiceServiceDeliveryChannelLink> ServiceServiceDeliveryChannelLinks { get; set; }
    public DbSet<ServiceServiceClientCategoryLink> ServiceServiceClientCategoryLinks { get; set; }
    public DbSet<BenchmarkImprovementOpportunityLink> BenchmarkImprovementOpportunityLinks { get; set; }
    public DbSet<OperationProcedureParentChildLink> OperationProcedureParentChildLinks { get; set; }

    // integrate with HR system
    public DbSet<UserHrNotification> UserHrNotifications { get; set; }
}
