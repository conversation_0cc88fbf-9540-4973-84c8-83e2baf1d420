using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("benchmark_benchmark_request_reason_links")]
public class BenchmarkBenchmarkRequestReasonLink : BaseModel
{
    [Column("benchmark_id", Order = 0)] public Guid BenchmarkId { get; set; }

    [Column("reason_id", Order = 1)] public Guid ReasonId { get; set; }

    [ForeignKey("BenchmarkId")]
    [InverseProperty("RequestReasonLinks")]
    public virtual Benchmark Benchmark { get; set; }

    [ForeignKey("ReasonId")]
    [InverseProperty("BenchmarkLinks")]
    public virtual BenchmarkRequestReason Reason { get; set; }
}