using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("benchmark_strategic_goal_links")]
public class BenchmarkStrategicGoalLink : BaseModel
{
    [Column("benchmark_id", Order = 0)] public Guid BenchmarkId { get; set; }

    [Column("goal_id", Order = 1)] public Guid GoalId { get; set; }

    [ForeignKey("BenchmarkId")]
    [InverseProperty("GoalLinks")]
    public virtual Benchmark Benchmark { get; set; }

    [ForeignKey("GoalId")]
    [InverseProperty("BenchmarkLinks")]
    public virtual StrategicGoal Goal { get; set; }
}