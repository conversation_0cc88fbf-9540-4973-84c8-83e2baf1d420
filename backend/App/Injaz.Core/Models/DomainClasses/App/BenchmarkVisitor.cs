using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("benchmark_visitor")]
public class BenchmarkVisitor : CreatableModel<User>
{
    [Column("benchmark_id")] public Guid BenchmarkId { get; set; }

    [Column("employee_number")] public string EmployeeNumber { get; set; }

    [Column("rank")] public string Rank { get; set; }

    [Column("full_name")] public string FullName { get; set; }

    [Column("department")] public string Department { get; set; }

    [Column("employment_title")] public string EmploymentTitle { get; set; }

    [Column("description")] public string Description { get; set; }

    [Column("phone")] public string Phone { get; set; }

    [Column("email")] public string Email { get; set; }

    [ForeignKey("BenchmarkId")]
    [InverseProperty("Visitors")]
    public virtual Benchmark Benchmark { get; set; }
}