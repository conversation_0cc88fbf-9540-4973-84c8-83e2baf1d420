using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;

[Table("capacity_time_dimensions")]
public class TimeDimension : ModifiableModel<User>
{
    [Column("year")]
    public int Year { get; set; }

    [Column("month_number")]
    public int MonthNumber { get; set; }

    public virtual ICollection<ServiceChannelDemand> ServiceChannelDemands { get; set; } = new List<ServiceChannelDemand>();

    public virtual ICollection<CenterMonthlyDemand> CenterMonthlyDemands { get; set; } = new List<CenterMonthlyDemand>();

    public virtual ICollection<ServiceMonthlyDemand> ServiceMonthlyDemands { get; set; } = new List<ServiceMonthlyDemand>();

    public virtual ICollection<TimeDimensionEntryPeriod> DataEntryPeriods { get; set; } = new List<TimeDimensionEntryPeriod>();
}
