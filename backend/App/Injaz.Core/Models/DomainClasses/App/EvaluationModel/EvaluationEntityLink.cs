using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.EvaluationModel;

[Table("evaluation_entity_links")]
public class EvaluationEntityLink : Model
{
    [Column("entity_id", Order = 0)] public Guid EntityId { get; set; }

    [Column("evaluation_id", Order = 1)] public Guid EvaluationId { get; set; }

    [ForeignKey(nameof(EvaluationId))]
    [InverseProperty(nameof(EvaluationModel.Evaluation.EntityLinks))]
    public virtual Evaluation Evaluation { get; set; }
}
