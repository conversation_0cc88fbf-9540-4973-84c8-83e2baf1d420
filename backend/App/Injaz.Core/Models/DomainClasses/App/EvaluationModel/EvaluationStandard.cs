using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.EvaluationModel;

[Table("evaluation_standards")]
public class EvaluationStandard : Model
{
    [Column("name_ar")] public string NameAr { get; set; }

    [Column("name_en")] public string NameEn { get; set; }

    [Column("target")] public double Target { get; set; }

    [Column("evaluation_id")] public Guid EvaluationId { get; set; }

    [ForeignKey(nameof(EvaluationId))]
    [InverseProperty(nameof(EvaluationModel.Evaluation.Standards))]
    public virtual Evaluation Evaluation { get; set; }
}
