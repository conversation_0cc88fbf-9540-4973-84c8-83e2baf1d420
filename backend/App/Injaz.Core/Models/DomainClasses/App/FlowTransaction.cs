using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("flow_transactions")]
public class FlowTransaction : Model
{
    [Column("user_id")] public Guid UserId { get; set; }

    [Column("entity_id")] public Guid EntityId { get; set; }

    [Column("department_id")] public Guid? DepartmentId { get; set; }

    [Column("value")] [MaxLength(128)] public string Value { get; set; }

    [Column("label")] [MaxLength(512)] public string Label { get; set; }

    [Column("note")] public string Note { get; set; }

    [ForeignKey(nameof(UserId))] public virtual User User { get; set; }
    [ForeignKey(nameof(DepartmentId))] public virtual Department Department { get; set; }
}
