using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("government_strategic_goals")]
public class GovernmentStrategicGoal: ModifiableModel<User>
{
    [Column("name_ar")]
    [MaxLength(1024)]
    public string NameAr { get; set; }

    [Column("name_en")]
    [MaxLength(1024)]
    public string NameEn { get; set; }

    [Column("from_year")] public int? FromYear { get; set; }

    [Column("to_year")] public int? ToYear { get; set; }

    public ICollection<Plan> Plans { get; set; }
}
