using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("improvement_opportunity_assigned_department_links")]
public class ImprovementOpportunityAssignedDepartmentLink : BaseModel
{
    [Column("improvement_opportunity_id", Order = 0)]
    public Guid ImprovementOpportunityId { get; set; }

    [Column("assigned_department_id", Order = 1)]
    public Guid AssignedDepartmentId { get; set; }


    [ForeignKey("ImprovementOpportunityId")]
    public virtual ImprovementOpportunity ImprovementOpportunity { get; set; }

    [ForeignKey("AssignedDepartmentId")] public virtual Department AssignedDepartment { get; set; }
}
