using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("improvement_opportunity_assigned_team_links")]
public class ImprovementOpportunityAssignedTeamLink
{
    [Column("improvement_opportunity_id", Order = 0)]
    public Guid ImprovementOpportunityId { get; set; }

    [Column("assigned_team_id", Order = 1)]
    public Guid AssignedTeamId { get; set; }


    [ForeignKey("ImprovementOpportunityId")]
    public virtual ImprovementOpportunity ImprovementOpportunity { get; set; }

    [ForeignKey("AssignedTeamId")] public virtual Team AssignedTeam { get; set; }
}
