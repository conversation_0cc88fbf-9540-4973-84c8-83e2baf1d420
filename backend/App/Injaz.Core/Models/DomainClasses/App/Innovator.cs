using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("innovators")]
public class Innovator : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }
    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }
    [Column("employee_number")]  [MaxLength(32)] public string EmployeeNumber { get; set; }
    [Column("rank")] [MaxLength(256)] public string Rank { get; set; }
    [Column("has_a_logo")] public int HasALogo { get; set; }

    public virtual ICollection<Innovation> Innovations { get; set; }
    public virtual ICollection<TrainingProgram> TrainingPrograms { get; set; }
    public virtual ICollection<Award> Awards { get; set; }
}
