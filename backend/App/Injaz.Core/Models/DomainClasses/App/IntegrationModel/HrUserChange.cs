using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Injaz.Core.Models.DomainClasses.App.IntegrationModel;

// this model serve the integration with the HR system, it notify the admin when user get transferred or terminated form the server

[Table("user_hr_notifications")]
public class UserHrNotification
{
    [Key]
    [Column("id")]
    public Guid Id { get; set; }

    [Column("action_date")]
    public DateTime ActionDate { get; set; }

    [Column("type")]
    public string Type { get; set; }

    [Column("message")]
    public string Message { get; set; }

    [Column("employee_number")]
    public int EmployeeNumber { get; set; }

    [Column("employee_name")]
    public string EmployeeName { get; set; }

    [Column("is_pending")]
    public bool IsPending { get; set; }

    [Column("finalized_by")]
    public Guid? FinalizedBy { get; set; }

    [Column("finalized_date")]
    public DateTime? FinalizedDateTime { get; set; }

    [Column("admin_note")]
    public string AdminNote { get; set; }

    [Column("user_id")]
    public Guid? UserId { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("UserHrNotifications")]
    public virtual User User { get; set; }

    public const string TypeTermination = "termination";
    public const string TypeTransfer = "transfer";
}
