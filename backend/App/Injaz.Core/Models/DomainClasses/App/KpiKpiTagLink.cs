using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("kpi_kpi_tag_links")]
public class KpiKpiTagLink
{
    [Column("kpi_id", Order = 0)] public Guid KpiId { get; set; }

    [Column("tag_id", Order = 1)] public Guid TagId { get; set; }

    [ForeignKey("KpiId")]
    [InverseProperty("TagLinks")]
    public virtual Kpi Kpi { get; set; }

    [ForeignKey("TagId")]
    [InverseProperty("KpiLinks")]
    public virtual KpiTag Tag { get; set; }
}
