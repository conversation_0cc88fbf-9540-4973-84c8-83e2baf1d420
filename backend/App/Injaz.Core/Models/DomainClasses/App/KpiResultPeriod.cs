using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Evaluate.Attributes;
using Injaz.Core.Evaluate.Interfaces;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("kpi_result_periods")]
[Evaluate("kpi_result_period")]
public class KpiResultPeriod : Model, IEvaluatableEntity
{
    [Column("kpi_result_id")] public Guid KpiResultId { get; set; }

    [Column("period")] public int Period { get; set; }

    [Column("a")] public double? A { get; set; }

    [Column("b")] public double? B { get; set; }

    [Column("target")] public double? Target { get; set; }

    [Column("is_approved")] public int IsApproved { get; set; }

    [Column("supervisor_note")]
    [MaxLength(8192)]
    public string SupervisorNote { get; set; }

    [Column("leadership_directive")]
    [MaxLength(8192)]
    public string LeadershipDirective { get; set; }

    [Column("result_analysis")] public string ResultAnalysis { get; set; }

    [Column("improvement_procedure")] public string ImprovementProcedure { get; set; }

    [Column("improvement_procedure_completion_percentage")]
    public string ImprovementProcedureCompletionPercentage { get; set; }

    [Column("improvement_procedure_expected_completion_date")]
    public DateTime? ImprovementProcedureExpectedCompletionDate { get; set; }

    [ForeignKey("KpiResultId")]
    [InverseProperty("Periods")]
    public virtual KpiResult KpiResult { get; set; }

    public virtual ICollection<KpiResultCapability> Capabilities { get; set; }
    public virtual ICollection<KpiResultPeriodBreakdown> Breakdowns { get; set; }
}
