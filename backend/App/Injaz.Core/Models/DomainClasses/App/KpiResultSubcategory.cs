using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("kpi_result_subcategories")]
public class KpiResultSubcategory : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("category_id")] public Guid CategoryId { get; set; }

    [ForeignKey(nameof(CategoryId))]
    [InverseProperty(nameof(KpiResultCategory.Subcategories))]
    public virtual KpiResultCategory Category { get; set; }

    public virtual ICollection<KpiResultBreakdown> Breakdowns { get; set; }
}
