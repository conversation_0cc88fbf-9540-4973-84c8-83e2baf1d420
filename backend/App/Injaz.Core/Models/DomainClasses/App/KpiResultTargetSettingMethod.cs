using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("kpi_result_target_setting_methods")]
public class KpiResultTargetSettingMethod : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("show_extra_target_fields")] public bool ShowExtraTargetFields { get; set; }

    public virtual ICollection<KpiResult> KpiResults { get; set; }
}
