using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using Injaz.Core.Models.DomainClasses.App.RiskModel;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("library_files")]
public class LibraryFile : ModifiableModel<User>
{
    public LibraryFile()
    {
        CreationTime = DateTime.UtcNow;
        LatestModificationTime = DateTime.UtcNow;
    }

    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("owner_id")] public Guid OwnerId { get; set; }

    [Column("link")] [MaxLength(8192)] public string Link { get; set; }

    [Column("file_name")] public string FileName { get; set; }

    [Column("file_size")] public int? FileSize { get; set; }

    [Column("content_type")] public string ContentType { get; set; }

    [Column("latest_modification_time")] public DateTime LatestModificationTime { get; set; }

    [Column("scopes")] public string[] Scopes { get; set; }

    [ForeignKey("OwnerId")] public virtual User Owner { get; set; }

    // public virtual ICollection<KpiResultPeriodLibraryFileLink> KpiResultPeriodLinks { get; set; }
    public virtual ICollection<KpiResultCapabilityLibraryFileLink> KpiResultCapabilityLinks { get; set; }
    public virtual ICollection<LibraryFileLibraryTagLink> TagLinks { get; set; }
    public virtual ICollection<CapabilityLibraryFileLink> CapabilityLinks { get; set; }
    public virtual ICollection<BenchmarkLibraryFileLink> BenchmarkLinks { get; set; }
    public virtual ICollection<KpiBenchmark> KpiBenchmarks { get; set; }
    public virtual ICollection<Operation> Operations { get; set; }
    public virtual ICollection<OperationEnhancement> EnhancementsAsFlowChartFile { get; set; }
    public virtual ICollection<OperationEnhancement> EnhancementsAsBusinessModelFiles { get; set; }
    public virtual ICollection<UserRequest> UserRequests { get; set; }
    public virtual ICollection<PlanSubsubtaskLibraryFileLink> PlanSubsubtaskLinks { get; set; }
    public virtual ICollection<Tournament> Tournaments { get; set; }
    public virtual ICollection<Standard> StandardTeamFormations { get; set; }
    public virtual ICollection<Standard> StandardPresentations { get; set; }
    public virtual ICollection<OperationProcedure> OperationProcedures { get; set; }
    public virtual ICollection<StandardLibraryFileLink> StandardLinks { get; set; }
    public virtual ICollection<ImprovementOpportunityLibraryFileLink> ImprovementOpportunityLinks { get; set; }
    public virtual ICollection<StandardSubtaskLibraryFileLink> StandardSubtaskLinks { get; set; }

    public virtual ICollection<KpiDynamicDataEntryRequestLibraryFileLink> KpiDynamicDataEntryRequestLinks
    {
        get;
        set;
    }

    public virtual ICollection<OperationProcedureStep> OperationProcedureSteps { get; set; }
    public virtual ICollection<RiskManagementProcedureLibraryFileLink> RiskManagementProcedureLinks { get; set; }
    public virtual ICollection<PartnershipContract> PartnershipContracts { get; set; }
}
