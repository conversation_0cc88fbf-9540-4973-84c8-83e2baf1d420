using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using Injaz.Core.Models.DomainClasses.App.RiskModel;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("operations")]
public class Operation : ModifiableModel<User>
{
    [Column("code")] [MaxLength(1024)] public string Code { get; set; }

    [Column("ministerial_code")]
    [MaxLength(1024)]
    public string MinisterialCode { get; set; }


    [Column("local_code")]
    [MaxLength(1024)]
    public string LocalCode { get; set; }

    [Column("number")] public int Number { get; set; }
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("description_ar")]
    [MaxLength(1024)]
    public string DescriptionAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("description_en")]
    [MaxLength(1024)]
    public string DescriptionEn { get; set; }

    [Column("output")] public string Output { get; set; }

    [Column("purpose")] public string Purpose { get; set; }

    [Column("sustainability_impacts")] public string[] SustainabilityImpact { get; set; }

    [Column("weight")] public double? Weight { get; set; }

    [Column("parent_id")] public Guid? ParentId { get; set; }

    // Defined auto by system.
    [Column("operation_level")] public int Level { get; set; }

    [Column("owner_department_id")] public Guid? OwnerDepartmentId { get; set; }

    // The following properties are limited to operations at the forth level.
    [Column("operation_inputs")]
    [MaxLength(1024)]
    public string Inputs { get; set; }

    [Column("input_type")]
    [MaxLength(1024)]
    public string InputType { get; set; }

    [Column("types")] public string[] Types { get; set; }


    [Column("outputs")] [MaxLength(1024)] public string Outputs { get; set; }

    [Column("output_type")]
    [MaxLength(1024)]
    public string OutputType { get; set; }

    [Column("supplier_name")]
    [MaxLength(1024)]
    public string SupplierName { get; set; }

    [Column("duration")] [MaxLength(1024)] public string Duration { get; set; }

    [Column("supplier_category")]
    [MaxLength(1024)]
    public string SupplierCategory { get; set; } // silver, bronze , golden

    [Column("technical_solutions")]
    [MaxLength(1024)]
    public string TechnicalSolutions { get; set; }

    [Column("main_flow_chart_id")] public Guid? MainFLowChartId { get; set; }

    [Column("danger_number")] public string DangerNumber { get; set; }

    [Column("beneficiaries")] public string[] Beneficiaries { get; set; }

    [Column("terminologies")] public string Terminologies { get; set; }

    [Column("version")] public string? Version { get; set; }

    [ForeignKey("MainFLowChartId")]
    [InverseProperty("Operations")]
    public virtual LibraryFile MainFlowChartFile { get; set; }

    [ForeignKey("ParentId")] public virtual Operation Parent { get; set; }

    [ForeignKey("OwnerDepartmentId")] public virtual Department OwnerDepartment { get; set; }

     [Column("main_operation_owner_id")] public Guid? MainOperationOwnerId { get; set; }

    [ForeignKey("MainOperationOwnerId")]
    [InverseProperty("Operations")]
    public virtual MainOperationOwner MainOperationOwner { get; set; }

    public virtual ICollection<OperationSuccessFactorLink> SuccessFactorOperationLinks { get; set; }
    public virtual ICollection<OperationPartnerLink> PartnerLinks { get; set; }
    public virtual ICollection<Operation> Children { get; set; }
    public virtual ICollection<OperationStrategicGoalLink> GoalLinks { get; set; }
    public virtual ICollection<OperationKpiLink> KpiLinks { get; set; }
    public virtual ICollection<OperationExecutor> Executors { get; set; }
    public virtual ICollection<OperationFormFileLink> FormFileLinks { get; set; }
    public virtual ICollection<OperationPolicyLink> PolicyLinks { get; set; }
    public virtual ICollection<OperationSpecificationLink> SpecificationLinks { get; set; }
    public virtual ICollection<OperationEnhancement> Enhancements { get; set; }
    public virtual ICollection<OperationRuleAndRegulationLink> RuleAndRegulationLinks { get; set; }
    public virtual ICollection<UserRequest> UserRequests { get; set; }
    public virtual ICollection<PlanOperationLink> PlanLinks { get; set; }
    public virtual ICollection<BenchmarkOperationLink> BenchmarkLinks { get; set; }
    public virtual ICollection<PlanTaskOperationLink> PlanTaskLinks { get; set; }
    public virtual ICollection<OperationProcedure> Procedures { get; set; }
    public virtual ICollection<ImprovementOpportunityOperationLink> ImprovementOpportunityLinks { get; set; }
    public virtual ICollection<PartnershipGoalOperationLink> PartnershipGoalLinks { get; set; }
    public virtual ICollection<RiskOperationLink> RiskLinks { get; set; }
    public virtual ICollection<OperationServiceLink> ServiceLinks { get; set; }
    public virtual ICollection<OperationUpdateRequest> UpdateRequests { get; set; }


    public const string OutputTypeTask = "task"; // عملية خدمة
    public const string OutputTypeAdministrative = "administrative"; // عملية إدارية
    public const string OutputTypeField = "field"; // عملية ميدانية

    public const string SupplierTypeSilver = "silver";
    public const string SupplierTypeBronze = "bronze";
    public const string SupplierTypeGolden = "golden";

    public const string TypeManual = "manual";
    public const string TypeElectronic = "electronic";
    public const string TypeSmart = "smart";

    public const string TypeEmployee = "employee";
    public const string TypeCustomer = "customer";
    public const string TypeDepartment = "department";
    public const string TypePublic = "public";
    public const string TypePrivate = "private";

    // SustainabilityImpacts
    public const string ImpactEnvironmental = "environmental";
    public const string ImpactEconomical = "economical";
    public const string ImpactSocial = "social";
}
