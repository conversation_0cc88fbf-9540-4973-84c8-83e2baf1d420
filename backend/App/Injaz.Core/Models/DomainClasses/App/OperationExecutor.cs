using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("operation_executor")]
public class OperationExecutor : CreatableModel<User>
{
    [Column("operation_id")] public Guid OperationId { get; set; }

    [Column("type")] [MaxLength(32)] public string Type { get; set; }

    [Column("name")] [MaxLength(1024)] public string Name { get; set; }

    [ForeignKey("OperationId")]
    [InverseProperty("Executors")]
    public virtual Operation Operation { get; set; }

    public const string TypeEmployee = "employee";
    public const string TypeCustomer = "customer";
    public const string TypeContractor = "contractor";
    public const string TypePartner = "partner";
}
