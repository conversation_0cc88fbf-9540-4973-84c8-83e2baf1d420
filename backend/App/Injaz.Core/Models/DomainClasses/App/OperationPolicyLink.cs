using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("operation_policy_links")]
public class OperationPolicyLink: BaseModel
{
    [Column("operation_id", Order = 0)]
    public Guid OperationId { get; set; }

    [Column("policy_id", Order = 1)]
    public Guid PolicyId { get; set; }

    [ForeignKey("OperationId")]
    public virtual Operation Operation { get; set; }

    [ForeignKey("PolicyId")]
    public virtual Policy Policy { get; set; }
}
