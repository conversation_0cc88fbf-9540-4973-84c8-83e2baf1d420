using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("operation_procedure_kpi_links")]
public class OperationProcedureKpiLink : BaseModel
{
    [Column("procedure_id", Order = 0)] public Guid ProcedureId { get; set; }

    [Column("kpi_id", Order = 1)] public Guid KpiId { get; set; }

    [ForeignKey("ProcedureId")]
    [InverseProperty("KpiLinks")]
    public virtual OperationProcedure Procedure { get; set; }

    [ForeignKey("KpiId")]
    [InverseProperty("OperationProcedureLinks")]
    public virtual Kpi Kpi { get; set; }
}