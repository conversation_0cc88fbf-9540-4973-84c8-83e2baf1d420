using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("operation_procedure_parent_child_links")]
public class OperationProcedureParentChildLink : BaseModel
{
    [Column("parent_procedure_id", Order = 0)]
    public Guid ParentProcedureId { get; set; }

    [ForeignKey(nameof(ParentProcedureId))]
    public virtual OperationProcedure ParentProcedure { get; set; }

    [Column("child_procedure_id", Order = 1)]
    public Guid ChildProcedureId { get; set; }

    [ForeignKey(nameof(ChildProcedureId))] public virtual OperationProcedure ChildProcedure { get; set; }
}
