using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("operation_rules_and_regulations")]
public class OperationRuleAndRegulation : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    public virtual ICollection<OperationRuleAndRegulationLink> OperationLinks { get; set; }
}
