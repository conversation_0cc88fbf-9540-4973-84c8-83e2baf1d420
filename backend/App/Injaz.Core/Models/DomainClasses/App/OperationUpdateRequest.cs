using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Nodes;
using Injaz.Core.Flow.Attributes;
using Injaz.Core.Flow.Interfaces;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("operation_update_requests")]
[FlowEntityLabel("operation-update-request")]
public class OperationUpdateRequest : CreatableModel<User>, IFlowEntity
{
    [Column("operation_id")] public Guid OperationId { get; set; }

    [Column("old_data")] public JsonNode OldData { get; set; }

    [Column("new_data")] public JsonNode NewData { get; set; }

    [MaxLength(128)]
    [Column("flow_state")]
    public string FlowState { get; set; }

    [ForeignKey(nameof(OperationId))]
    [InverseProperty(nameof(Models.DomainClasses.App.Operation.UpdateRequests))]
    public virtual Operation Operation { get; set; }

    [Foreign<PERSON>ey(nameof(CreatedById))] public virtual User CreatedBy { get; set; }

    public virtual ICollection<OperationUpdateRequestItem> Items { get; set; }
}
