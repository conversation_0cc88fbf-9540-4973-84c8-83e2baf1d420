using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using Injaz.Core.Models.DomainClasses.App.ServiceModel;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.PartnerModel;

[Table("partners")]
public class Partner : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }
    [Column("coordinator")] public string Coordinator { get; set; }
    [Column("contact_number")] public string ContactNumber { get; set; }
    [Column("email")] public string Email { get; set; }
    public virtual ICollection<OperationPartnerLink> OperationLinks { get; set; }
    public virtual ICollection<OperationExecutor> OperationExecutors { get; set; }
    public virtual ICollection<Benchmark> Benchmarks { get; set; }
    public virtual ICollection<PlanPartnerLink> PlanLinks { get; set; }
    public virtual ICollection<ServicePartnerLink> ServiceLinks { get; set; }
    public virtual ICollection<PartnershipContract> PartnershipContracts { get; set; }

    [ForeignKey(nameof(CreatedById))] public virtual User CreatedBy { get; set; }

    public const string PartnerTypePartnersWithPartnershipContract = "partners_with_partnership_contract";
    public const string PartnerTypePartnersWithoutPartnershipContract = "partners_without_partnership_contract";
}
