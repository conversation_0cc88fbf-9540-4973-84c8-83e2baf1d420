using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Attributes;
using Injaz.Core.Flow.Attributes;
using Injaz.Core.Flow.Interfaces;
using Injaz.Core.Models.DomainClasses.App.PartnerModel;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.PartnershipModel;

[Table("partnership_contracts")]
[FlowEntityLabel("partnership_contract")]
public class PartnershipContract : ModifiableModel<User>, IFlowEntity
{
    [Column("title_ar")] [MaxLength(1024)] public string TitleAr { get; set; }

    [Column("title_en")] [MaxLength(1024)] public string TitleEn { get; set; }

    [Column("start_date")] public DateTime StartDate { get; set; }

    [Column("time_frame")] public string TimeFrame { get; set; }

    [Column("is_renewable")] public bool IsRenewable { get; set; }

    [Column("is_expiry_notification_sent")]
    public bool IsExpiryNotificationSent { get; set; }

    [Column("partner_capabilities")] public string PartnerCapabilities { get; set; }

    [Column("resources")] public string Resources { get; set; }

    [Column("purpose")] public string Purpose { get; set; }

    [Column("roles_and_responsibilities")] public string RolesAndResponsibilities { get; set; }

    [Column("dealing_with_conflicts")] public string DealingWithConflicts { get; set; }

    [Column("arrangements")] public string Arrangements { get; set; }

    [Column("current_situation")] public string CurrentSituation { get; set; }

    [Column("membership")] public string Membership { get; set; }

    [Column("flow_state")] public string FlowState { get; set; }

    [Column("partnership_scope")]
    [MaxLength(128)]
    public string PartnershipScope { get; set; }

    [Column("partner_id")] public Guid? PartnerId { get; set; }
    [ForeignKey(nameof(PartnerId))] public virtual Partner Partner { get; set; }

    [Column("other_partner")]
    [MaxLength(1024)]
    public string OtherPartner { get; set; }

    [Column("partnership_type_id")] public Guid? PartnershipTypeId { get; set; }

    [ForeignKey(nameof(PartnershipTypeId))]
    public virtual PartnershipType PartnershipType { get; set; }

    [Column("department_id")] public Guid DepartmentId { get; set; }

    [ForeignKey(nameof(DepartmentId))] public virtual Department Department { get; set; }

    [Column("sector_type")]
    [MaxLength(32)]
    public string SectorType { get; set; }

    [Column("is_evaluation_locked")] public bool IsEvaluationLocked { get; set; } = true;

    [Column("agreement_file_id")] public Guid? AgreementFileId { get; set; }

    [ForeignKey(nameof(AgreementFileId))]
    [InverseProperty("PartnershipContracts")]
    public virtual LibraryFile AgreementFile { get; set; }

    [Column("terms")] [DbJsonConverter] public PartnershipContractTerm[] Terms { get; set; }

    public virtual ICollection<PartnershipActivity> Activities { get; set; }
    public virtual ICollection<PartnershipTerminationRequest> TerminationRequests { get; set; }
    public virtual ICollection<PartnershipGoal> Goals { get; set; }
    public virtual ICollection<PartnershipScopeLink> PartnershipScopeLinks { get; set; }
    public virtual ICollection<PartnershipFrameworkLink> PartnershipFrameworkLinks { get; set; }
    public virtual ICollection<PartnershipPartnerStandardLink> PartnershipPartnerStandardLinks { get; set; }
    public virtual ICollection<PartnershipPartnershipFieldLink> PartnershipPartnershipFieldLinks { get; set; }
    public virtual ICollection<PartnershipPartnerEvaluation> PartnershipPartnerEvaluations { get; set; }

    // Statuses
    public const string StatusNew = "new";
    public const string StatusManagerFirstReview = "manager_first_review";
    public const string StatusFirstReview = "first_review";
    public const string StatusSecondReview = "second_review";
    public const string StatusActive = "active";
    public const string StatusRejected = "rejected";
    public const string StatusRejectedFromFirstReview = "rejected_from_first_review";
    public const string StatusTerminated = "terminated";

    // Time Frames
    public const string TimeFrameAnnual = "annual";
    public const string TimeFrameSemiAnnual = "semi_annual";
    public const string TimeFrameQuarter = "quarter";
    public const string TimeFrameMonth = "month";
}
