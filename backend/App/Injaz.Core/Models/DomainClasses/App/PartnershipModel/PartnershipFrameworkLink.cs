using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.PartnershipModel;

[Table("partnership_framework_links")]
public class PartnershipFrameworkLink : BaseModel
{
    [Column("partnership_contract_id", Order = 0)]
    public Guid PartnershipContractId { get; set; }

    [Column("partnership_framework_id", Order = 1)]
    public Guid PartnershipFrameworkId { get; set; }

    [ForeignKey(nameof(PartnershipContractId))]
    public virtual PartnershipContract PartnershipContract { get; set; }

    [ForeignKey(nameof(PartnershipFrameworkId))]
    public virtual PartnershipFramework PartnershipFramework { get; set; }
}
