using System;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Models.DomainClasses.App.ServiceModel;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.PartnershipModel;

[Table("partnership_goal_service_links")]
public class PartnershipGoalServiceLink : BaseModel
{
    [Column("partnership_goal_id", Order = 0)]
    public Guid PartnershipGoalId { get; set; }

    [ForeignKey(nameof(PartnershipGoalId))]
    public virtual PartnershipGoal PartnershipGoal { get; set; }

    [Column("service_id", Order = 1)] public Guid ServiceId { get; set; }
    [ForeignKey(nameof(ServiceId))] public virtual Service Service { get; set; }
}
