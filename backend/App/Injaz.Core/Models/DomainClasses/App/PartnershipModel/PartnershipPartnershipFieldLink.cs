using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.PartnershipModel;
[Table("partnership_partnership_field_links")]
public class PartnershipPartnershipFieldLink : BaseModel
{
    [Column("partnership_contract_id", Order = 0)]
    public Guid PartnershipContractId { get; set; }

    [ForeignKey(nameof(PartnershipContractId))]
    public virtual PartnershipContract PartnershipContract { get; set; }

    [Column("partnership_field_id", Order = 1)]
    public Guid PartnershipFieldId { get; set; }

    [Foreign<PERSON>ey(nameof(PartnershipFieldId))]
    public virtual PartnershipField PartnershipField { get; set; }
}
