using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.PartnershipModel;

[Table("partnership_scopes")]
public class PartnershipScope : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("partnership_field_id")] public Guid? PartnershipFieldId { get; set; }

    [Foreign<PERSON>ey(nameof(PartnershipFieldId))]
    public virtual PartnershipField? PartnershipField { get; set; }

    public virtual ICollection<PartnershipScopeLink> PartnershipContractLinks { get; set; }
}
