using System;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Flow.Attributes;
using Injaz.Core.Flow.Interfaces;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.PartnershipModel;

[Table("partnership_termination_requests")]
[FlowEntityLabel("partnership_termination_request")]
public class PartnershipTerminationRequest : ModifiableModel<User>, IFlowEntity
{
    [Column("termination_date")] public DateTime TerminationDate { get; set; }

    [Column("termination_reason")] public string TerminationReason { get; set; }

    [Column("progress_summary")] public string ProgressSummary { get; set; }

    [Column("successes")] public string Successes { get; set; }

    [Column("lessons_learned")] public string LessonsLearned { get; set; }

    [Column("partner_notes")] public string PartnerNotes { get; set; }

    [Column("flow_state")] public string FlowState { get; set; }

    [Column("partnership_contract_id")] public Guid PartnershipContractId { get; set; }

    [ForeignKey(nameof(PartnershipContractId))]
    public virtual PartnershipContract PartnershipContract { get; set; }

    // Statuses
    public const string StatusNew = "new";
    public const string StatusFirstReview = "first_review";
    public const string StatusCompleted = "completed";
    public const string StatusRejected = "rejected";
}
