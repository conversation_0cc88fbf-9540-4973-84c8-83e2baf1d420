using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Attributes;
using Injaz.Core.Flow.Attributes;
using Injaz.Core.Flow.Interfaces;
using Injaz.Core.Flow.Services;
using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App.RiskModel;
using Injaz.Core.Models.Misc;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("plans")]
[FlowEntityLabel("plan")]
public class Plan : ModifiableModel<User>, IPlanEntity, IDefaultFlowEntity
{
    [Column("code")] [MaxLength(1024)] public string Code { get; set; }

    [Column("year")] public int Year { get; set; }

    [Column("assigned_department_id")] public Guid? AssignedDepartmentId { get; set; }

    [Column("assigned_team_id")] public Guid? AssignedTeamId { get; set; }

    [Column("assigned_user_id")] public Guid? AssignedUserId { get; set; }

    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("category_id")] public Guid CategoryId { get; set; }

    [Column("from")] public DateTime? From { get; set; }

    [Column("to")] public DateTime? To { get; set; }

    [Column("government_strategic_goal_id")]
    public Guid? GovernmentStrategicGoalId { get; set; }

    [Column("government_directions")] public string GovernmentDirections { get; set; }

    [Column("other_partners")] public string OtherPartners { get; set; }

    [Column("initiatives")] public string Initiatives { get; set; }

    [Column("flow_state")] public string FlowState { get; set; } = DefaultFlowState.Draft;

    [Column("description")]
    [MaxLength(1024)]
    public string Description { get; set; }

    [Column("work_scope")]
    [MaxLength(1024)]
    public string WorkScope { get; set; }

    [Column("implementation_requirement")]
    [MaxLength(1024)]
    public string ImplementationRequirement { get; set; }

    [Column("expected_outputs")]
    [DbJsonConverter]
    public PlanExpectedOutput[] ExpectedOutputs { get; set; }

    [Column("lessons_learned")]
    [DbJsonConverter]
    public PlanLessonLearned[] LessonsLearned { get; set; }

    [Column("challenges")]
    [DbJsonConverter]
    public PlanChallenge[] Challenges { get; set; }

    [Column("communication_processes")]
    [DbJsonConverter]
    public PlanCommunicationProcess[] CommunicationProcesses { get; set; }

    [Column("risks")] [DbJsonConverter] public PlanRisk[] Risks { get; set; }

    [Column("is_budget_allocated")] public int IsBudgetAllocated { get; set; }

    [Column("financial_requirements")]
    [DbJsonConverter]
    public PlanFinancialRequirement[] FinancialRequirements { get; set; }

    [Column("financial_stages")]
    [DbJsonConverter]
    public PlanFinancialStage[] FinancialStages { get; set; }

    [Column("expected_benefits")]
    [DbJsonConverter]
    public PlanExpectedBenefit[] ExpectedBenefits { get; set; }

    [ForeignKey("AssignedDepartmentId")]
    [InverseProperty("Plans")]
    public virtual Department AssignedDepartment { get; set; }

    [ForeignKey("AssignedTeamId")]
    [InverseProperty("Plans")]
    public virtual Team AssignedTeam { get; set; }

    [ForeignKey("AssignedUserId")]
    [InverseProperty("Plans")]
    public virtual User AssignedUser { get; set; }

    [ForeignKey("GovernmentStrategicGoalId")]
    [InverseProperty("Plans")]
    public virtual GovernmentStrategicGoal GovernmentStrategicGoal { get; set; }

    [ForeignKey("CategoryId")] public virtual PlanCategory Category { get; set; }

    [ForeignKey("CreatedById")] public virtual User CreatedBy { get; set; }

    public virtual ICollection<PlanResource> Resources { get; set; }

    public virtual ICollection<PlanStrategicGoalLink> StrategicGoalLinks { get; set; }
    public virtual ICollection<PlanMinistryStrategicGoalLink> MinistryStrategicGoalLinks { get; set; }
    public virtual ICollection<PlanPlanInputLink> InputLinks { get; set; }
    public virtual ICollection<PlanPartnerLink> PartnerLinks { get; set; }
    public virtual ICollection<PlanPartneringDepartmentLink> PartneringDepartmentLinks { get; set; }
    public virtual ICollection<PlanOperationLink> OperationLinks { get; set; }
    public virtual ICollection<PlanKpiLink> KpiLinks { get; set; }
    public virtual ICollection<PlanTask> Tasks { get; set; }
    public virtual ICollection<PlanDependency> Principals { get; set; }
    public virtual ICollection<PlanDependency> Dependencies { get; set; }
    public virtual ICollection<PlanPolicyLink> PolicyLinks { get; set; }
    public virtual ICollection<RiskPlanLink> RiskLinks { get; set; }
    public virtual ICollection<PlanPlanFuturePlanLink> FuturePlanLinks { get; set; }

    // Assignee Types
    public const string AssigneeTypeDepartment = "department";
    public const string AssigneeTypeTeam = "team";
    public const string AssigneeTypeUser = "user";
}
