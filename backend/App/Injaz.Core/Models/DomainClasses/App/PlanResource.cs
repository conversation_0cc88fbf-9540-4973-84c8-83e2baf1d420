using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("plan_resources")]
public class PlanResource : Model
{
    [Column("plan_id")] public Guid PlanId { get; set; }

    [Column("name")] [MaxLength(1024)] public string Name { get; set; }

    [Column("count")] public int Count { get; set; }

    [ForeignKey("PlanId")]
    [InverseProperty("Resources")]
    public virtual Plan Plan { get; set; }
}
