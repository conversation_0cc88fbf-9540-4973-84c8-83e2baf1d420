using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("plan_subsubtask_approvals")]
public class PlanSubsubtaskApproval : Model
{
    [Column("subsubtask_id")] public Guid SubsubtaskId { get; set; }

    [Column("approving_user_id")] public Guid ApprovingUserId { get; set; }

    [Column("type")] public string Type { get; set; }

    [Column("note")] public string Note { get; set; }

    [ForeignKey("SubsubtaskId")]
    [InverseProperty("Approvals")]
    public virtual PlanSubsubtask Subsubtask { get; set; }

    [ForeignKey("ApprovingUserId")] public virtual User ApprovingUser { get; set; }

    public const string TypeDraft = "draft";
    public const string TypeSubmitted = "submitted";
    public const string TypeApproved = "approved";
    public const string TypeRejected = "rejected";
    public const string TypeFinal = "final";
}
