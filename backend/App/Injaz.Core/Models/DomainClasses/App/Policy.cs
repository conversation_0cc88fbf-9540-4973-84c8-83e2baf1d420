using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("policies")]
public class Policy : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("attachment_name")] public string AttachmentName { get; set; }

    [Column("content_type")] public string ContentType { get; set; }

    public virtual ICollection<OperationPolicyLink> OperationPolicyLinks { get; set; }
}
