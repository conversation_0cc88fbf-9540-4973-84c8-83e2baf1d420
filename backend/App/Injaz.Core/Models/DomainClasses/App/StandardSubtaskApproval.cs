using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("standard_subtask_approvals")]
public class StandardSubtaskApproval : Model
{
    [Column("subtask_id")] public Guid SubtaskId { get; set; }

    [Column("approving_user_id")] public Guid ApprovingUserId { get; set; }

    [Column("type")] public string Type { get; set; }

    [Column("note")] public string Note { get; set; }

    [ForeignKey(nameof(SubtaskId))]
    [InverseProperty(nameof(StandardSubtask.Approvals))]
    public virtual StandardSubtask Subtask { get; set; }

    [ForeignKey(nameof(ApprovingUserId))] public virtual User ApprovingUser { get; set; }

    // The subtask has been created.
    public const string TypeDraft = "draft";

    // The subtask has been submitted by the team
    // to the tournament supervisor to be initially
    // approved for data entry.
    public const string TypeInitiallySubmitted = "initially_submitted";

    // The subtask has been approved by the tournament supervisor
    // and assigner should assign the team members.
    public const string TypeInitiallyApproved = "initially_approved";

    // The subtask has been assigned by the assigner user
    // and the team members should start working on it.
    public const string TypeAssigned = "assigned";

    // The subtask has been rejected by the tournament supervisor
    // and the team should either delete it or apply needed
    // changes.
    public const string TypeInitiallyRejected = "initially_rejected";

    // The assigned submitted the subtask to the team for
    // approval after achieving its goal.
    public const string TypeSubmitted = "submitted";

    // The team approves the achievement and sends it to
    // the tournament supervisor for final approval.
    public const string TypeApproved = "approved";

    // Either the team or the supervisor rejects the
    // the subtask, which would make the subtask go
    // back to the assignee.
    public const string TypeRejected = "rejected";

    // The tournament supervisor approves the subtask
    // and no further changes on the task is possible.
    public const string TypeFinal = "final";
}
