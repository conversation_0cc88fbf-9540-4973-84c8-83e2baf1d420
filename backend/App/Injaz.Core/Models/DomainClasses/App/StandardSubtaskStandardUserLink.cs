using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("standard_subtask_standard_user_links")]
public class StandardSubtaskStandardUserLink : BaseModel
{
    [Column("subtask_id", Order = 0)] public Guid SubtaskId { get; set; }

    [Column("standard_id", Order = 1)] public Guid StandardId { get; set; }

    [Column("user_id", Order = 2)] public Guid UserId { get; set; }

    [ForeignKey(nameof(SubtaskId))]
    [InverseProperty(nameof(StandardSubtask.StandardUserLinks))]
    public virtual StandardSubtask Subtask { get; set; }

    [ForeignKey("StandardId,UserId")]
    [InverseProperty(nameof(Injaz.Core.Models.DomainClasses.App.StandardUserLink.StandardSubtaskUserLinks))]
    public virtual StandardUserLink StandardUserLink { get; set; }
}
