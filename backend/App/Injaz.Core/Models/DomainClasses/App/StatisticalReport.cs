using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("statistical_reports")]
public class StatisticalReport : ModifiableModel<User>
{
    [Column("name_ar")] public string NameAr { get; set; }

    [Column("name_en")] public string NameEn { get; set; }

    [Column("cycle")] public string Cycle { get; set; }

    [Column("initial_year")] public int InitialYear { get; set; }

    [Column("is_published")] public int IsPublished { get; set; }

    [Column("is_decrease_best")] public int IsDecreaseBest { get; set; }

    public virtual ICollection<StatisticalReportCategory> Categories { get; set; }

    public const string CycleMonth = "month";
    public const string CycleQuarter = "quarter";
    public const string CycleSemiAnnual = "semi_annual";
    public const string CycleAnnual = "annual";
}
