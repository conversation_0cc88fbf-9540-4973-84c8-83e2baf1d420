using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("strategic_pillar_goals_links")]
public class StrategicPillarGoalsLink : BaseModel
{

    [Column("goal_id", Order = 0)]
    public Guid  GoalId { get; set; }

    [Column("pillar_id", Order = 1)]
    public Guid PillarId { get; set; }

    [ForeignKey("PillarId")]
    public virtual StrategicPillar StrategicPillar { get; set; }

    [ForeignKey("GoalId")]
    public virtual StrategicGoal StrategicGoal { get; set; }

}
