using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("users_requests")]
public class UserRequest: ModifiableModel<User>
{
    [Column("content")] public string Content { get; set; }
    [Column("is_closed")] public int IsClosed { get; set; }
    [Column("user_id")] public Guid UserId { get; set; }
    [Column("operation_id")] public Guid? OperationId { get; set; }
    [Column("kpi_id")] public Guid? KpiId { get; set; }
    [Column("library_file_id")] public Guid? LibraryFileId { get; set; }

    [ForeignKey("UserId")] public virtual User User { get; set; }
    [ForeignKey("OperationId")] public virtual Operation Operation { get; set; }
    [ForeignKey("KpiId")] public virtual Kpi Kpi { get; set; }
    [ForeignKey("LibraryFileId")] public virtual LibraryFile LibraryFile { get; set; }
    public virtual ICollection<UserRequestComment>  RequestComments { get; set; }

    // request types;
    public const string RequestTypeOperation = "operation";
    public const string RequestTypeKpi = "kpi";
    public const string RequestTypeFile = "file";
}
