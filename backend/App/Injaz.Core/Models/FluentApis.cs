using System;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Nodes;
using Injaz.Core.Abstraction;
using Injaz.Core.Attributes;
using Injaz.Core.Evaluate.Models;
using Injaz.Core.Extensions;
using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using Injaz.Core.Models.DomainClasses.App.RiskModel;
using Injaz.Core.Models.DomainClasses.App.ServiceModel;
using Injaz.Core.Models.DomainClasses.Identity;
using Injaz.Core.Models.DomainClasses.Permission;
using Injaz.Core.Models.Misc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using MNMWebApp.Models.Abstraction;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace Injaz.Core.Models;

public static class FluentApis
{
    public static void Setup(ModelBuilder builder)
    {
        // identity tables and columns mapping
        SetupIdentity(builder);

        // json converter
        ConfigureJsonConverter(builder);

        // required by dotnet core v2
        builder.Entity<User>().HasMany(x => x.Claims).WithOne(x => x.User).HasForeignKey("UserId");

        // Global Query Filters for soft delete
        builder.SetGlobalQueryFilterForDerivedTypes<CreatableModel<User>>(x => x.IsDeleted == 0);

        // app specific relations
        builder.Entity<KpiResultRequest>()
            .HasOne(x => x.KpiResult)
            .WithMany(x => x.Requests)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<KpiResultCapability>()
            .HasMany(x => x.LibraryFileLinks)
            .WithOne(x => x.KpiResultCapability)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<KpiResultPeriod>()
            .HasMany(x => x.Capabilities)
            .WithOne(x => x.KpiResultPeriod)
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<Department>()
            .HasMany(x => x.Children)
            .WithOne(x => x.ParentDepartment)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<Operation>()
            .HasMany(x => x.Children)
            .WithOne(x => x.Parent)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<Department>()
            .HasMany(x => x.OwnedKpis)
            .WithOne(x => x.OwningDepartment)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<UserRequest>()
            .HasMany(x => x.RequestComments)
            .WithOne(x => x.UserRequest)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<KpiResultDataEntryResponseTransfer>()
            .HasMany(x => x.UserLinks)
            .WithOne(x => x.Transfer)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<KpiResultPeriod>()
            .HasMany(x => x.Breakdowns)
            .WithOne(x => x.Period)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<KpiResultBreakdown>()
            .HasMany(x => x.Values)
            .WithOne(x => x.Breakdown)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<Benchmark>()
            .HasMany(x => x.KpiResultLinks)
            .WithOne(x => x.Benchmark)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<KpiResult>()
            .HasMany(x => x.BenchmarkLinks)
            .WithOne(x => x.Result)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<PermissionOverrider>()
            .HasOne(x => x.Permission)
            .WithMany(x => x.Overriders)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<StandardSubtaskStandardUserLink>()
            .HasOne(x => x.StandardUserLink)
            .WithMany(x => x.StandardSubtaskUserLinks)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Entity<KpiDynamicDataEntryRequestLibraryFileLink>()
            .HasOne(x => x.LibraryFile)
            .WithMany(x => x.KpiDynamicDataEntryRequestLinks)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<OperationProcedureParentChildLink>()
            .HasOne(link => link.ParentProcedure)
            .WithMany(parent => parent.ChildLinks)
            .HasForeignKey(link => link.ParentProcedureId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<OperationProcedureParentChildLink>()
            .HasOne(link => link.ChildProcedure)
            .WithMany(child => child.ParentLinks)
            .HasForeignKey(link => link.ChildProcedureId)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<Plan>()
            .HasMany(x => x.Dependencies)
            .WithOne(x => x.PrincipalPlan)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<Plan>()
            .HasMany(x => x.Principals)
            .WithOne(x => x.DependentPlan)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<PartnershipField>()
            .HasMany(x => x.PartnershipScopes)
            .WithOne(x => x.PartnershipField)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<KpiResult>()
            .HasOne(x => x.TargetSettingMethod)
            .WithMany(x => x.KpiResults)
            .OnDelete(DeleteBehavior.NoAction);

        builder.Entity<EvaluationInstance>()
            .HasOne(x => x.Evaluation)
            .WithMany()
            .OnDelete(DeleteBehavior.SetNull);

        builder.Entity<StatisticalReportCategoryResult>()
            .HasMany(r => r.Attachments)
            .WithOne(x => x.Result)
            .OnDelete(DeleteBehavior.Cascade);

        // Property conversions.
        builder.Entity<Operation>().Property(x => x.Types).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<Operation>().Property(x => x.Beneficiaries).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<Operation>().Property(x => x.SustainabilityImpact).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        // builder.Entity<PermissionGroup>().Property(x => x.PermissionList).HasConversion(
        //     value => JsonConvert.SerializeObject(value),
        //     value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        // );

        builder.Entity<Tournament>().Property(x => x.Years).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<int[]>(value) : null
        );

        builder.Entity<Service>().Property(x => x.Categories).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<Service>().Property(x => x.Types).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<KpiResultDataEntryRequest>().Property(x => x.Periods).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<int[]>(value) : null
        );

        builder.Entity<Service>().Property(x => x.PaymentMethods).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );
        builder.Entity<Service>().Property(x => x.DevelopmentEntrances).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );
        builder.Entity<Service>().Property(x => x.ProactiveStandards).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<LibraryFile>().Property(x => x.Scopes).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<ImprovementOpportunity>().Property(x => x.InputsSources).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );
        builder.Entity<ImprovementOpportunity>().Property(x => x.Principles).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<Email>().Property(x => x.To).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<AppSetting>().Property(x => x.DashboardTopItem).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<DashboardTopItem>(value)
        );

        builder.Entity<AppSetting>().Property(x => x.DashboardSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<DashboardSetting>(value)
        );


        builder.Entity<AppSetting>().Property(x => x.AllowedFileTypes).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<string[]>(value)
        );
        builder.Entity<AppSetting>().Property(x => x.LdapSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<LdapSetting>(value)
        );

        builder.Entity<AppSetting>().Property(x => x.Office365Setting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<Office365Setting>(value)
        );

        builder.Entity<AppSetting>().Property(x => x.MailSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<MailSetting>(value)
        );

        builder.Entity<AppSetting>().Property(x => x.KpiSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<KpiSetting>(value)
        );

        builder.Entity<AppSetting>().Property(x => x.PlanSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<PlanSetting>(value)
        );

        builder.Entity<AppSetting>().Property(x => x.ServiceSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<ServiceSetting>(value)
        );

        builder.Entity<AppSetting>().Property(x => x.OperationSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<OperationSetting>(value)
        );

        builder.Entity<AppSetting>().Property(x => x.RiskSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<RiskSetting>(value)
        );
        builder.Entity<AppSetting>().Property(x => x.StatisticalReportSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<StatisticalReportSetting>(value)
        );
        builder.Entity<AppSetting>().Property(x => x.PartnershipSetting).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<PartnershipSetting>(value)
        );
        builder.Entity<StandardUserLink>().Property(x => x.Roles).HasConversion(
            value => JsonConvert.SerializeObject(value),
            value => HelperFunctions.IsValidJson(value) ? JsonConvert.DeserializeObject<string[]>(value) : null
        );

        builder.Entity<OperationProcedure>().Property(x => x.InCharge).HasConversion(
            value => JsonConvert.SerializeObject(value, new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            }),
            value => JsonConvert.DeserializeObject<OperationProcedure.OperationProcedureInCharge[]>(value)
        );

        builder.Entity<OperationUpdateRequest>().Property(x => x.OldData)
            .HasConversion(
                x => x.ToJsonString(new()
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = false
                }),
                x => JsonNode.Parse(x, null, default)!);

        builder.Entity<OperationUpdateRequest>().Property(x => x.NewData)
            .HasConversion(
                x => x.ToJsonString(new()
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    WriteIndented = false
                }),
                x => JsonNode.Parse(x, null, default)!);


        // unique keys:
        builder.Entity<Department>().HasIndex(x => x.HierarchyCode).IsUnique();
        builder.Entity<TimeDimension>().HasIndex(x => new { x.Year, x.MonthNumber }).IsUnique();

        builder.Entity<Evaluation>().HasIndex(x => x.Type);
        builder.Entity<EvaluationInstance>().HasIndex(x => x.EntityId);
        builder.Entity<EvaluationInstance>().HasIndex(x => x.Type);
        // builder.Entity<Innovator>().HasIndex(x => x.EmployeeNumber).IsUnique();

        // composite keys
        builder.Entity<KpiKpiTagLink>().HasKey(x => new { x.KpiId, x.TagId });
        builder.Entity<KpiStrategicGoalLink>().HasKey(x => new { x.KpiId, x.StrategicGoalId });
        builder.Entity<LibraryFileLibraryTagLink>().HasKey(x => new { x.LibraryFileId, x.TagId });
        // builder.Entity<KpiResult>().HasKey(x => new {x.KpiId, x.Year, x.DepartmentId});
        builder.Entity<KpiResultCapabilityLibraryFileLink>().HasKey(x => new { x.CapabilityId, x.LibraryFileId });
        // builder.Entity<KpiResultPeriodLibraryFileLink>().HasKey(x => new {x.KpiResultPeriodId, x.LibraryFileId});
        builder.Entity<DepartmentKpiLink>().HasKey(x => new { x.DepartmentId, x.KpiId });
        builder.Entity<OperationStrategicGoalLink>().HasKey(x => new { x.OperationId, x.GoalId });
        builder.Entity<OperationPartnerLink>().HasKey(x => new { x.OperationId, x.PartnerId });
        builder.Entity<OperationSuccessFactorLink>().HasKey(x => new { x.OperationId, x.SuccessFactorId });
        builder.Entity<StrategicPillarGoalsLink>().HasKey(x => new { x.PillarId, x.GoalId });
        builder.Entity<StrategicPerspectiveGoalsLink>().HasKey(x => new { x.PerspectiveId, x.GoalId });
        builder.Entity<OperationKpiLink>().HasKey(x => new { x.KpiId, x.OperationId });
        builder.Entity<OperationProcedureKpiLink>().HasKey(x => new { x.ProcedureId, x.KpiId });
        builder.Entity<OperationServiceLink>().HasKey(x => new { x.ServiceId, x.OperationId });
        builder.Entity<OperationPolicyLink>().HasKey(x => new { x.PolicyId, x.OperationId });
        builder.Entity<OperationSpecificationLink>().HasKey(x => new { x.SpecificationId, x.OperationId });
        builder.Entity<OperationRuleAndRegulationLink>()
            .HasKey(x => new { RuleId = x.RuleAndRegulationId, x.OperationId });
        builder.Entity<OperationEnhancementFileLink>().HasKey(x => new { x.FileId, x.EnhancementId });
        builder.Entity<OperationFormFileLink>().HasKey(x => new { x.OperationId, x.FileId });
        builder.Entity<PlanStrategicGoalLink>().HasKey(x => new { x.PlanId, x.GoalId });
        builder.Entity<PlanMinistryStrategicGoalLink>().HasKey(x => new { x.PlanId, x.MinistryStrategicGoalId });
        builder.Entity<PlanPartneringDepartmentLink>().HasKey(x => new { x.PlanId, x.DepartmentId });
        builder.Entity<PlanPlanInputLink>().HasKey(x => new { x.PlanId, x.PlanInputId });
        builder.Entity<PlanPartnerLink>().HasKey(x => new { x.PlanId, x.PartnerId });
        builder.Entity<PlanOperationLink>().HasKey(x => new { x.PlanId, x.OperationId });
        builder.Entity<PlanKpiLink>().HasKey(x => new { x.PlanId, x.KpiId });
        builder.Entity<TeamUserLink>().HasKey(x => new { x.TeamId, x.UserId });
        builder.Entity<DepartmentUserLink>().HasKey(x => new { x.DepartmentId, x.UserId });
        builder.Entity<PermissionUserLink>().HasKey(x => new { x.PermissionId, x.UserId });
        builder.Entity<PermissionGroupUserLink>().HasKey(x => new { x.GroupId, x.UserId });
        builder.Entity<PermissionOverrider>().HasKey(x => new { x.PermissionId, x.OverriderId });
        builder.Entity<PermissionPermissionGroupLink>().HasKey(x => new { x.PermissionId, x.GroupId });
        builder.Entity<CapabilityKpiLink>().HasKey(x => new { x.CapabilityId, x.KpiId });
        builder.Entity<CapabilityLibraryFileLink>().HasKey(x => new { x.CapabilityId, x.LibraryFileId });
        builder.Entity<KpiResultDataEntryRequestDepartmentLink>().HasKey(x => new { x.RequestId, x.DepartmentId });
        builder.Entity<KpiResultDataEntryRequestKpiLink>().HasKey(x => new { x.RequestId, x.KpiId });
        builder.Entity<KpiResultDataEntryResponseTransferUserLink>().HasKey(x => new { x.TransferId, x.UserId });
        builder.Entity<BenchmarkBenchmarkRequestReasonLink>().HasKey(x => new { x.BenchmarkId, x.ReasonId });
        builder.Entity<OperationProcedureParentChildLink>()
            .HasKey(x => new { x.ParentProcedureId, x.ChildProcedureId });
        builder.Entity<BenchmarkBenchmarkSelectionReasonLink>().HasKey(x => new { x.BenchmarkId, x.ReasonId });
        builder.Entity<BenchmarkOperationLink>().HasKey(x => new { x.BenchmarkId, x.OperationId });
        builder.Entity<BenchmarkStrategicGoalLink>().HasKey(x => new { x.BenchmarkId, x.GoalId });
        builder.Entity<BenchmarkKpiResultLink>().HasKey(x => new { x.BenchmarkId, x.ResultId });
        builder.Entity<BenchmarkLibraryFileLink>().HasKey(x => new { x.BenchmarkId, x.LibraryFileId });
        builder.Entity<ImprovementOpportunityStandardLink>()
            .HasKey(x => new { x.ImprovementOpportunityId, x.StandardId });
        builder.Entity<ImprovementOpportunityPrincipleLink>()
            .HasKey(x => new { x.ImprovementOpportunityId, x.PrincipleId });
        builder.Entity<InnovationActivityLink>().HasKey(x => new { x.InnovationId, x.ActivityId });
        builder.Entity<PlanTaskKpiLink>().HasKey(x => new { x.PlanTaskId, x.KpiId });
        builder.Entity<PlanTaskOperationLink>().HasKey(x => new { x.PlanTaskId, x.OperationId });
        builder.Entity<PlanSubsubtaskLibraryFileLink>()
            .HasKey(x => new { PlanSubtaskId = x.PlanSubsubtaskId, x.LibraryFileId });
        builder.Entity<ServiceDepartmentLink>().HasKey(x => new { x.ServiceId, x.DepartmentId });
        builder.Entity<NotificationUserLink>().HasKey(x => new { x.NotificationId, x.UserId });
        builder.Entity<StandardKpiLink>().HasKey(x => new { x.StandardId, x.KpiId });
        builder.Entity<StandardCapabilityLink>().HasKey(x => new { x.StandardId, x.CapabilityId });
        builder.Entity<StandardLibraryFileLink>().HasKey(x => new { x.StandardId, x.LibraryFileId });
        builder.Entity<ImprovementOpportunityOperationLink>()
            .HasKey(x => new { x.ImprovementOpportunityId, x.OperationId });
        builder.Entity<ImprovementOpportunityLibraryFileLink>()
            .HasKey(x => new { x.ImprovementOpportunityId, x.LibraryFileId });
        builder.Entity<ImprovementOpportunityInputSourceLink>().HasKey(x => new { x.OpportunityId, x.SourceId });
        builder.Entity<ServiceCategoryLink>().HasKey(x => new { x.ServiceId, x.CategoryId });
        builder.Entity<StandardUserLink>().HasKey(x => new { x.StandardId, x.UserId });
        builder.Entity<StandardSubtaskLibraryFileLink>().HasKey(x => new { x.SubtaskId, x.LibraryFileId });
        builder.Entity<StandardSubtaskStandardUserLink>().HasKey(x => new { x.SubtaskId, x.StandardId, x.UserId });
        builder.Entity<PartnershipScopeLink>()
            .HasKey(x => new { x.PartnershipContractId, x.PartnershipScopeId });
        builder.Entity<PartnershipFrameworkLink>()
            .HasKey(x => new { x.PartnershipContractId, x.PartnershipFrameworkId });
        builder.Entity<PartnershipPartnerStandardLink>()
            .HasKey(x => new { x.PartnershipContractId, x.PartnerStandardId });
        builder.Entity<PartnershipPartnershipFieldLink>()
            .HasKey(x => new { x.PartnershipContractId, x.PartnershipFieldId });
        builder.Entity<PartnershipGoalNationalAgendaLink>()
            .HasKey(x => new { x.PartnershipGoalId, x.NationalAgendaId });
        builder.Entity<PartnershipGoalOperationLink>()
            .HasKey(x => new { x.PartnershipGoalId, x.OperationId });
        builder.Entity<PartnershipGoalServiceLink>()
            .HasKey(x => new { x.PartnershipGoalId, x.ServiceId });
        builder.Entity<PartnershipGoalActivityKpiLink>()
            .HasKey(x => new { x.PartnershipGoalActivityId, x.KpiId });
        builder.Entity<KpiDynamicDataEntryRequestLibraryFileLink>()
            .HasKey(x => new { x.RequestId, FileId = x.LibraryFileId });
        builder.Entity<PlanDependency>()
            .HasKey(x => new { x.PrincipalPlanId, x.DependentPlanId });
        builder.Entity<PlanPolicyLink>()
            .HasKey(x => new { x.PlanId, x.PolicyId });

        builder.Entity<RiskOperationLink>().HasKey(x => new { x.RiskId, x.OperationId });
        builder.Entity<RiskPlanLink>().HasKey(x => new { x.RiskId, x.PlanId });
        builder.Entity<RiskStrategicGoalLink>().HasKey(x => new { x.RiskId, x.GoalId });
        builder.Entity<RiskManagementProcedureLibraryFileLink>()
            .HasKey(x => new { x.ManagementProcedureId, x.LibraryFileId });
        builder.Entity<ServiceServiceProviderChannelLink>()
            .HasKey(x => new { x.ServiceId, x.ServiceProviderChannelId });
        builder.Entity<ServiceServiceDeliveryChannelLink>()
            .HasKey(x => new { x.ServiceId, x.ServiceDeliveryChannelId });
        builder.Entity<ServiceServiceClientCategoryLink>()
            .HasKey(x => new { x.ServiceId, x.ServiceClientCategoryId });
        builder.Entity<BenchmarkImprovementOpportunityLink>()
            .HasKey(x => new { x.BenchmarkId, x.ImprovementOpportunityId });
        builder.Entity<ServiceKpiLink>().HasKey(x => new { x.ServiceId, x.KpiId });
        builder.Entity<PlanPlanFuturePlanLink>().HasKey(x => new { x.PlanId, x.FuturePlanId });
        // indices:
        // builder.Entity<KpiResult>()
        //     .HasIndex(x => new {x.KpiId, x.Year, x.DepartmentId})
        //     .IsUnique();
        builder.Entity<KpiResultPeriod>()
            .HasIndex(x => new { x.KpiResultId, x.Period })
            .IsUnique();

        builder.Entity<FlowTransaction>().HasIndex(x => x.Label);
        builder.Entity<FlowTransaction>().HasIndex(x => x.EntityId);

        // user defined functions:
        Assembly
            .GetExecutingAssembly()
            .GetTypes()
            .Where(x => x != typeof(ISqlFunction))
            .Where(x => x.IsAssignableTo(typeof(ISqlFunction)))
            .ToList()
            .ForEach(type =>
            {
                var sqlFunction = (ISqlFunction)Activator.CreateInstance(type)!;

                var function = builder.HasDbFunction(sqlFunction.GetBinding())
                    .HasName(sqlFunction.GetName())
                    .HasSchema("");

                // Custom per-function configurations
                if (sqlFunction.GetName() == "fn_contains_at_least_one")
                {
                    function.HasParameter("target").Metadata.TypeMapping = new StringTypeMapping(
                        "NVARCHAR(MAX)", // StoreType
                        DbType.String, // DbType
                        unicode: true, // Unicode (true because it's NVARCHAR)
                        size: null // Size (null because it's MAX)
                    );
                }
            });

        // Data types
        builder.Entity<ModificationRecord>()
            .ToSqlQuery("workaround for table create/drop")
            .HasNoKey();

        builder.Entity<StringWrapper>()
            .ToSqlQuery("workaround for table create/drop")
            .HasKey(x => x.Value);

        builder.Entity<IntWrapper>()
            .ToSqlQuery("workaround for table create/drop")
            .HasKey(x => x.Value);

        builder.Entity<EvaluationScoreDetail>()
            .ToSqlQuery("workaround for table create/drop")
            .HasKey(x => new { x.EntityId, x.ScoreBandId });
        builder.Entity<EvaluationScore>()
            .ToSqlQuery("workaround for table create/drop")
            .HasKey(x => new { x.InstanceId });
        builder.Entity<ImprovementOpportunityAssignedDepartmentLink>()
            .HasKey(x => new { x.ImprovementOpportunityId, x.AssignedDepartmentId });
        builder.Entity<ImprovementOpportunityAssignedTeamLink>()
            .HasKey(x => new { x.ImprovementOpportunityId, x.AssignedTeamId });
        builder.Entity<ImprovementOpportunityAssignedUserLink>()
            .HasKey(x => new { x.ImprovementOpportunityId, x.AssignedUserId });
    }

    private static void SetupIdentity(ModelBuilder builder)
    {
        builder.Entity<User>().ToTable("users");
        builder.Entity<Role>().ToTable("roles");
        builder.Entity<UserRole>().ToTable("user_roles");
        builder.Entity<UserLogin>().ToTable("user_logins");
        builder.Entity<RoleClaim>().ToTable("role_claims");
        builder.Entity<UserClaim>().ToTable("user_claims");

        builder.Entity<Role>().Property(x => x.ConcurrencyStamp).HasColumnName("concurrency_stamp");
        builder.Entity<Role>().Property(x => x.Id).HasColumnName("id");
        builder.Entity<Role>().Property(x => x.Name).HasColumnName("name");
        builder.Entity<Role>().Property(x => x.NormalizedName).HasColumnName("normalized_name");

        builder.Entity<RoleClaim>().Property(x => x.ClaimType).HasColumnName("claim_type");
        builder.Entity<RoleClaim>().Property(x => x.ClaimValue).HasColumnName("claim_value");
        builder.Entity<RoleClaim>().Property(x => x.Id).HasColumnName("id");
        builder.Entity<RoleClaim>().Property(x => x.RoleId).HasColumnName("role_id");

        builder.Entity<UserClaim>().Property(x => x.ClaimType).HasColumnName("claim_type");
        builder.Entity<UserClaim>().Property(x => x.ClaimValue).HasColumnName("claim_value");
        builder.Entity<UserClaim>().Property(x => x.Id).HasColumnName("id");
        builder.Entity<UserClaim>().Property(x => x.UserId).HasColumnName("user_id");

        builder.Entity<UserLogin>().Property(x => x.LoginProvider).HasColumnName("login_provider");
        builder.Entity<UserLogin>().Property(x => x.ProviderDisplayName).HasColumnName("provider_display_name");
        builder.Entity<UserLogin>().Property(x => x.ProviderKey).HasColumnName("provider_key");
        builder.Entity<UserLogin>().Property(x => x.UserId).HasColumnName("user_id");

        builder.Entity<UserRole>().Property(x => x.RoleId).HasColumnName("role_id");
        builder.Entity<UserRole>().Property(x => x.UserId).HasColumnName("user_id");

        builder.Entity<UserToken>().Property(x => x.LoginProvider).HasColumnName("login_provider");
        builder.Entity<UserToken>().Property(x => x.Name).HasColumnName("name");
        builder.Entity<UserToken>().Property(x => x.UserId).HasColumnName("user_id");
        builder.Entity<UserToken>().Property(x => x.Value).HasColumnName("value");

        builder.Entity<User>().Property(x => x.ConcurrencyStamp).HasColumnName("concurrency_stamp");
        builder.Entity<User>().Property(x => x.Id).HasColumnName("id");
        builder.Entity<User>().Property(x => x.UserName).HasColumnName("username");
        builder.Entity<User>().Property(x => x.NormalizedUserName).HasColumnName("normalized_username");
        builder.Entity<User>().Property(x => x.Email).HasColumnName("email");
        builder.Entity<User>().Property(x => x.NormalizedEmail).HasColumnName("normalized_email");
        builder.Entity<User>().Property(x => x.EmailConfirmed).HasColumnName("email_confirmed");
        builder.Entity<User>().Property(x => x.PasswordHash).HasColumnName("password_hash");
        builder.Entity<User>().Property(x => x.SecurityStamp).HasColumnName("security_stamp");
        builder.Entity<User>().Property(x => x.PhoneNumber).HasColumnName("phone_number");
        builder.Entity<User>().Property(x => x.PhoneNumberConfirmed).HasColumnName("phone_number_confirmed");
        builder.Entity<User>().Property(x => x.TwoFactorEnabled).HasColumnName("two_factor_enabled");
        builder.Entity<User>().Property(x => x.LockoutEnabled).HasColumnName("lockout_enabled");
        builder.Entity<User>().Property(x => x.LockoutEnd).HasColumnName("lockout_end");
        builder.Entity<User>().Property(x => x.AccessFailedCount).HasColumnName("access_fail_count");
    }

    private static void ConfigureJsonConverter(ModelBuilder builder)
    {
        foreach (var entityType in builder.Model.GetEntityTypes())
        {
            var properties = entityType.ClrType.GetProperties()
                .Where(prop => Attribute.IsDefined(prop, typeof(DbJsonConverterAttribute)));
            foreach (var property in properties)
            {
                var elementType = property.PropertyType.GetElementType();

                var converterType = typeof(AppBaseDbContext.JsonArrayConverter<>).MakeGenericType(elementType!);
                var converterInstance = Activator.CreateInstance(converterType);

                // Set the value converter
                builder.Entity(entityType.ClrType).Property(property.PropertyType, property.Name)
                    .HasConversion(converterInstance as ValueConverter);
            }
        }
    }
}
