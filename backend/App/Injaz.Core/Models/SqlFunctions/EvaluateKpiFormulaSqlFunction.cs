using System;
using System.Collections.Generic;
using System.Reflection;
using Injaz.Core.Abstraction;

namespace Injaz.Core.Models.SqlFunctions;

public class EvaluateKpiFormulaSqlFunction : ISqlFunction
{
    public string GetName()
    {
        return "fn_evaluate_kpi_formula";
    }

    public MethodInfo GetBinding()
    {
        return GetType().GetMethod(nameof(Call));
    }

    public IEnumerable<Type> GetDependencies()
    {
        return Array.Empty<Type>();
    }

    public string GetDefinition()
    {
        return $@"
                CREATE OR
                    ALTER FUNCTION {GetName()}(@formula NVARCHAR(1024), @a FLOAT, @b FLOAT, @default FLOAT)
                        RETURNS FLOAT
                    AS
                    BEGIN
                        RETURN CASE
                                   WHEN @a IS NULL OR (@b IS NULL AND @formula <> 'A') THEN NULL
                                   WHEN @formula = 'A' THEN @a
                                   WHEN @formula = 'A-B' THEN @a - @b
                                   WHEN @formula = 'B-A' THEN @b - @a
                                   WHEN @formula = 'A/B' THEN IIF(@b = 0, 0, @a / @b)
                                   WHEN @formula = '(A/B)*100' THEN IIF(@b = 0, 0, @a / @b * 100)
                                   WHEN @formula = '(A/B)*1000' THEN IIF(@b = 0, 0, @a / @b * 1000)
                                   WHEN @formula = '(A/B)*10000' THEN IIF(@b = 0, 0, @a / @b * 10000)
                                   WHEN @formula = '(A/B)*100000' THEN IIF(@b = 0, 0, @a / @b * 100000)
                                   WHEN @formula = 'B/A' THEN IIF(@a = 0, 0, @b / @a)
                                   WHEN @formula = '(B/A)*100' THEN IIF(@a = 0, 0, @b / @a * 100)
                                   WHEN @formula = '((A-B)/A)*100' THEN IIF(@a = 0, 0, ((@a - @b) / @a) * 100)
                                   WHEN @formula = '((A-B)/B)*100' THEN IIF(@b = 0, 0, ((@a - @b) / @b) * 100)
                                   WHEN @formula = '((B-A)/A)*100' THEN IIF(@a = 0, 0, ((@b - @a) / @a) * 100)
                                   WHEN @formula = '((B-A)/B)*100' THEN IIF(@b = 0, 0, ((@b - @a) / @b) * 100)
                                   WHEN @formula = '1-((B-A)/B)' THEN IIF(@b = 0, 0, 1 - ((@b - @a) / @b) * 100)
                                   ELSE -1
                            END
                    END;
        ";
    }

    public static double? Call(string formula, double? a, double? b, double @default)
    {
        throw new NotSupportedException();
    }
}
