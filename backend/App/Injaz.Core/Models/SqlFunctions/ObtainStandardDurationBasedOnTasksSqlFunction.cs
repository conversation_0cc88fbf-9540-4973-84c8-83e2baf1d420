using System;
using System.Collections.Generic;
using System.Reflection;
using Injaz.Core.Abstraction;

namespace Injaz.Core.Models.SqlFunctions;

public class ObtainStandardDurationBasedOnTasksSqlFunction : ISqlFunction
{
    public string GetName()
    {
        return "fn_obtain_standard_duration_based_on_tasks";
    }

    public MethodInfo GetBinding()
    {
        return GetType().GetMethod(nameof(Call));
    }

    public IEnumerable<Type> GetDependencies()
    {
        return Array.Empty<Type>();
    }

    public string GetDefinition()
    {
        return $@"
                CREATE OR ALTER FUNCTION {GetName()}(@id UNIQUEIDENTIFIER)
                    RETURNS FLOAT
                AS
                BEGIN
                    RETURN (SELECT SUM(DATEDIFF(S, sst.[from], sst.[to]))
                            FROM standards s
                                     INNER JOIN standard_tasks st ON s.id = st.standard_id AND st.is_deleted = 0
                                     INNER JOIN standard_subtasks sst ON st.id = sst.task_id AND sst.is_deleted = 0 AND sst.[from] <= GETUTCDATE()
                            WHERE s.is_deleted = 0
                              AND s.id = @id
                    )
                END
            ";
    }

    public static double Call(Guid standardId)
    {
        throw new NotSupportedException();
    }
}
