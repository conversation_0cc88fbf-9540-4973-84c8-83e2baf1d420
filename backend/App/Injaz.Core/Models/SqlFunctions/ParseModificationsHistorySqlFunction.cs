using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Injaz.Core.Abstraction;
using Injaz.Core.Models.Misc;

namespace Injaz.Core.Models.SqlFunctions;

public class ParseModificationsHistorySqlFunction : ISqlFunction
{
    public string GetName()
    {
        return "fn_parse_modifications_history";
    }

    public MethodInfo GetBinding()
    {
        return GetType().GetMethod(nameof(Call));
    }

    public IEnumerable<Type> GetDependencies()
    {
        return Array.Empty<Type>();
    }

    public string GetDefinition()
    {
        return $@"
                CREATE OR ALTER FUNCTION {GetName()}(
                    @modification_history NVARCHAR(MAX)
                )
                    RETURNS TABLE
                        AS
                        RETURN
                            (
                                SELECT TRY_CAST(JSON_VALUE(t.[value], '$.By.Id') AS UNIQUEIDENTIFIER) AS user_id,
                                       JSON_VALUE(t.[value], '$.By.FullName')                     AS user_full_name,
                                       CAST(JSON_VALUE(t.[value], '$.Time') AS DATETIME2)         AS time,
                                       JSON_VALUE(t.[value], '$.Notes')                           AS notes
                                FROM OPENJSON(@modification_history) t
                            )
                ";
    }

    public static IQueryable<ModificationRecord> Call(string modificationHistory)
    {
        throw new NotSupportedException();
    }
}
