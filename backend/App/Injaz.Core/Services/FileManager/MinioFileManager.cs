using System;
using System.IO;
using System.Threading.Tasks;
using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Minio;

namespace Injaz.Core.Services.FileManager;

public class MinioFileManager : IFileManager
{
    private MinioClient _client;
    private string _bucketName;
    private readonly IStringLocalizer<SharedResource> _localizer;


    public MinioFileManager(IConfiguration configuration,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _bucketName = configuration.GetValue<string>("Minio:BucketName");
        _client = new MinioClient(
            configuration.GetValue<string>("Minio:Url"),
            configuration.GetValue<string>("Minio:AccessKey"),
            configuration.GetValue<string>("Minio:SecretKey")
        );
        _localizer = localizer;
    }

    public async Task<FileManagerResult> PutAsync(Stream stream, string dir, string name, string contentType = null)
    {
        try
        {
            await _client.PutObjectAsync(_bucketName, $"{dir}/{name}", stream, stream.Length, contentType);
            return new FileManagerResult {Succeeded = true};
        }
        catch (Exception e)
        {
            return new FileManagerResult {Succeeded = false, ErrorMessage = e.Message};
        }
    }

    public async Task<FileManagerResult> DeleteAsync(string path)
    {
        try
        {
            await _client.RemoveObjectAsync(_bucketName, path);
            return new FileManagerResult {Succeeded = true};
        }
        catch (Exception e)
        {
            return new FileManagerResult {Succeeded = false, ErrorMessage = e.Message};
        }
    }

    public async Task<Stream> GetAsync(string path)
    {
        try
        {
            var taskCompletionSource = new TaskCompletionSource<Stream>();

            await _client.GetObjectAsync(_bucketName, path, stream =>
            {
                var ms = new MemoryStream();
                stream.CopyTo(ms);
                ms.Seek(0, SeekOrigin.Begin);
                taskCompletionSource.SetResult(ms);
            });

            return await taskCompletionSource.Task;
        }
        catch (Exception e)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["file_is_not_found"] }
            };
        }

    }

    public async Task<Stream> GetAsync(string path, long start, long end)
    {
        var taskCompletionSource = new TaskCompletionSource<Stream>();
        await _client.GetObjectAsync(_bucketName, path, start, end - start, stream => { taskCompletionSource.SetResult(stream); });
        return await taskCompletionSource.Task;
    }

    public async Task<long> GetLengthAsync(string path)
    {
        return (await _client.StatObjectAsync(_bucketName, path)).Size;
    }
}
