using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;

namespace Injaz.Core.Services;

public class FileTypeDetectorService
{
    private readonly string _tikaUrl;

    public FileTypeDetectorService(IConfiguration configuration)
    {
        _tikaUrl = configuration["Tika:Url"];
    }

    public async Task<string> GetExtensionAsync(Stream stream)
    {
        var contentTypeTokens = (await GetContentTypeAsync(stream)).Split("/");
        return contentTypeTokens[^1];
    }

    public async Task<string> GetContentTypeAsync(Stream stream)
    {
        using var client = new HttpClient();
        var tikaRequest = await client.PutAsync($"{_tikaUrl}/detect/stream", new StreamContent(stream));
        return await tikaRequest.Content.ReadAsStringAsync();
    }
}
