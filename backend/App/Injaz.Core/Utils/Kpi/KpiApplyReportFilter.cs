using System;
using System.Linq;
using LinqKit;
using Injaz.Core.Dtos.Report;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;

namespace Injaz.Core.Utils.Kpi;

public static class KpiApplyReportFilter
{
    public static IQueryable<Core.Models.DomainClasses.App.Kpi> Run(
        ReportKpiFilterDto filter,
        Guid userId,
        bool hasFullAccess,
        IQueryable<Models.DomainClasses.App.Kpi> itemsQueryable,
        IQueryable<Department> departmentsQueryable,
        IQueryable<Evaluation> evaluationsQueryable
    )
    {
        var currentYear = DateTime.UtcNow.Year;
        filter.FromYear = filter.FromYear == -1 ? currentYear - 4 : filter.FromYear;
        filter.ToYear = filter.ToYear == -1 ? filter.FromYear + 5 : filter.ToYear;

        var items = itemsQueryable
            .AsExpandable()
            .Where(x => x.Status.Equals(Core.Models.DomainClasses.App.Kpi.StatusActive))
            .Where(HelperExpression.IsInvolvedWithKpi(userId, departmentsQueryable, true)
                .Or(x => hasFullAccess));

        if (filter.Ids is { Length: > 0 })
        {
            items = items.Where(x => filter.Ids.Contains(x.Id));
        }

        if (filter.TagIds is { Length: > 0 })
        {
            items = items.Where(x => x.TagLinks.Any(y => filter.TagIds.Contains(y.TagId)));
        }

        if (filter.DepartmentIds is { Length: > 0 })
        {
            // Get the departments hierarchy codes:
            var codes = departmentsQueryable
                .Where(x => filter.DepartmentIds.Contains(x.Id))
                .Select(x => x.HierarchyCode);

            // Get all kpis that have an owning department that
            // is in one of the selected department ids, or
            // fall under it.
            items = items.Where(x => codes.Any(y => x.OwningDepartment.HierarchyCode.StartsWith(y)));
        }
        if (filter.MeasuringDepartmentIds is { Length: > 0 })
        {
            // Get the departments hierarchy codes:
            var codes = departmentsQueryable
                .Where(x => filter.MeasuringDepartmentIds.Contains(x.Id))
                .Select(x => x.HierarchyCode);

            // Get all kpis that have an owning department that
            // is in one of the selected department ids, or
            // fall under it.
            items = items.Where(x => codes.Any(y => x.MeasuringDepartment.HierarchyCode.StartsWith(y)));
        }
        if (filter.ExcludedDepartmentIds is { Length: > 0 })
        {
            var codes = departmentsQueryable
                .Where(x => filter.ExcludedDepartmentIds.Contains(x.Id))
                .Select(x => x.HierarchyCode);

            items = items.Where(x => !codes.Any(y => x.OwningDepartment.HierarchyCode.StartsWith(y)));
        }

        if (filter.TypeIds is { Length: > 0 })
        {
            items = items.Where(x => filter.TypeIds.Contains(x.Type.Id));
        }

        if (filter.MeasurementCycle != null)
        {
            items = items.Where(x => x.Results.Any(y =>
                y.MeasurementCycle == filter.MeasurementCycle &&
                filter.FromYear <= y.Year && y.Year < filter.ToYear
            ));
        }

        if (filter.IsSpecial)
        {
            items = items.Where(x => x.IsSpecial == 1);
        }

        if (filter.IsTrend)
        {
            items = items.Where(x => x.IsTrend == 1);
        }

        if (filter.EvaluationId != null)
        {
            var evaluationEntityLinks = evaluationsQueryable
                .Where(x => x.Id == filter.EvaluationId)
                .SelectMany(x => x.EntityLinks)
                .Select(x => x.EntityId)
                .ToList();

            items = items.Where(x =>
                !evaluationEntityLinks.Any() ||
                evaluationEntityLinks.Contains(x.Id)
            );
        }

        return items;
    }
}
