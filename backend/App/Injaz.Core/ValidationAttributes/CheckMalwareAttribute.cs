using System.ComponentModel.DataAnnotations;
using Injaz.Core.Services;
using Microsoft.AspNetCore.Mvc.DataAnnotations;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Localization;

namespace Injaz.Core.ValidationAttributes;

public class CheckMalwareAttribute : ValidationAttribute
{
    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        var malwareDetectorService = validationContext.GetService<MalwareDetectorService>();
        var task = malwareDetectorService.CheckAsync(value as byte[]);
        task.Wait();
        var hasPassed = task.Result;

        return hasPassed
            ? ValidationResult.Success
            : new ValidationResult(ErrorMessage);
    }
}

public class CheckMalwareAttributeAdapter : AttributeAdapterBase<CheckMalwareAttribute>
{
    public CheckMalwareAttributeAdapter(CheckMalwareAttribute attribute, IStringLocalizer stringLocalizer,
        MalwareDetectorService malwareDetectorService)
        : base(attribute, stringLocalizer)
    {
    }

    public override void AddValidation(ClientModelValidationContext context)
    {
    }

    public override string GetErrorMessage(ModelValidationContextBase validationContext)
    {
        return GetErrorMessage(validationContext.ModelMetadata, validationContext.ModelMetadata.GetDisplayName());
    }
}
