using Injaz.Core.ValidationAttributes;
using Microsoft.AspNetCore.Mvc.DataAnnotations;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.Configurations;

public static partial class Config
{
    public static void ConfigureCustomValidationAttributes(IServiceCollection services)
    {
        services.AddSingleton<IValidationAttributeAdapterProvider, CustomAttributeAdapterProvider>();
    }
}
