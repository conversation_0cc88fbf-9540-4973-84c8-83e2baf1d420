using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Injaz.Core.Models.DomainClasses.App;
using LinqKit;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using IApplicationDbContext = Injaz.ModuleDepartment.DbContext;

namespace Injaz.Controllers;

[ApiController]
[Route("api/department-tree")]
public class DepartmentTreeController : ControllerBase
{
    private readonly IApplicationDbContext _context;
    private readonly IMemoryCache _cache;
    private readonly ILogger<DepartmentTreeController> _logger;


    public DepartmentTreeController(
        IApplicationDbContext context,
        IMemoryCache cache,
        ILogger<DepartmentTreeController> logger)
    {
        _context = context;
        _cache = cache;
        _logger = logger;
    
    }

    [HttpGet("tree")]
    public async Task<IActionResult> GetDepartmentTree([FromQuery] string lang = "en", [FromQuery] Guid? organizationTypeId = null)
    {
        var cacheKey = $"dept_tree_{lang}_{organizationTypeId}";

        if (_cache.TryGetValue(cacheKey, out List<DepartmentTreeDto> cachedResult))
        {
            _logger.LogInformation("Department tree served from cache for key: {CacheKey}", cacheKey);
            return Ok(cachedResult);
        }

        try
        {
            var query = _context.Departments.AsQueryable();

            if (organizationTypeId.HasValue)
            {
                query = query.Where(d => d.OrganizationTypeId == organizationTypeId);
            }

            var departments = await query
                .OrderBy(d => d.LevelNumber)
                .ThenBy(d => d.Order)
                .ThenBy(d => lang == "en" ? d.NameEn : d.NameAr)
                .Select(DepartmentTreeDto.Mapper(lang))
                .ToListAsync();

            // Build hierarchy and compute full paths
            var result = BuildDepartmentTree(departments, lang);

            // Calculate approximate size based on number of departments
            var estimatedSize = CalculateCacheSize(result);

            // Cache with proper size configuration
            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                Size = estimatedSize,
                Priority = CacheItemPriority.Normal,
                SlidingExpiration = TimeSpan.FromMinutes(10) // Extend cache if actively used
            };

            _cache.Set(cacheKey, result, cacheEntryOptions);
            _logger.LogInformation("Department tree cached with size: {Size} for key: {CacheKey}", estimatedSize, cacheKey);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading department tree for language: {Language}, organizationType: {OrgType}", lang, organizationTypeId);
            return StatusCode(500, "An error occurred while loading the department tree.");
        }
    }

    [HttpPost("search")]
    public async Task<IActionResult> SearchDepartments([FromBody] DepartmentSelectionRequest request)
    {
        try
        {
            IQueryable<Department> query = _context.Departments;

            if (request.OrganizationTypeId.HasValue)
            {
                query = query.Where(d => d.OrganizationTypeId == request.OrganizationTypeId);
            }

            if (!string.IsNullOrWhiteSpace(request.SearchTerm))
            {
                var searchTerm = request.SearchTerm.Trim().ToLower();
                query = query.Where(d =>
                    (request.Language == "en" ? d.NameEn : d.NameAr).ToLower().Contains(searchTerm) ||
                    d.HierarchyCode.Contains(searchTerm));
            }

            // Use synchronous ToList() temporarily
            var departments = query
                .AsExpandable()
                .OrderBy(d => d.LevelNumber)
                .ThenBy(d => d.Order)
                .ThenBy(d => request.Language == "en" ? d.NameEn : d.NameAr)
                .Take(request.MaxResults ?? 50)
                .Select(DepartmentSearchDto.Mapper(request.Language))
                .ToList(); // Synchronous version

            // Compute full paths for search results
            await ComputeFullPathsForSearch(departments, request.Language);

            return Ok(departments);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching departments: {Error}", ex.ToString());
            return StatusCode(500, "An error occurred while searching departments.");
        }
    }

    // Clear cache endpoint for admin purposes
    [HttpPost("clear-cache")]
    public IActionResult ClearCache()
    {
        try
        {
            // Since IMemoryCache doesn't have a clear all method, we'll track our keys
            var cacheKeys = GetDepartmentCacheKeys();
            foreach (var key in cacheKeys)
            {
                _cache.Remove(key);
            }

            _logger.LogInformation("Department cache cleared manually");
            return Ok(new { message = "Cache cleared successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing department cache");
            return StatusCode(500, "An error occurred while clearing cache.");
        }
    }

    private int CalculateCacheSize(List<DepartmentTreeDto> departments)
    {
        // Estimate size based on number of departments and average data per department
        // Each department roughly contains: ID (16 bytes) + Name (avg 50 chars * 2 bytes) + other fields
        // Approximate 200 bytes per department + overhead
        var departmentCount = CountAllDepartments(departments);
        return Math.Max(1, departmentCount / 10); // Every 10 departments = size 1
    }

    private int CountAllDepartments(List<DepartmentTreeDto> departments)
    {
        var count = departments.Count;
        foreach (var dept in departments)
        {
            count += CountAllDepartments(dept.Children);
        }
        return count;
    }

    private List<string> GetDepartmentCacheKeys()
    {
        // In a real implementation, you might want to maintain a list of cache keys
        // or use a more sophisticated cache invalidation strategy
        var languages = new[] { "en", "ar" };
        var keys = new List<string>();

        foreach (var lang in languages)
        {
            keys.Add($"dept_tree_{lang}_");
            keys.Add($"dept_tree_{lang}_null");
            // Add other potential organization type IDs if known
        }

        return keys;
    }

    // Rest of the methods remain the same...
    private List<DepartmentTreeDto> BuildDepartmentTree(List<DepartmentTreeDto> departments, string lang)
    {
        var departmentDict = departments.ToDictionary(d => d.Id, d => d);
        var rootDepartments = new List<DepartmentTreeDto>();

        foreach (var dept in departments)
        {
            // Compute full path
            dept.FullPath = ComputeFullPath(dept, departmentDict);

            if (dept.ParentDepartmentId == null)
            {
                rootDepartments.Add(dept);
            }
            else if (departmentDict.ContainsKey(dept.ParentDepartmentId.Value))
            {
                departmentDict[dept.ParentDepartmentId.Value].Children.Add(dept);
            }
        }

        return rootDepartments;
    }


    private string ComputeFullPath(DepartmentTreeDto department, Dictionary<Guid, DepartmentTreeDto> departmentDict)
    {
        var path = new List<string> { department.Name };
        var current = department;
        var visitedIds = new HashSet<Guid> { department.Id };
        var levelCount = 0;

        while (current.ParentDepartmentId.HasValue &&
               departmentDict.ContainsKey(current.ParentDepartmentId.Value) &&
               !visitedIds.Contains(current.ParentDepartmentId.Value) &&
               levelCount < 2)
        {
            current = departmentDict[current.ParentDepartmentId.Value];
            visitedIds.Add(current.Id);
            path.Insert(0, current.Name);
            levelCount++;
        }

        // Add truncation indicator if there are more parent levels
        if (current.ParentDepartmentId.HasValue &&
            departmentDict.ContainsKey(current.ParentDepartmentId.Value) &&
            levelCount >= 2)
        {
            path.Insert(0, "2");
        }

        return string.Join(" > ", path);
    }

    private async Task ComputeFullPathsForSearch(List<DepartmentSearchDto> departments, string lang)
    {
        foreach (var dept in departments)
        {
            var pathParts = new List<string>();
            var currentId = dept.Id;
            var processedIds = new HashSet<Guid>();
            var levelCount = 0;

            // Build path by walking up the hierarchy (limited to configured max parent levels)
            while (currentId != Guid.Empty &&
                   !processedIds.Contains(currentId) &&
                   levelCount <= 2)
            {
                processedIds.Add(currentId);

                var current = await _context.Departments
                    .Where(d => d.Id == currentId)
                    .Select(d => new {
                        d.Id,
                        d.ParentDepartmentId,
                        Name = lang == "en" ? d.NameEn : d.NameAr
                    })
                    .FirstOrDefaultAsync();

                if (current == null) break;

                pathParts.Insert(0, current.Name);
                currentId = current.ParentDepartmentId ?? Guid.Empty;

                if (levelCount > 0 || current.ParentDepartmentId.HasValue)
                {
                    levelCount++;
                }
            }

            // Check if there are more parent levels beyond our limit
            var hasMoreParents = false;
            if (currentId != Guid.Empty && levelCount >= 2)
            {
                var nextParent = await _context.Departments
                    .Where(d => d.Id == currentId)
                    .Select(d => d.ParentDepartmentId)
                    .FirstOrDefaultAsync();
                hasMoreParents = nextParent.HasValue;
            }

            // Add truncation indicator if there are more parent levels
            if (hasMoreParents)
            {
                pathParts.Insert(0, "...");
            }

            dept.FullPath = string.Join(" > ", pathParts);
            dept.ParentNames = pathParts.Count > 1 ?
                string.Join(" > ", pathParts.Take(pathParts.Count - 1)) : "";
        }
    }




    // Startup.cs or Program.cs configuration for memory cache
    public void ConfigureServices(IServiceCollection services)
    {
        // Configure memory cache with size limit
        services.AddMemoryCache(options =>
        {
            options.SizeLimit = 100; // Adjust based on your needs
            options.CompactionPercentage = 0.25; // Remove 25% of entries when limit is reached
        });

        // Other service configurations...
    }
}



public class DepartmentOptions
{
    public const string SectionName = "Department";

    /// <summary>
    /// Maximum number of parent levels to show in department paths
    /// </summary>
    public int MaxParentLevelsInPath { get; set; } = 2;

    /// <summary>
    /// Maximum number of search results to return
    /// </summary>
    public int MaxSearchResults { get; set; } = 50;

    /// <summary>
    /// Cache duration in minutes for department tree
    /// </summary>
    public int CacheDurationMinutes { get; set; } = 30;

    /// <summary>
    /// Sliding cache expiration in minutes
    /// </summary>
    public int SlidingCacheExpirationMinutes { get; set; } = 10;

    /// <summary>
    /// Character to use for path separation
    /// </summary>
    public string PathSeparator { get; set; } = " > ";

    /// <summary>
    /// Text to show when path is truncated
    /// </summary>
    public string TruncationIndicator { get; set; } = "...";
}

