using System.Reflection;
using Injaz.Core.Resources.Shared;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.Controllers;

public class HomeController : Controller
{
    private readonly IStringLocalizer<SharedResource> _localizer;

    public HomeController(
        IStringLocalizer<SharedResource> localizer
    )
    {
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/ping")]
    public IActionResult Ping()
    {
        return this.GetResponseObject(messages: new string[] { _localizer["server_running"] });
    }

    [HttpGet]
    [Route("/update")]
    public IActionResult Update()
    {
        return this.GetResponseObject(messages: new string[]
        {
            _localizer["update_number_0", typeof(Program).Assembly
                .GetCustomAttribute<AssemblyFileVersionAttribute>()
                ?.Version!]
        });
    }
}
