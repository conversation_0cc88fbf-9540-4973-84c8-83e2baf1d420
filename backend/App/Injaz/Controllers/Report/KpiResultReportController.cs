using System;
using System.Linq;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Report;
using Injaz.Core.ExcelGenerators;
using Injaz.Core.Extensions;
using Injaz.Core.Permission;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.Controllers.Report;

public partial class ReportController
{
    [HttpGet]
    [Route("/report/kpi-result")]
    [Authorize(Policy = PermissionNameList.ReportKpi)]
    public async Task<IActionResult> KpiResultList(
        [BindQueryStringJson] ReportKpiResultFilterDto filter,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var data = await ApplyKpiResultFilter(filter);

        return this.GetResponseObject(extra: new
        {
            Items = data.Items
                .Skip(pageNumber * pageSize)
                .Take(pageSize),
            data.Count,
            data.FilteredCount
        });
    }

    [HttpGet]
    [Route("/report/kpi-result/export")]
    [Authorize(Policy = PermissionNameList.ReportKpi)]
    public async Task<IActionResult> KpiResultExport(
        [BindQueryStringJson] ReportKpiResultFilterDto filter
    )
    {
        var settings = GetDefaultKpiResultSettings();

        var data = await ApplyKpiResultFilter(filter);

        var kpiResults = data
            .Items
            .ToList();

        var years = Enumerable.Range(filter.FromYear, filter.ToYear - filter.FromYear).ToArray();

        // Get kpi prefix from app settings.
        var appSetting = _appDataContext
            .AppSettings
            .Single();

        using var generator = new KpiResultReportExcelGenerator(
            kpiResults,
            settings,
            _localizer,
            years,
            appSetting
        );

        return File(
            fileContents: generator.Generate(),
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileDownloadName: "test.xlsx"
        );
    }

    private async Task<(IQueryable<ReportKpiResultDto> Items, int Count, int FilteredCount)> ApplyKpiResultFilter(
        ReportKpiResultFilterDto filter
    )
    {
        var currentYear = DateTime.UtcNow.Year;
        filter.FromYear = filter.FromYear == -1 ? currentYear - 4 : filter.FromYear;
        filter.ToYear = filter.ToYear == -1 ? filter.FromYear + 5 : filter.ToYear;

        var userId = new Guid(_userManager.GetUserId(User));
        var hasFullAccess = await this.EnsureUserHasPermission(PermissionNameList.FullAccess);

        var items = _appDataContext
            .KpiResults
            .AsExpandable()
            .Where(x => x.Kpi.Status == Core.Models.DomainClasses.App.Kpi.StatusActive)
            .Where(HelperExpression
                .IsInvolvedWithKpiResult(userId, _appDataContext.Departments, true)
                .Or(x => hasFullAccess)
            )
            .Where(x => filter.FromYear <= x.Year && x.Year < filter.ToYear)

            // A workaround to get one kpi result for every year given
            // a particular department.
            .Where(x =>
                // Pass it if there is only one result for that particular
                // department.
                x.Kpi.Results
                    .Where(y => filter.FromYear <= y.Year && y.Year < filter.ToYear)
                    .Count(y => y.DepartmentId == x.DepartmentId && y.KpiId == x.KpiId) == 1 ||

                // Or only take the result of the first year for that particular
                // department.
                (
                    x.Kpi.Results
                        .Where(y => filter.FromYear <= y.Year && y.Year < filter.ToYear)
                        .Count(y => y.DepartmentId == x.DepartmentId && y.KpiId == x.KpiId) > 1
                    &&
                    x.Kpi.Results
                        .Where(y => filter.FromYear <= y.Year && y.Year < filter.ToYear)
                        .Where(y => y.DepartmentId == x.DepartmentId && y.KpiId == x.KpiId)
                        .Min(y => y.Year) == x.Year
                )
            );

        if (filter.Cycle != null)
        {
            items = items
                .Where(x => filter.Cycle == x.Kpi.MeasurementCycle)
                .Where(x => x.MeasurementCycle == filter.Cycle);
        }

        if (filter.Periods is { Length: > 0 })
        {
            items = items.Where(x => x.Periods.Any(y => filter.Periods.Contains(y.Period)));
        }

        var count = items.Count();


        if (filter.KpiIds is { Length: > 0 })
        {
            items = items.Where(x => filter.KpiIds.Contains(x.KpiId));
        }

        if (filter.DepartmentIds is { Length: > 0 })
        {
            items = items.Where(x => filter.DepartmentIds.Contains(x.DepartmentId));
        }
        if (filter.MeasuringDepartmentIds is { Length: > 0 })
        {
            items = items
                .Where(x => x.Kpi.MeasuringDepartmentId.HasValue &&
                            filter.MeasuringDepartmentIds.Contains(x.Kpi.MeasuringDepartmentId.Value));

        }
        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;

        var result = items
            .Select(
                ReportKpiResultDto.Mapper(
                    HelperFunctions.GetLanguageCode(),
                    canAchievedBeNegative,
                    filter.FromYear,
                    filter.ToYear,
                    filter.Cycle,
                    filter.Periods
                )
            )
            .OrderBy(x => x.Kpi.Code.Length)
            .ThenBy(x => x.Kpi.Code);

        return (result, count, items.Count());
    }

    private ReportKpiResultSettingDto GetDefaultKpiResultSettings()
    {
        return new ReportKpiResultSettingDto
        {
            Columns = new ReportKpiResultSettingColumnDto
            {
                Formula = true,
                Type = true,
                FormulaDescriptionA = true,
                FormulaDescriptionB = true,
                MeasurementCycle = true,
                Units = true,
                Operation = true,
                MeasuringDepartment = true
            }
        };
    }
}
