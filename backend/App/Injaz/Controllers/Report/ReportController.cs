using Injaz.Core.Evaluate.Services;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace Injaz.Controllers.Report;

[Authorize]
public partial class ReportController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly UserManager<User> _userManager;
    private readonly AppSettingService _appSettingService;
    private readonly EvaluationService<KpiResultPeriod> _kpiResultPeriodEvaluationService;

    public ReportController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer,
        UserManager<User> userManager,
        AppSettingService appSettingService,
        EvaluationService<KpiResultPeriod> kpiResultPeriodEvaluationService
    )
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
        _userManager = userManager;
        _appSettingService = appSettingService;
        _kpiResultPeriodEvaluationService = kpiResultPeriodEvaluationService;

        _kpiResultPeriodEvaluationService.SetDb(_appDataContext);
    }
}
