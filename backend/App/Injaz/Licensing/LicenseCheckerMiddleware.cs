using System.Threading.Tasks;
using Injaz.Core.Exceptions;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace Injaz.Licensing;

public static class LicenseCheckerAppBuilderCollection
{
    public static IApplicationBuilder UseLicenseChecker(this IApplicationBuilder app)
    {
        return app.UseMiddleware<LicenseCheckerMiddleware>();
    }
}

public class LicenseCheckerMiddleware
{
    private readonly RequestDelegate _requestDelegate;

    public LicenseCheckerMiddleware(
        RequestDelegate requestDelegate
    )
    {
        _requestDelegate = requestDelegate;
    }

    public async Task Invoke(HttpContext context)
    {
        var result = Utils.ValidateLicenseKey();

        if (result.IsSuccess)
        {
            await _requestDelegate.Invoke(context);
            return;
        }

        throw new GenericException
        {
            Messages = new[] { result.ErrorMessage }
        };
    }
}