using System;
using System.Linq;
using Injaz.Core;
using Injaz.Core.Models;

namespace Injaz.Services;

public class DepartmentHierarchyCodeService
{
    private readonly AppDataContext _appDataContext;

    public DepartmentHierarchyCodeService(AppDataContext appDataContext)
    {
        _appDataContext = appDataContext;
    }

    public void Regenerate()
    {
        void Run(bool nullify = false)
        {
            var allDepartments = _appDataContext
                .Departments
                .ToList();

            void Fetch(Guid? parentId, string upperHierarchyCode)
            {
                var departments = allDepartments
                    .Where(x => x.ParentDepartmentId.Equals(parentId));

                var currentHierarchyCode = "000";
                foreach (var department in departments)
                {
                    department.HierarchyCode = nullify ? null : $"{upperHierarchyCode}{currentHierarchyCode}";
                    Fetch(department.Id, $"{upperHierarchyCode}{currentHierarchyCode}");
                    currentHierarchyCode = HelperFunctions.Increment(currentHierarchyCode);
                }
            }

            Fetch(null, "");

            _appDataContext.ForceSaveChanges();
        }

        Run();
    }
}
