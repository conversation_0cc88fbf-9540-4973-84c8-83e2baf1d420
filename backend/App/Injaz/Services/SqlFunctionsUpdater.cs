using System;
using System.Collections.Generic;
using System.Linq;
using Injaz.Core.Abstraction;
using Injaz.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace Injaz.Services;

public class SqlFunctionsUpdater
{
    private readonly AppDataContext _db;
    private readonly HashSet<Type> _waitingForDependencyResolution = new();
    private readonly HashSet<Type> _created = new();

    public SqlFunctionsUpdater(AppDataContext db)
    {
        _db = db;
    }

    public void Update()
    {
        _created.Clear();
        _waitingForDependencyResolution.Clear();

        _db.Database.BeginTransaction();

        typeof(ISqlFunction).Assembly
            .GetTypes()
            .Where(x => x != typeof(ISqlFunction))
            .Where(x => x.IsAssignableTo(typeof(ISqlFunction)))
            .ToList()
            .ForEach(CreateSqlFunction);

        _db.Database.CommitTransaction();
    }

    private void CreateSqlFunction(Type type)
    {
        if (_created.Contains(type)) return;
        if (_waitingForDependencyResolution.Contains(type))
        {
            throw new Exception("Circular dependency found while creating sql functions.");
        }

        var sqlFunction = (ISqlFunction)Activator.CreateInstance(type)!;

        var dependencies = sqlFunction.GetDependencies().ToList();
        if (dependencies.Any())
        {
            _waitingForDependencyResolution.Add(type);

            dependencies.ForEach(CreateSqlFunction);

            _waitingForDependencyResolution.Remove(type);
        }

        var sql = sqlFunction.GetDefinition();
        _db.Database.ExecuteSqlRaw(sql);
        _created.Add(type);
    }
}
