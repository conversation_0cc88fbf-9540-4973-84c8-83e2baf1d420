using Injaz.Core.Dtos.CapacityPlanning.ServiceChannelDemands;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Misc;
using MediatR;

namespace Injaz.ModuleCapacityPlanning.Core.Queries.ServiceChannelDemand;

public class GetServiceChannelDemandInsightReportQuery : IRequest<List<ServiceChannelDemandInsightReportDto>>
{
    public Guid? DepartmentId { get; set; }
    public int? Year { get; set; }
}
