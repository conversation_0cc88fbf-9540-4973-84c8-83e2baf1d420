using Injaz.Core.Dtos.CapacityPlanning.ServiceChannelDemands;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Misc;
using MediatR;

namespace Injaz.ModuleCapacityPlanning.Core.Queries.ServiceChannelDemand;

public class GetServiceChannelDemandListQuery : IRequest<TableResultDto<ServiceChannelDemandListDto>>
{
    public Guid? Department { get; set; }

    public int[]? Years { get; set; }

    public int[]? Months { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
}
