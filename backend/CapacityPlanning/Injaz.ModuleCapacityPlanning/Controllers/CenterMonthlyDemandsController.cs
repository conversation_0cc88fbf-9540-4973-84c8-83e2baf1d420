using Injaz.Core.Exceptions;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleCapacityPlanning.Core.Commands.CenterMonthlyDemand;
using Injaz.ModuleCapacityPlanning.Core.Queries.CenterMonthlyDemand;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleCapacityPlanning.Controllers;

public class CenterMonthlyDemandsController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CenterMonthlyDemandsController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.CapacityPlanningRead)]
    [Route("/capacity-planning/center-monthly-demand")]
    public async Task<IActionResult> List(
        Guid? departmentId = null,
        int[]? years = null,
        int[]? months = null,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var query = new GetCenterMonthlyDemandListQuery
        {
            DepartmentId = departmentId,
            Years = years ?? new []{ DateTime.Now.Year },
            Months = months,
            PageSize = pageSize,
            PageNumber = pageNumber
        };

        return this.GetResponseObject(extra: await _mediator.Send(query));
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.CapacityPlanningRead)]
    [Route("/capacity-planning/center-monthly-demand/{id:guid}")]
    public async Task<IActionResult> Get(Guid id)
    {
        var item =  await _mediator.Send(new GetCenterMonthlyDemandByIdQuery { Id = id });

        return this.GetResponseObject(extra: item);
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.CapacityPlanningRead)]
    [Route("/capacity-planning/center-monthly_demand/get-by-time-and-department")]
    public async Task<IActionResult> Get(Guid timeId, Guid departmentId)
    {
        var item =  await _mediator.Send(new GetCenterMonthlyDemandsByTimeAndDepartmentQuery
        {
            DepartmentId = departmentId,
            TimeId = timeId
        });

        return this.GetResponseObject(extra: item);
    }


    [HttpPost]
    [Authorize(Policy = PermissionNameList.CapacityPlanningWrite)]
    [Route("/capacity-planning/center-monthly-demand")]
    public async Task<IActionResult> Create([BindBodySingleJson] CreateCenterMonthlyDemandCommand centerMonthlyDemand)
    {
        if (centerMonthlyDemand == null)
        {
            throw new GenericException()
            {
                Messages = new[] { "Command object is null" }
            };
        }
        return this.GetResponseObject(extra: await _mediator.Send(centerMonthlyDemand));
    }

    [HttpPut]
    [Authorize(Policy = PermissionNameList.CapacityPlanningWrite)]
    [Route("/capacity-planning/center-monthly-demand")]
    public async Task<IActionResult> Update([BindBodySingleJson] UpdateCenterMonthlyDemandCommand centerMonthlyDemand)
    {
        if (centerMonthlyDemand == null)
        {
            throw new GenericException()
            {
                Messages = new[] { "Command object is null" }
            };
        }
        return this.GetResponseObject(extra: await _mediator.Send(centerMonthlyDemand));
    }

    [HttpDelete]
    [Authorize(Policy = PermissionNameList.CapacityPlanningDelete)]
    [Route("/capacity-planning/center-monthly-demand/{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _mediator.Send(new DeleteCenterMonthlyDemandCommand
        {
            Id = id
        });

        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }
}
