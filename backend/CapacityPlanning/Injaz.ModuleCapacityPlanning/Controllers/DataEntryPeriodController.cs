using Injaz.Core.Exceptions;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModuleCapacityPlanning.Core.Commands.DataEntryPeriod;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleCapacityPlanning.Controllers;

public class DataEntryPeriodController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ValidationService _validationService;

    public DataEntryPeriodController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer, ValidationService validationService)
    {
        _mediator = mediator;
        _localizer = localizer;
        _validationService = validationService;
    }


    [HttpPost]
    [Authorize(Policy = PermissionNameList.CapacityPlanning)]
    [Route("/capacity-planning/data-entry-period/toggle-single-status")]
    public async Task<IActionResult> ToggleSinglePeriod([BindBodySingleJson] ToggleSingleDataEntryCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }
        return this.GetResponseObject(extra: await _mediator.Send(command));
    }

    [HttpPost]
    [Authorize(Policy = PermissionNameList.CapacityPlanning)]
    [Route("/capacity-planning/data-entry-period/toggle-all-status")]
    public async Task<IActionResult> ToggleAllDepartmentsPeriod([BindBodySingleJson] ToggleAllTimeDataEntryCommand command)
    {
        return this.GetResponseObject(extra: await _mediator.Send(command));
    }
}
