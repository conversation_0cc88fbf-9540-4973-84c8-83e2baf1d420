using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;
using Injaz.Core.Models.DomainClasses.App.ServiceModel;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleCapacityPlanning;

public class DbContext : AppBaseDbContext
{
    public DbContext(DbContextOptions<DbContext> options, IServiceProvider serviceProvider)
        : base(options, serviceProvider)
    {
    }

    public DbSet<TimeDimension> TimeDimensions { get; set; }
    public DbSet<ServiceChannelDemand> ServiceChannelDemands { get; set; }
    public DbSet<CenterMonthlyDemand> CenterMonthlyDemands { get; set; }
    public DbSet<CenterParameter> CenterParameters { get; set; }
    public DbSet<ServiceMonthlyDemand> ServiceMonthlyDemands { get; set; }
    public DbSet<TimeDimensionEntryPeriod> TimeDimensionEntryPeriods { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<Service> Services { get; set; }

    public DbSet<CenterServiceLink> CenterServiceLinks { get; set; }

}
