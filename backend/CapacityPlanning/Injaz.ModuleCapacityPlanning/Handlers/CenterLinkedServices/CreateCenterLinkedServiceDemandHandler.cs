using Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;
using Injaz.ModuleCapacityPlanning.Core.Commands.CenterLinkedServices;
using Injaz.ModuleCapacityPlanning.Services;
using MediatR;

namespace Injaz.ModuleCapacityPlanning.Handlers.CenterLinkedServices;

public class CreateCenterLinkedServiceDemandHandler : IRequestHandler<CreateCenterLinkedServiceCommand>
{
    private readonly DbContext _db;

    public CreateCenterLinkedServiceDemandHandler(
        DbContext db,
        DataEntryPermissionChecker dataEntryPermissionChecker)
    {
        _db = db;
    }

    public async Task<Unit> Handle(CreateCenterLinkedServiceCommand command,
        CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command, cancellationToken);
    }

    private async Task<Unit> CreateOrUpdate(CreateCenterLinkedServiceCommand command,
        CancellationToken cancellationToken)
    {
        if (command == null)
        {
            throw new ArgumentNullException(nameof(command));
        }

        // delete all existing links for this center
        _db.CenterServiceLinks.RemoveRange(_db.CenterServiceLinks.Where(x => x.DepartmentId == command.DepartmentId));

        // check if services are not empty, if so loop through and add them to the database
        if (command.ServiceIds.Any())
        {
            foreach (var service in command.ServiceIds)
            {
                _db.CenterServiceLinks.Add(new CenterServiceLink
                {
                    DepartmentId = command.DepartmentId,
                    ServiceId = service
                });
            }
        }

        _db.SaveChanges();

        return await Task.FromResult(Unit.Value);
    }
}
