using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.Core.Permission;
using Injaz.Core.Services;
using Injaz.ModuleCapacityPlanning.Core.Commands.CenterParameters;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleCapacityPlanning.Handlers.CenterParameters;

public class DeleteCenterParametersHandler : IRequestHandler<DeleteCenterParametersCommand>
{
    private readonly DbContext _db;
    private readonly PermissionEnsurerService _permissionEnsurerService;
    private readonly CurrentUserService _currentUserService;

    public DeleteCenterParametersHandler(
        DbContext db,
        PermissionEnsurerService permissionService,
        CurrentUserService currentUserService
    )
    {
        _db = db;
        _permissionEnsurerService = permissionService;
        _currentUserService = currentUserService;
    }

    public async Task<Unit> Handle(DeleteCenterParametersCommand query, CancellationToken cancellationToken)
    {
        var entity = await _db.CenterParameters
            .FirstOrDefaultAsync(x => x.Id == query.Id, cancellationToken: cancellationToken);


        var userId = _currentUserService.GetId();
        var hasFullAccessPermission = await _permissionEnsurerService.Ensure(PermissionNameList.FullAccess);

        if (entity == null)
            throw new ItemNotFoundException();

        // Check if user has permission to delete for this department
        AppHelperFunctions.AssertUserInvolvedWithDepartment(
            userId,
            entity.DepartmentId,
            _db.Departments,
            hasFullAccessPermission);


        _db.CenterParameters.Remove(entity);
        _db.SaveChanges();

        return Unit.Value;
    }
}
