using Injaz.Core;
using Injaz.Core.Dtos.CapacityPlanning.ServiceMonthlyDemands;
using Injaz.Core.Exceptions;
using Injaz.ModuleCapacityPlanning.Core.Queries.ServiceMonthlyDemand;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleCapacityPlanning.Handlers.ServiceMonthlyDemands;

public class GetServiceMonthlyDemandByIdHandler : IRequestHandler<GetServiceMonthlyDemandByIdQuery, ServiceMonthlyDemandGetDto>
{
    private readonly DbContext _db;
    private readonly string _lang;

    public GetServiceMonthlyDemandByIdHandler(
        DbContext db)
    {
        _db = db;
        _lang = HelperFunctions.GetLanguageCode();
    }

    public async Task<ServiceMonthlyDemandGetDto> Handle(GetServiceMonthlyDemandByIdQuery query, CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        var item =  _db.ServiceMonthlyDemands
            .AsExpandable()
            .Select(ServiceMonthlyDemandGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .Include(x => x.Department)
            .Include(x => x.Time)
            .Include(x => x.Service)
            .FirstOrDefault(x => x.Id == query.Id);

        if (item == null)
            throw new ItemNotFoundException();

        return item;
    }
}
