using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Exceptions;
using Injaz.Core.Services;
using Injaz.ModuleCapacityPlanning.Core.Commands.TimeDimension;
using LinqKit;
using MediatR;

namespace Injaz.ModuleCapacityPlanning.Handlers.TimeDimension;

public class CreateOrUpdateTimeDimensionHandler :
    IRequestHandler<CreateTimeDimensionCommand, TimeDimensionGetDto>,
    IRequestHandler<UpdateTimeDimensionCommand, TimeDimensionGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdateTimeDimensionHandler(
        DbContext db,
        ValidationService validationService,
        CurrentUserService currentUserService
    )
    {
        _db = db;
        _validationService = validationService;
    }

    public Task<TimeDimensionGetDto> Handle(CreateTimeDimensionCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<TimeDimensionGetDto> Handle(UpdateTimeDimensionCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private async Task<TimeDimensionGetDto> CreateOrUpdate(CreateTimeDimensionCommand command)
    {
        await Task.CompletedTask;

        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }

        var item = new Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.TimeDimension();

        if (command is UpdateTimeDimensionCommand timeEdit)
        {
            item = _db.TimeDimensions
                .FirstOrDefault(x => x.Id == timeEdit.Id);

            if (item is null)
                throw new ItemNotFoundException();

            // Check for duplicate Year and MonthNumber combination (excluding current record)
            var duplicateExists = _db.TimeDimensions
                .Any(x => x.Year == command.Year && x.MonthNumber == command.MonthNumber && x.Id != timeEdit.Id);

            if (duplicateExists)
            {
                throw new GenericException
                {
                    Messages = new[] { $"A record with Year {command.Year} and Month {command.MonthNumber} already exists." }
                };
            }

            item.Year = command.Year;
            item.MonthNumber = command.MonthNumber;

            _db.SaveChanges();

            return _db.TimeDimensions
                .AsExpandable()
                .Select(TimeDimensionGetDto.Mapper())
                .Single(x => x.Id == item.Id);
        }
        else
        {
            var monthsList = Enumerable.Range(1, 12).ToList();
            if (command.AutoAddMonths)
            {
                // find the existed months
                List<int> existedMonths;
                existedMonths = _db.TimeDimensions
                    .Where(x => x.Year == command.Year)
                    .Select(x => x.MonthNumber)
                    .ToList();

                var remainMonths = monthsList
                    .Where(x => !existedMonths.Contains(x))
                    .Select(x => new Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.TimeDimension()
                    {
                        Year = command.Year,
                        MonthNumber = x,
                    })
                    .ToList();

                if (remainMonths.Any())
                {
                    _db.TimeDimensions.AddRange(remainMonths);
                    _db.SaveChanges();
                }

                // int the case of bulk auto creations
                return new TimeDimensionGetDto();
            }

            // Check for duplicate Year and MonthNumber combination
            var duplicateExists = _db.TimeDimensions
                .Any(x => x.Year == command.Year && x.MonthNumber == command.MonthNumber);

            if (duplicateExists)
            {
                throw new GenericException
                {
                    Messages = new[] { $"A record with Year {command.Year} and Month {command.MonthNumber} already exists." }
                };
            }

            item = new Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel.TimeDimension
            {
                Year = command.Year,
                MonthNumber = command.MonthNumber,
            };
            _db.TimeDimensions.Add(item);
            _db.SaveChanges();

            return _db.TimeDimensions
                .AsExpandable()
                .Select(TimeDimensionGetDto.Mapper())
                .Single(x => x.Id == item.Id);
        }
    }
}
