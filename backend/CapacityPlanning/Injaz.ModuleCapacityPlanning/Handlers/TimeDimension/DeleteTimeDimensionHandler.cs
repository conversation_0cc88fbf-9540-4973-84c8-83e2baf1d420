using Injaz.Core.Exceptions;
using Injaz.ModuleCapacityPlanning.Core.Commands.TimeDimension;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleCapacityPlanning.Handlers.TimeDimension;

public class DeleteTimeDimensionHandler : IRequestHandler<DeleteTimeDimensionCommand>
{
    private readonly DbContext _db;

    public DeleteTimeDimensionHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public async Task<Unit> Handle(DeleteTimeDimensionCommand query, CancellationToken cancellationToken)
    {
        var entity = await _db.TimeDimensions
            .FindAsync(new object[] { query.Id }, cancellationToken);

        if (entity == null)
            throw new ItemNotFoundException();

        // Check for dependencies
        var hasRequestDemandData = await _db.ServiceChannelDemands.AnyAsync(x => x.TimeId == query.Id, cancellationToken);
        var hasCapacities = await _db.CenterMonthlyDemands.AnyAsync(x => x.TimeId == query.Id, cancellationToken);
        var hasServiceRequests =
            await _db.ServiceMonthlyDemands.AnyAsync(x => x.TimeId == query.Id, cancellationToken);
        var hasPeriods = await _db.TimeDimensionEntryPeriods.AnyAsync(x => x.TimeId == query.Id, cancellationToken);

        if (hasRequestDemandData || hasCapacities || hasServiceRequests || hasPeriods)
            throw new GenericException()
            {
                Messages = new[] { "Time dimension is used by other entities" }
            };

        _db.TimeDimensions.Remove(entity);
        _db.SaveChanges();

        return Unit.Value;
    }
}
