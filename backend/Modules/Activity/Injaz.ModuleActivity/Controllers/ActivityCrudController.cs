using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Activity;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;
using ActivityModel = Injaz.Core.Models.DomainClasses.App.Activity;

namespace Injaz.ModuleActivity.Controllers;

public partial class ActivityController
{
    [HttpGet]
    [Route("/activity")]
    public IActionResult List(string keyword = "",
        int? year = null,
        int pageNumber = 0,
        int pageSize = 20)
    {
        keyword = keyword?.ToLower()?.Trim() ?? "";

        var items = _appDataContext.Activities.Where(x => x.NameAr.ToLower().Contains(keyword) ||
                                                          x.NameEn.ToLower().Contains(keyword));

        if (year != null)
        {
            items = items.Where(x => x.Year == year);
        }

        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: new
        {
            Items = items.OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                .Skip(pageNumber * pageSize)
                .Take(pageSize)
                .Select(ActivityListDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.Activities.Count(),
            FilteredCount = items.Count()
        });

    }

    [HttpGet]
    [Route("/activity/{id:guid}")]
    public IActionResult Get(Guid id, bool forEdit = false)
    {
        var item = forEdit
            ? _appDataContext.Activities
                .AsExpandable()
                .Select(ActivityEditDto.Mapper())
                .FirstOrDefault(p => p.Id.Equals(id)) as object
            : _appDataContext.Activities
                .AsExpandable()
                .Select(ActivityDetailDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(p => p.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }


        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/activity")]
    public IActionResult Create([BindBodySingleJson] ActivityCreateDto activity)
    {
        return CreateOrUpdate(activity);
    }

    [HttpPut]
    [Route("/activity")]
    public IActionResult Update([BindBodySingleJson] ActivityEditDto activity)
    {
        return CreateOrUpdate(activity);
    }

    [HttpDelete]
    [Route("/activity/{id:guid}")]
    public IActionResult Delete(Guid id)
    {
        var data = _appDataContext.Activities.Find(id);

        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        _appDataContext.Activities.Remove(data);
        _appDataContext.SaveChanges();

        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }

    private IActionResult CreateOrUpdate(ActivityCreateDto activity)
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        ActivityModel item;
        if (activity is ActivityEditDto activityEditDto)
        {
            item = _appDataContext.Activities.Find(activityEditDto.Id);

            if (item == null)
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] }
                );
            }
        }
        else
        {
            item = new ActivityModel();
            _appDataContext.Activities.Add(item);
        }

        // Fill the fields.
        item.NameAr = activity.NameAr.Trim();
        item.NameEn = activity.NameEn?.Trim() ?? activity.NameAr.Trim();
        item.Year = activity.Year;


        _appDataContext.SaveChanges();

        return this.GetResponseObject
        (
            extra: _appDataContext
                .Activities
                .AsExpandable()
                .Select(ActivityGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

}
