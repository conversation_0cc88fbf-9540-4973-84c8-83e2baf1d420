using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Activity;
using Injaz.Core.Dtos.Innovation;
using Injaz.Core.Models.DomainClasses.App;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;

namespace Injaz.ModuleActivity.Controllers;

public partial class ActivityController
{
    [HttpGet]
    [Route("/activity/innovation/{id:guid}")]
    public IActionResult ListLinkedInnovations(Guid id, string title, int pageNumber = 0, int pageSize = 20)
    {
        title = title?.Trim().ToLower() ?? "";

        var items = _appDataContext
            .InnovationActivityLinks.AsExpandable()
            .Where(x => x.ActivityId.Equals(id));

        var count = items.Count();

        if (!string.IsNullOrWhiteSpace(title))
        {
            items = items.Where(x => x.Innovation.Title.ToLower().Contains(title));
        }

        return this.GetResponseObject(extra: new
        {
            Items = items
                .Select(x => x.Innovation)
                .Select(InnovationListDto.Mapper(HelperFunctions.GetLanguageCode()))
                .OrderBy(x => x.Title)
                .Skip(pageNumber * pageSize)
                .Take(pageSize),
            Count = count,
            FilteredCount = items.Count()
        });
    }

    [HttpPost]
    [Route("/activity/innovation/{id:guid}")]
    public async Task<IActionResult> LinkWithInnovation(Guid id, Guid innovationId)
    {
        // Get the data
        var dtoExpression = ActivityGetDto.Mapper(HelperFunctions.GetLanguageCode());

        var activity = _appDataContext
            .Activities.AsExpandable()
            .Where(x => x.Id.Equals(id))
            .Select(x => new
            {
                Kpi = dtoExpression.Invoke(x),
                IsUsedByInnovation = x.InnovationLinks.Any(y => y.InnovationId.Equals(innovationId)),
            })
            .FirstOrDefault();

        var innovation = await _appDataContext
            .Innovations
            .FindAsync(innovationId);

        // Ensure that the innovation and activity exists.
        if (innovation == null || activity == null)
        {
            return this.GetResponseObject(
                success:0,
                statusCode: 404,
                messages: new string[] { _localizer["innovation_or_activity_does_not_exist"] }
            );
        }

        // Ensure that the innovation is not already linked with the activity.
        if (activity.IsUsedByInnovation)
        {
            return this.GetResponseObject(
                success:0,
                statusCode: 423,
                messages: new string[] { _localizer["innovation_is_already_using_the_activity"] }
            );
        }

        // Link 'em!
        await _appDataContext.InnovationActivityLinks.AddAsync(new InnovationActivityLink
            { InnovationId = innovationId, ActivityId = id });
        _appDataContext.SaveChanges();

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpDelete]
    [Route("/activity/innovation/{id:guid}")]
    public IActionResult UnlinkFromInnovation(Guid id, Guid innovationId)
    {
        // Ensure that the link exists.
        var innovationActivityLink = _appDataContext
            .InnovationActivityLinks
            .FirstOrDefault(x => x.InnovationId.Equals(innovationId) && x.ActivityId.Equals(id));

        if (innovationActivityLink == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Unlink 'em!
        _appDataContext.InnovationActivityLinks.Remove(innovationActivityLink);
        _appDataContext.SaveChanges();

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    // [HttpGet]
    // [Route("/innovation/activity/{innovationId:guid}")]
    // public IActionResult ListLinkedActivities(Guid innovationId, string keyword, int pageNumber = 0, int pageSize = 20)
    // {
    //     keyword = keyword?.Trim().ToLower() ?? "";

    //     var items = _appDataContext
    //         .InnovationActivityLinks.AsExpandable()
    //         .Where(x => x.InnovationId.Equals(innovationId));

    //     var count = items.Count();

    //     items = items.Where(x => x.Activity.NameAr.ToLower().Contains(keyword) ||
    //                              x.Activity.NameEn.ToLower().Contains(keyword));

    //     return this.GetResponseObject(extra: new
    //     {
    //         Items = items
    //             .Select(x => x.Activity)
    //             .Select(ActivityGetDto.Mapper(HelperFunctions.GetLanguageCode()))
    //             .OrderBy(x => x.Name)
    //             .Skip(pageNumber * pageSize)
    //             .Take(pageSize),
    //         Count = count,
    //         FilteredCount = items.Count()
    //     });
    // }

}
