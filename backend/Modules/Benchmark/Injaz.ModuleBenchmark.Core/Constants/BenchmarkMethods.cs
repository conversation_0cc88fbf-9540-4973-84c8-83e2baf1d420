using Injaz.Core.Dtos.Misc;
using BenchmarkModel = Injaz.Core.Models.DomainClasses.App.Benchmark;

namespace Injaz.ModuleBenchmark.Core.Constants;

public static class BenchmarkMethods
{
    public static readonly MiscItemDto[] List =
    {
        new()
        {
            Id = BenchmarkModel.MethodVisit,
            Name = "benchmark_method_visit"
        },

        new()
        {
            Id = BenchmarkModel.MethodRemote,
            Name = "benchmark_method_remote"
        },

        new()
        {
            Id = BenchmarkModel.MethodResearch,
            Name = "benchmark_method_research"
        },

        new()
        {
            Id = BenchmarkModel.MethodStatistics,
            Name = "benchmark_method_statistics"
        },
    };
}
