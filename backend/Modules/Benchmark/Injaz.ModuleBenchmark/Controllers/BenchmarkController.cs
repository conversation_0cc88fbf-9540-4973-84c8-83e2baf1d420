using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleBenchmark.Controllers;

[Authorize]
public partial class BenchmarkController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly UserManager<User> _userManager;
    private readonly AppSettingService _appSettingService;

    public BenchmarkController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer,
        UserManager<User> userManager,
        AppSettingService appSettingService
    )
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
        _userManager = userManager;
        _appSettingService = appSettingService;
    }
}