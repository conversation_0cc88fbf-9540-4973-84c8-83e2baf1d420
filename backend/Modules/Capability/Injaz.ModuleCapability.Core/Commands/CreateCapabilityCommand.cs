using Injaz.Core.Dtos.Capability;
using Injaz.Core.Dtos.CapabilityType;
using MediatR;

namespace Injaz.ModuleCapability.Core.Commands;

public class CreateCapabilityCommand : IRequest<CapabilityGetDto>
{
    public string NameAr { get; set; }
    public string NameEn { get; set; }
    public string DescriptionAr { get; set; }
    public string DescriptionEn { get; set; }
    public int Year { get; set; }
    public CapabilityTypeSimpleDto Type { get; set; }
}
