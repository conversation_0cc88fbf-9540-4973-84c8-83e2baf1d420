using Injaz.Core.Permission;
using Injaz.ModuleCapability.Core.Commands;
using Injaz.ModuleCapability.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;
using Injaz.Core.Resources.Shared;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleCapability.Controllers;

[Authorize(Policy = PermissionNameList.Capability)]
public class LibraryFileController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public LibraryFileController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }


    [HttpGet]
    [Route("/capability/library-file/{id:guid}")]
    public async Task<IActionResult> ListLinkedLibraryFiles(Guid id, string keyword, int pageNumber = 0,
        int pageSize = 20)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetCapabilityLinkedLibraryFileListQuery
        {
            Id = id,
            Keyword = keyword,
            PageSize = pageSize,
            PageNumber = pageNumber
        }));
    }

    [HttpPost]
    [Route("/capability/library-file/{id}")]
    public async Task<IActionResult> LinkWithLibraryFile(Guid id, Guid libraryFileId)
    {
        await _mediator.Send(new LinkCapabilityWithLibraryFileCommand
        {
            Id = id,
            LibraryFileId = libraryFileId
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpDelete]
    [Route("/capability/library-file/{id:guid}")]
    public async Task<IActionResult> UnlinkFromLibraryFile(Guid id, Guid libraryFileId)
    {
        await _mediator.Send(new UnlinkCapabilityFromLibraryFileCommand
        {
            Id = id,
            LibraryFileId = libraryFileId
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }
}
