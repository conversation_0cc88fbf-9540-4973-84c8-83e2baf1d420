using Injaz.Core.Permission;
using Injaz.ModuleCapability.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;

namespace Injaz.ModuleCapability.Controllers;

[Authorize(Policy = PermissionNameList.Capability)]
public class StatisticsController : Controller
{
    private readonly IMediator _mediator;

    public StatisticsController(
        IMediator mediator
    )
    {
        _mediator = mediator;
    }


    [HttpGet]
    [Route("/capability/statistics")]
    public async Task<IActionResult> General()
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetCapabilityStatisticsQuery()));
    }
}
