using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleCapability;

public class DbContext : AppBaseDbContext, IDbContext
{
    public DbContext(DbContextOptions<DbContext> options, IServiceProvider serviceProvider)
        : base(options, serviceProvider)
    {
    }

    public DbSet<Capability> Capabilities { get; set; }

    public DbSet<CapabilityType> CapabilityTypes { get; set; }
    public DbSet<CapabilityKpiLink> CapabilityKpiLinks { get; set; }

    public DbSet<CapabilityLibraryFileLink> CapabilityLibraryFileLinks { get; set; }
    public DbSet<LibraryFile> LibraryFiles { get; set; }

    public DbSet<KpiResultCapabilityLibraryFileLink> KpiResultCapabilityLibraryFileLinks { get; set; }

    public DbSet<KpiResultAttachment> KpiResultAttachments { get; set; }

    public DbSet<LibraryTag> LibraryTags { get; set; }

    public DbSet<LibraryFileLibraryTagLink> LibraryFilesLibraryTags { get; set; }

    public DbSet<Kpi> Kpis { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<EvaluationInstance> EvaluationInstances { get; set; }
    public DbSet<User> Users { get; set; }
}
