using Injaz.Core.Dtos.Capability;
using Injaz.Core.Dtos.Misc;
using Injaz.ModuleCapability.Core.Queries;
using MediatR;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;

namespace Injaz.ModuleCapability.Handlers;

public class GetCapabilityListHandler : IRequestHandler<GetCapabilityListQuery, TableResultDto<CapabilityListDto>>
{
    private readonly DbContext _db;

    public GetCapabilityListHandler(DbContext db)
    {
        _db = db;
    }

    public Task<TableResultDto<CapabilityListDto>> Handle(GetCapabilityListQuery query,
        CancellationToken cancellationToken)
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";
        var items = _db
            .Capabilities
            .AsExpandable()
            .Where(x => query.TournamentIds == null || query.TournamentIds.Length == 0 ||
                        x.StandardLinks.Any(y => query.TournamentIds.Contains(y.Standard.Pillar.TournamentId)))
            .Where(x => query.PillarIds == null || query.PillarIds.Length == 0 ||
                        x.StandardLinks.Any(y => query.PillarIds.Contains(y.Standard.PillarId)))
            .Where(x => query.StandardIds == null || query.StandardIds.Length == 0 ||
                        x.StandardLinks.Any(y => query.StandardIds.Contains(y.StandardId)))
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return Task.FromResult(
            new TableResultDto<CapabilityListDto>
            {
                Items = items
                    .OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .Select(CapabilityListDto.Mapper(lang))
                    .ToList(),
                Count = _db.Capabilities.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
