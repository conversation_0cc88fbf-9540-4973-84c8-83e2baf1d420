using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Department;
using MediatR;

namespace Injaz.ModuleDepartment.Core.Commands;

public class CreateDepartmentCommand : IRequest<DepartmentGetDto>
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    public string Specializations { get; set; }

    [Display(Name = "manager_name")]
    [MaxLength(256, ErrorMessage = "0_should_not_have_more_than_1_characters")]
    public string ManagerName { get; set; }

    [Display(Name = "manager_email")]
    [MaxLength(8192, ErrorMessage = "0_should_not_have_more_than_1_characters")]
    public string ManagerEmail { get; set; }

    [Display(Name = "manager_title")] public string ManagerTitle { get; set; }

    [Display(Name = "manager_staff_number")]
    [MaxLength(256)]
    public string ManagerStaffNumber { get; set; }


    public string OldOrgId { get; set; }

    public bool IsMain { get; set; }

    [Required(ErrorMessage = "0_is_required")]
    [Display(Name = "organization_number")]
    public int Order { get; set; }

    [Display(Name = "level_number")] public int? Level { get; set; }


    [Display(Name = "is_police_station")]
    public bool IsPoliceStation { get; set; }



    [Required(ErrorMessage = "0_is_required")]
    [Display(Name = "organization_type")]
    public OrganizationTypeDto OrganizationType { get; set; }

    public DepartmentListDto ParentDepartment { get; set; }
}
