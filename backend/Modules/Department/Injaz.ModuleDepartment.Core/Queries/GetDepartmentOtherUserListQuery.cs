using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.User;
using MediatR;

namespace Injaz.ModuleDepartment.Core.Queries;

public class GetDepartmentOtherUserListQuery : IRequest<TableResultDto<UserWithEmployeeNumberDto>>
{
    public Guid Id { get; set; }

    public string Keyword { get; set; }
    public string EmployeeNumber { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
