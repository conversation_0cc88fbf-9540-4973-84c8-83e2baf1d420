using Injaz.Core.Permission;
using Injaz.ModuleDepartment.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;

namespace Injaz.ModuleDepartment.Controllers;

public class KpiController: Controller
{
    private readonly IMediator _mediator;

    public KpiController(
        IMediator mediator
    )
    {
        _mediator = mediator;
    }

    [HttpGet]
    [Route("/department/{id:guid}/owned-kpi")]
    [Authorize(Policy = PermissionNameList.DepartmentRead)]
    public async Task<IActionResult> DepartmentOwnedKpis(
        Guid id,
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetDepartmentOwnedKpiListByQuery
        {
            Id = id,
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }


    [HttpGet]
    [Route("/department/{id:guid}/linked-kpi")]
    [Authorize(Policy = PermissionNameList.DepartmentRead)]
    public async Task<IActionResult> DepartmentLinkedKpis(
        Guid id,
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetDepartmentLinkedKpiListQuery
        {
            Id = id,
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }
}
