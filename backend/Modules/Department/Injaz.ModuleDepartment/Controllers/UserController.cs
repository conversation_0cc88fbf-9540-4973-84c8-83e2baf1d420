using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleDepartment.Core.Commands;
using Injaz.ModuleDepartment.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.ModuleDepartment.Controllers;

public class UserController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public UserController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpPost]
    [Route("/department/user")]
    [Authorize(Policy = PermissionNameList.DepartmentWrite)]
    public async Task<IActionResult> AddUser(Guid id, Guid userId)
    {
        await _mediator.Send(new AddDepartmentUserCommand { Id = id, UserId = userId });
        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpDelete]
    [Route("/department/user")]
    [Authorize(Policy = PermissionNameList.DepartmentWrite)]
    public async Task<IActionResult> RemoveUser(Guid id, Guid userId)
    {
        await _mediator.Send(new RemoveDepartmentUserCommand { Id = id, UserId = userId });
        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpGet]
    [Route("/department/user")]
    [Authorize(Policy = PermissionNameList.DepartmentRead)]
    public async Task<IActionResult> GetUsers(
        Guid id,
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetDepartmentUserListQuery
        {
            Id = id,
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }

    [HttpGet]
    [Route("/department/user/other")]
    [Authorize(Policy = PermissionNameList.DepartmentRead)]
    public async Task<IActionResult> GetOtherUsers(
        Guid id,
        string keyword = "",
        string employeeNumber = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetDepartmentOtherUserListQuery
        {
            Id = id,
            Keyword = keyword,
            EmployeeNumber = employeeNumber,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }

    [HttpPut]
    [Route("/department/user/set-authorized-for-signing")]
    [Authorize(Policy = PermissionNameList.DepartmentWrite)]
    public async Task<IActionResult> SetAuthorizedForSigning(Guid id, Guid userId)
    {
        return await ToggleAuthorizedForSigning(id, userId, true);
    }

    [HttpPut]
    [Route("/department/user/unset-authorized-for-signing")]
    [Authorize(Policy = PermissionNameList.DepartmentWrite)]
    public async Task<IActionResult> UnsetAuthorizedForSigning(Guid id, Guid userId)
    {
        return await ToggleAuthorizedForSigning(id, userId, false);
    }

    private async Task<IActionResult> ToggleAuthorizedForSigning(Guid id, Guid userId, bool isAuthorizedForSigning)
    {
        await _mediator.Send(new ToggleDepartmentSignatoryCommand
        {
            Id = id,
            UserId = userId,
            IsAuthorizedForSigning = isAuthorizedForSigning
        });
        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpGet]
    [Route("/department/user/hierarchy")]
    [Authorize(Policy = PermissionNameList.DepartmentRead)]
    public async Task<IActionResult> GetHierarchyUsers(
        Guid id,
        string keyword = "",
        bool onlyAuthorizedForSigning = false,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetDepartmentHierarchyUserListQuery
        {
            Id = id,
            Keyword = keyword,
            OnlyAuthorizedForSigning = onlyAuthorizedForSigning,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }

    [HttpGet]
    [Route("/department/user/hierarchy/export")]
    [Authorize(Policy = PermissionNameList.DepartmentRead)]
    public async Task<IActionResult> ExportHierarchyUsers(
        Guid id,
        string keyword = "",
        bool onlyAuthorizedForSigning = false
    )
    {
        return File(
            fileContents: await _mediator.Send(new ExportDepartmentHierarchyUserListCommand
            {
                Id = id,
                Keyword = keyword,
                OnlyAuthorizedForSigning = onlyAuthorizedForSigning
            }),
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileDownloadName: "department-hierarchy-users.xlsx"
        );
    }
}
