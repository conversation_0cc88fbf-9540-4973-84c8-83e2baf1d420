using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleDepartment;

public class DbContext : AppBaseDbContext
{
    public DbContext(DbContextOptions<DbContext> options, IServiceProvider serviceProvider)
        : base(options, serviceProvider)
    {
    }

    public DbSet<Department> Departments { get; set; }
    public DbSet<DepartmentUserLink> DepartmentUserLinks { get; set; }
    public DbSet<OrganizationType> OrganizationTypes { get; set; }
    public DbSet<User> Users { get; set; }
}
