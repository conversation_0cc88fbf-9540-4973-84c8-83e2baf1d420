using System.Text;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleDepartment.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleDepartment.Handlers;

public class DeleteHandler : IRequestHandler<DeleteDepartmentCommand>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(DeleteDepartmentCommand command, CancellationToken cancellationToken)
    {
        // Get item.
        var lang = HelperFunctions.GetLanguageCode();
        var data = _db.Departments
            .Where(x => x.Id == command.Id)
            .Select(x => new
            {
                Department = x,
                IsLinkedToOtherResources =
                    x.Children.Any() ||
                    x.Operations.Any() ||
                    x.KpiResults.Any() ||
                    x.OwnedKpis.Any() ||
                    x.Plans.Any() ||
                    x.Tasks.Any() ||
                    x.Subtasks.Any() ||
                    x.StatisticalReportCategoryLinks.Any() ||
                    x.PartnershipContracts.Any() ||
                    x.Risks.Any() ||
                    x.Benchmarks.Any() ||
                    x.ServiceLinks.Any() ||
                    x.OwnedOpportunities.Any(),
                IsLinkedToBenchmarks = x.Benchmarks.Any(),
                IsLinkedToServices = x.OwnedServices.Any(),
                IsLinkedToOpportunities = x.OwnedOpportunities.Any(),
                IsLinkedToRisks = x.Risks.Any(),
                IsLinkedToPartnershipContracts = x.PartnershipContracts.Any(),
                IsLinkedToStatisticalReportCategories = x.StatisticalReportCategoryLinks.Any(),
                IsLinkedToSubtasks = x.Subtasks.Any(),
                IsLinkedToTasks = x.Tasks.Any(),
                IsLinkedToPlans = x.Plans.Any(),
                IsLinkedToKpis = x.OwnedKpis.Any(),
                IsLinkedToKpiResults = x.KpiResults.Any(),
                IsLinkedToOperations = x.Operations.Any(),
                IsLinkedToChildren = x.Children.Any()
            })
            .SingleOrDefault();

        // Ensure the item exists.
        if (data == null)
        {
            throw new ItemNotFoundException();
        }

        // Ensure nothing is linked to the team.
        if (data.IsLinkedToOtherResources)
        {
            var departmentName = lang == SupportedCultures.LanguageArabic
                ? data.Department.NameAr
                : data.Department.NameEn;


            // Use StringBuilder for dynamic message construction
            var errorMessageBuilder = new StringBuilder();

            // Append the department name to the error message
            errorMessageBuilder.AppendFormat(_localizer["cannot_delete_department_0"], departmentName);

            // Append benchmarks part if benchmarks name is not empty
            if (data.IsLinkedToBenchmarks)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_to_other_resources_with_benchmarks"]);
            }

            // Append services part if services name is not empty
            if (data.IsLinkedToServices)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_to_other_resources_with_services"]);
            }

            // Append opportunities part if opportunities name is not empty
            if (data.IsLinkedToOpportunities)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_to_other_resources_with_opportunities"]);
            }

            // Convert the StringBuilder to a string and throw the exception

            if (data.IsLinkedToRisks)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_risks"]);
            }

            if (data.IsLinkedToPartnershipContracts)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_partnership_contracts"]);
            }

            if (data.IsLinkedToStatisticalReportCategories)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_statistical_report_categories"]);
            }

            if (data.IsLinkedToSubtasks)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_subtasks"]);
            }

            if (data.IsLinkedToTasks)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_tasks"]);
            }

            if (data.IsLinkedToPlans)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_plans"]);
            }

            if (data.IsLinkedToKpis)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_kpis"]);
            }

            if (data.IsLinkedToKpiResults)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_kpi_results"]);
            }

            if (data.IsLinkedToOperations)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_operations"]);
            }

            if (data.IsLinkedToChildren)
            {
                errorMessageBuilder.Append(" ");
                errorMessageBuilder.AppendFormat(_localizer["is_connected_with_children_departments"]);
            }

            throw new GenericException
            {
                Messages = new[] { errorMessageBuilder.ToString() }
            };
        }

        // Set the hierarchy code to null.
        data.Department.HierarchyCode = null;

        // Remove the item.
        _db.Departments.Remove(data.Department);

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
