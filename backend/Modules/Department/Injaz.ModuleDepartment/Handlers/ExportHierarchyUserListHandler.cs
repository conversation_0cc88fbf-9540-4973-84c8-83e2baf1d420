using Injaz.Core.DtoMappingStrategies;
using Injaz.Core.ExcelGenerators;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleDepartment.Core.Commands;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using Translation.Data;

namespace Injaz.ModuleDepartment.Handlers;

public class ExportHierarchyUserListHandler : IRequestHandler<ExportDepartmentHierarchyUserListCommand, byte[]>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly TranslationContext _dbTranslation;

    public ExportHierarchyUserListHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer,
        TranslationContext dbTranslation
    )
    {
        _db = db;
        _localizer = localizer;
        _dbTranslation = dbTranslation;
    }

    public Task<byte[]> Handle(
        ExportDepartmentHierarchyUserListCommand command,
        CancellationToken cancellationToken
    )
    {
        command.Keyword = command.Keyword?.ToLower().Trim() ?? "";

        var departmentHierarchyCode = _db
            .Departments
            .Where(x => x.Id == command.Id)
            .Select(x => new { x.HierarchyCode })
            .FirstOrDefault()?.HierarchyCode;

        if (departmentHierarchyCode == null)
        {
            throw new ItemNotFoundException();
        }

        var items = _db.DepartmentUserLinks
            .AsNoTracking()
            .Include(x => x.Department)
            .Include(x => x.User)
            .ThenInclude(u => u.PermissionLinks)
            .Include(x => x.User)
            .ThenInclude(u => u.PermissionGroupLinks)
            .ThenInclude(pgl => pgl.PermissionGroup)
            .ThenInclude(pg => pg.PermissionLinks)
            .Where(x => x.Department.HierarchyCode.StartsWith(departmentHierarchyCode)
                        && (!command.OnlyAuthorizedForSigning ||
                            (command.OnlyAuthorizedForSigning && x.IsAuthorizedForSigning == 1))
                        && x.User.Status != Injaz.Core.Models.DomainClasses.App.User.StatusDeleted
                        && (EF.Functions.Like(x.User.NameAr, $"%{command.Keyword}%") ||
                            EF.Functions.Like(x.User.NameEn, $"%{command.Keyword}%")))
            .Map(new DepartmentUserWithPermissionLinkDtoMappingStrategy(_localizer))
            .OrderBy(x => x.User.Name)
            .ToList();
        var generator = new DepartmentUserHierarchyExcelGenerator(
            items,
            _localizer,
            _dbTranslation
        );

        return Task.FromResult(generator.Generate());
    }
}
