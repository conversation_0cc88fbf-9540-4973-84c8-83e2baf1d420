using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Exceptions;
using Injaz.ModuleDepartment.Core.Queries;
using MediatR;

namespace Injaz.ModuleDepartment.Handlers;

public class GetForEditByIdHandler : IRequestHandler<GetDepartmentForEditByIdQuery, DepartmentEditDto>
{
    private readonly DbContext _db;

    public GetForEditByIdHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<DepartmentEditDto> Handle(GetDepartmentForEditByIdQuery request, CancellationToken cancellationToken)
    {
        var item = _db.Departments
            .AsExpandable()
            .Select(DepartmentEditDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(x => x.Id.Equals(request.Id));


        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
