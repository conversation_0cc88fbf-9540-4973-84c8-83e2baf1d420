using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleDepartment.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleDepartment.Handlers;

public class RemoveUserHandler : IRequestHandler<RemoveDepartmentUserCommand>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public RemoveUserHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(RemoveDepartmentUserCommand command, CancellationToken cancellationToken)
    {
        // Ensure that the user is
        // in the department.
        var link = _db.DepartmentUserLinks.Find(command.Id, command.UserId);
        if (link == null)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["user_is_not_on_the_department"] }
            };
        }

        _db.DepartmentUserLinks.Remove(link);
        _db.SaveChanges();

        return Task.FromResult<Unit>(default);
    }
}
