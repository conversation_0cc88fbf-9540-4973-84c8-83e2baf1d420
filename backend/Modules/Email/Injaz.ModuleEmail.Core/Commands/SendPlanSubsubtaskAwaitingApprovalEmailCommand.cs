namespace Injaz.ModuleEmail.Core.Commands;

public class SendPlanSubsubtaskAwaitingApprovalEmailCommand : SendEmailBaseCommand
{
    public SendPlanSubsubtaskAwaitingApprovalEmailCommand()
    {
        SubjectAr = "إجراء في انتظار الاعتماد";
        SubjectEn = "Plan subsubtask awaiting approval";
    }

    public string PlanSubtaskNameAr { get; set; }

    public string PlanSubtaskNameEn { get; set; }

    public Guid PlanSubtaskId { get; set; }
}
