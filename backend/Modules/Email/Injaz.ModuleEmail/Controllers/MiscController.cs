using Injaz.Core.Resources.Shared;
using Injaz.ModuleEmail.Core.Commands;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.ModuleEmail.Controllers;

[Authorize]
public class MiscController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public MiscController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpPost]
    [Route("/email/test/{to}")]
    public async Task<IActionResult> SendTestEmail(string to)
    {
        await _mediator.Send(new SendDefaultEmailCommand
        {
            To = new[] { to },
            SubjectAr = "رسالة اختبارية",
            SubjectEn = "Test email",
            BodyAr = "هذه رسالة اختبارية",
            BodyEn = "This is a test email"
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }
}