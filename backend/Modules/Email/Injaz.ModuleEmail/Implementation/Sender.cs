using System.Net;
using System.Net.Mail;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Services;
using Injaz.ModuleEmail.Abstraction;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;

namespace Injaz.ModuleEmail.Implementation;

public class Sender : ISender
{
    private readonly IWebHostEnvironment _env;
    private readonly MailSetting _setting;

    public Sender(
        AppSettingService appSettingService,
        IWebHostEnvironment env
    )
    {
        _env = env;
        _setting = appSettingService.Get().MailSetting;
    }

    public void Send(Email item)
    {
        // Filter out all emails during development,
        // and only keep the ones that are allowed for testing.
        string[] to;
        if (_env.IsDevelopment())
        {
            to = item.To.Where(x => _testingEmails.Contains(x) || x.EndsWith("yopmail.com")).ToArray();
        }
        else
        {
            to = item.To;
        }

        if (to.Length == 0)
        {
            return;
        }

        var client = new SmtpClient
        {
            Host = _setting.Host,
            Port = _setting.Port,
            EnableSsl = _setting.IsSslEnabled,
            DeliveryMethod = SmtpDeliveryMethod.Network,
            UseDefaultCredentials = false,
            Credentials = string.IsNullOrEmpty(_setting.Username) && string.IsNullOrEmpty(_setting.Password)
                ? null
                : new NetworkCredential(
                    _setting.Username,
                    _setting.Password
                )
        };

        var message = new MailMessage(
            _setting.From,
            string.Join(',', to),
            item.Subject,
            item.Body
        );

        message.IsBodyHtml = true;

        client.Send(message);
    }

    private static string[] _testingEmails = new[] { "<EMAIL>" };
}
