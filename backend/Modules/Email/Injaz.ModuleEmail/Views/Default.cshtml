@using Injaz.ModuleEmail.Views.Shared
@model Injaz.ModuleEmail.Views.DefaultViewModel;

@section ContentAr
{
    @await Html.PartialAsync("Spacing", 50)

    <!-- TITLE (AR) -->
    @await Html.PartialAsync("Heading1", new Heading1ViewModel("مرحبا!", true))

    @await Html.PartialAsync("Spacing", 20)

    <!-- MESSAGE (AR) -->
    @await Html.PartialAsync("Paragraph", new ParagraphViewModel(Model.BodyAr, true))

    @await Html.PartialAsync("Spacing", 100)
}

@section ContentEn
{
    <!-- TITLE (EN) -->
    @await Html.PartialAsync("Heading1", new Heading1ViewModel($"Hello!", false))

    @await Html.PartialAsync("Spacing", 20)

    <!-- MESSAGE (EN) -->
    @await Html.PartialAsync("Paragraph", new ParagraphViewModel(Model.BodyEn, false))

    @await Html.PartialAsync("Spacing", 100)
}