@using Injaz.ModuleEmail.Views.Shared
@model Injaz.ModuleEmail.Core.Commands.SendPlanAwaitingApprovalEmailCommand

@section ContentAr
{
    @await Html.PartialAsync("Spacing", 50)

    <!-- TITLE (AR) -->
    @await Html.PartialAsync("Heading1", new Heading1ViewModel("مرحبا!", true))

    @await Html.PartialAsync("Spacing", 20)

    <!-- MESSAGE (AR) -->
    @await Html.PartialAsync("Heading2", new Heading2ViewModel($"الخطة \"{Model.PlanNameAr}\" في انتظار الاعتماد.", true))

    @await Html.PartialAsync("Spacing", 30)

    <!-- GO TO PLAN BUTTON (AR) -->
    @await Html.PartialAsync("Button", new ButtonViewModel("الذهاب للخطة", $"{{{{app_url}}}}/plan/detail/{Model.PlanId}"))

    @await Html.PartialAsync("Spacing", 20)

    <!-- GO TO AWAITING APPROVALS BUTTON (AR) -->
    @await Html.PartialAsync("Button", new ButtonViewModel("الذهاب للخطط الغير معتمدة", "{{app_url}}/plan/not-approved"))

    @await Html.PartialAsync("Spacing", 100)
}

@section ContentEn
{
    @await Html.PartialAsync("Spacing", 50)

    <!-- TITLE (AR) -->
    @await Html.PartialAsync("Heading1", new Heading1ViewModel("Hello!", false))

    @await Html.PartialAsync("Spacing", 20)

    <!-- MESSAGE (AR) -->
    @await Html.PartialAsync("Heading2", new Heading2ViewModel($"The plan \"{Model.PlanNameEn}\" is awaiting your approval.", false))

    @await Html.PartialAsync("Spacing", 50)

    <!-- GO TO PLAN BUTTON (AR) -->
    @await Html.PartialAsync("Button", new ButtonViewModel("Go to plan", $"{{{{app_url}}}}/plan/detail/{Model.PlanId}"))

    @await Html.PartialAsync("Spacing", 20)

    <!-- GO TO AWAITING APPROVALS BUTTON (AR) -->
    @await Html.PartialAsync("Button", new ButtonViewModel("Go to not approved plans", "{{app_url}}/plan/not-approved"))

    @await Html.PartialAsync("Spacing", 100)
}
