using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Dtos.LibraryTag;
using Injaz.Core.ValidationAttributes;
using Injaz.ModuleLibrary.Core.Constants;
using MediatR;

namespace Injaz.ModuleLibrary.Core.Commands;

public class CreateLibraryFileCommand : IRequest<LibraryFileGetDto>
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "link")] public string Link { get; set; }

    [Display(Name = "file")]
    [FileType(ErrorMessage = "0_invalid_file_type")]
    [MaxFileSize(150 * 1024 * 1024, ErrorMessage = "0_should_not_be_more_than_150mb")]
    public byte[] File { get; set; }

    [Display(Name = "tags")] public IEnumerable<LibraryTagSimpleDto> Tags { get; set; }

    [Display(Name = "scopes")]
    [ItemsInList(LibraryFileScopes.Shared, LibraryFileScopes.ModulePlan)]
    public IEnumerable<string>? Scopes { get; set; }

    // Used by handler.
    public Guid CurrentUserId { get; set; }
}
