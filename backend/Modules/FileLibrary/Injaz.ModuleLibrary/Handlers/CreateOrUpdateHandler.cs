using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.Core.Services.FileManager;
using Injaz.ModuleLibrary.Core.Commands;
using Injaz.ModuleLibrary.Core.Constants;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleLibrary.Handlers;

public class CreateOrUpdateHandler :
    IRequestHandler<CreateLibraryFileCommand, LibraryFileGetDto>,
    IRequestHandler<UpdateLibraryFileCommand, LibraryFileGetDto>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IFileManager _fileManager;
    private readonly FileTypeDetectorService _fileTypeDetectorService;
    private readonly ValidationService _validationService;

    public CreateOrUpdateHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer,
        IFileManager fileManager,
        FileTypeDetectorService fileTypeDetectorService,
        ValidationService validationService
    )
    {
        _db = db;
        _localizer = localizer;
        _fileManager = fileManager;
        _fileTypeDetectorService = fileTypeDetectorService;
        _validationService = validationService;
    }

    public async Task<LibraryFileGetDto> Handle(CreateLibraryFileCommand command, CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command);
    }

    public async Task<LibraryFileGetDto> Handle(UpdateLibraryFileCommand command, CancellationToken cancellationToken)
    {
        return await CreateOrUpdate(command);
    }

    private async Task<LibraryFileGetDto> CreateOrUpdate(CreateLibraryFileCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }

        LibraryFile item;

        if (command is UpdateLibraryFileCommand updateCommand)
        {
            // Get item.
            item = await _db.LibraryFiles.FindAsync(updateCommand.Id);

            // Ensure the item exists.
            if (item == null)
            {
                throw new ItemNotFoundException();
            }

            // Remove all tags.
            _db.RemoveRange(
                _db.LibraryFileLibraryTagLinks.Where(x => x.LibraryFileId.Equals(updateCommand.Id)));

            item.Scopes = GetNewScopesForUpdate(item.Scopes, command.Scopes);

            // Delete existing file if new one is coming.
            if (command.File != null && !string.IsNullOrEmpty(item.FileName))
            {
            }
        }
        else
        {
            // Ensure that either a file or link has been passed.
            if (command.File == null && string.IsNullOrEmpty(command.Link) && command is not UpdateLibraryFileCommand)
            {
                throw new GenericException
                {
                    Messages = new string[] { _localizer["you_should_either_pass_a_file_or_a_link"] }
                };
            }

            item = new LibraryFile
            {
                OwnerId = command.CurrentUserId,
                Scopes = command.Scopes?.ToArray() ?? new[] { LibraryFileScopes.Shared }
            };
            _db.LibraryFiles.Add(item);
        }

        // Clean:
        command.NameAr = command.NameAr.Trim();
        command.NameEn = HelperFunctions.Default(command.NameEn?.Trim(), command.NameAr);


        // Upload the file:
        if (command.File != null)
        {
            FileManagerResult result;

            // Try to remove previous file if we're
            // updating.
            if (
                command is UpdateLibraryFileCommand &&
                !string.IsNullOrEmpty(item.FileName)
            )
            {
                result = await _fileManager.DeleteAsync($"{Directories.Library}/{item.FileName}");
                if (!result.Succeeded)
                {
                    throw new GenericException { Messages = new[] { result.ErrorMessage } };
                }
            }

            // Try and upload the file.
            var fileName = Guid.NewGuid().ToString();
            await using var ms = new MemoryStream(command.File);
            var contentType = await _fileTypeDetectorService.GetContentTypeAsync(ms);
            ms.Seek(0, SeekOrigin.Begin);


            result = await _fileManager.PutAsync(ms, Directories.Library, fileName, contentType);
            if (!result.Succeeded)
            {
                throw new GenericException { Messages = new[] { result.ErrorMessage } };
            }

            item.FileName = fileName;
            item.ContentType = contentType;
            item.FileSize = command.File.Length;
            item.Link = null;
        }
        else if (!string.IsNullOrEmpty(command.Link))
        {
            item.FileName = null;
            item.ContentType = null;
            item.FileSize = null;
            item.Link = command.Link;
        }

        // Update the item.
        item.LatestModificationTime = DateTime.UtcNow;
        item.NameAr = command.NameAr;
        item.NameEn = command.NameEn;
        item.TagLinks = command.Tags?.Select(x => new LibraryFileLibraryTagLink()
        {
            TagId = x.Id,
        }).ToList();

        _db.SaveChanges();

        return _db
            .LibraryFiles
            .AsExpandable()
            .Select(LibraryFileGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .First(x => x.Id.Equals(item.Id));
    }

    private static string[] GetNewScopesForUpdate(string[] oldScopes, IEnumerable<string> newScopes)
    {
        oldScopes ??= Array.Empty<string>();
        newScopes = newScopes?.ToArray() ?? Array.Empty<string>();

        // Remove shared scope if it's not needed.
        if (oldScopes.Contains(LibraryFileScopes.Shared) && !newScopes.Contains(LibraryFileScopes.Shared))
        {
            return oldScopes.Where(x => x != LibraryFileScopes.Shared).ToArray();
        }

        // Add shared scope if it's needed.
        if (!oldScopes.Contains(LibraryFileScopes.Shared) && newScopes.Contains(LibraryFileScopes.Shared))
        {
            return oldScopes.Append(LibraryFileScopes.Shared).ToArray();
        }

        return oldScopes;
    }
}
