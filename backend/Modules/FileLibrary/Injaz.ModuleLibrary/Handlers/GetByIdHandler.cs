using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Exceptions;
using Injaz.ModuleLibrary.Core.Expressions;
using Injaz.ModuleLibrary.Core.Queries;
using MediatR;

namespace Injaz.ModuleLibrary.Handlers;

public class GetByIdHandler : IRequestHandler<GetLibraryFileByIdQuery, LibraryFileGetDto>
{
    private readonly DbContext _db;

    public GetByIdHandler(DbContext db) => _db = db;

    public Task<LibraryFileGetDto> Handle(GetLibraryFileByIdQuery query, CancellationToken cancellationToken)
    {
        var item = _db
            .LibraryFiles
            .AsExpandable()
            .Where(x => x.Id.Equals(query.Id))
            .Where(ScopeHelper.MatchScopesExpression(query.Scopes))
            .Select(LibraryFileGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault();

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
