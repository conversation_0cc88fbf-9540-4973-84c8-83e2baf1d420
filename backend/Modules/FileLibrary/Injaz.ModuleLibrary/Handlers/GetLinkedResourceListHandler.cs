using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModuleLibrary.Core.Queries;
using MediatR;

namespace Injaz.ModuleLibrary.Handlers;

public class GetLinkedResourceListHandler :
    IRequestHandler<GetLibraryFileLinkedResourceListQuery, IEnumerable<ResourceDto>>
{
    private readonly DbContext _db;

    public GetLinkedResourceListHandler(DbContext db) => _db = db;

    public Task<IEnumerable<ResourceDto>> Handle(
        GetLibraryFileLinkedResourceListQuery query,
        CancellationToken cancellationToken
    )
    {
        var items =

            // Kpi result capabilities
            _db.KpiResultCapabilities
                .AsExpandable()
                .Where(x => x.LibraryFileLinks.Any(y => y.LibraryFileId == query.Id))
                .Select(ResourceDto.KpiResultCapabilityMapper(HelperFunctions.GetLanguageCode()))

                // Capabilities
                .Union(_db.Capabilities
                    .AsExpandable()
                    .Where(x => x.LibraryFileLinks.Any(y => y.LibraryFileId == query.Id))
                    .Select(ResourceDto.CapabilityMapper(HelperFunctions.GetLanguageCode())))

                // Kpi benchmarks
                .Union(_db.KpiBenchmarks
                    .AsExpandable()
                    .Where(x => x.LibraryFileId == query.Id)
                    .Select(ResourceDto.KpiBenchmarkMapper()))

                // Operations
                .Union(_db.Operations
                    .AsExpandable()
                    .Where(x => x.MainFLowChartId == query.Id)
                    .Select(ResourceDto.OperationMapper(HelperFunctions.GetLanguageCode())))

                // Operation enhancements
                .Union(_db.OperationEnhancements
                    .AsExpandable()
                    .Where(x => x.FlowChartFileId == query.Id || x.BusinessModelFileId == query.Id ||
                                x.FileLinks.Any(y => y.FileId == query.Id))
                    .Select(ResourceDto.OperationEnhancementMapper(HelperFunctions.GetLanguageCode())))

                // Plan subsubtasks
                .Union(_db.PlanSubsubtasks
                    .AsExpandable()
                    .Where(x => x.LibraryFileLinks.Any(y => y.LibraryFileId == query.Id))
                    .Select(ResourceDto.PlanSubsubtaskMapper(HelperFunctions.GetLanguageCode())))

                // Benchmarks
                .Union(_db.Benchmarks
                    .AsExpandable()
                    .Where(x => x.LibraryFileLinks.Any(y => y.LibraryFileId == query.Id))
                    .Select(ResourceDto.BenchmarkMapper()))

                // Tournaments
                .Union(_db.Tournaments
                    .AsExpandable()
                    .Where(x => x.LibraryFileId == query.Id)
                    .Select(ResourceDto.TournamentMapper(HelperFunctions.GetLanguageCode())))

                // Standards
                .Union(_db.Standards
                    .AsExpandable()
                    .Where(x => x.TeamFormationLibraryFileId == query.Id ||
                                x.PresentationLibraryFileId == query.Id ||
                                x.LibraryFileLinks.Any(y => y.LibraryFileId == query.Id))
                    .Select(ResourceDto.StandardMapper(HelperFunctions.GetLanguageCode())))

                // Operation procedures
                .Union(_db.OperationProcedures
                    .AsExpandable()
                    .Where(x => x.FlowchartFileId == query.Id)
                    .Select(ResourceDto.OperationProcedureMapper(HelperFunctions.GetLanguageCode())))

                // Improvement opportunities
                .Union(_db.ImprovementOpportunities
                    .AsExpandable()
                    .Where(x => x.LibraryFileLinks.Any(y => y.LibraryFileId == query.Id))
                    .Select(ResourceDto.ImprovementOpportunityMapper()))

                // Standard subtasks
                .Union(_db.StandardSubtasks
                    .AsExpandable()
                    .Where(x => x.LibraryFileLinks.Any(y => y.LibraryFileId == query.Id))
                    .Select(ResourceDto.StandardSubtaskMapper(HelperFunctions.GetLanguageCode())))

                // Library Tags
                .Union(_db.LibraryTags
                    .AsExpandable()
                    .Where(x => x.LibraryFileLinks.Any(y => y.LibraryFileId == query.Id))
                    .Select(ResourceDto.LibraryTagsMapper(HelperFunctions.GetLanguageCode())))

                // Operation Procedure Steps
                .Union(_db.OperationProcedureSteps
                    .AsExpandable()
                    .Where(x => x.UsedModelFileId == query.Id)
                    .Select(ResourceDto.OperationProcedureStepsMapper()))

                // Enhancements As FlowChart Files
                .Union(_db.EnhancementsAsFlowChartFiles
                    .AsExpandable()
                    .Where(x => x.FlowChartFileId == query.Id || x.BusinessModelFileId == query.Id)
                    .Select(ResourceDto.EnhancementsAsFlowChartFilesMapper()))
                .AsEnumerable();

        return Task.FromResult(items);
    }
}
