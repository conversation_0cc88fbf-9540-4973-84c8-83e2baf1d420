using Injaz.Core.Constants;
using Injaz.Core.Exceptions;
using Injaz.Core.Misc;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services.FileManager;
using Injaz.ModuleLibrary.Core.Constants;
using Injaz.ModuleLibrary.Core.Expressions;
using Injaz.ModuleLibrary.Core.Queries;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleLibrary.Handlers;

public class GetStreamHandler : IRequestHandler<GetLibraryFileStreamQuery, FileContent>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IFileManager _fileManager;

    public GetStreamHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer,
        IFileManager fileManager
    )
    {
        _db = db;
        _localizer = localizer;
        _fileManager = fileManager;
    }

    public async Task<FileContent> Handle(GetLibraryFileStreamQuery query, CancellationToken cancellationToken)
    {
        var scopes = new List<string> { LibraryFileScopes.Shared };

        if (query.CurrentUserHasPlanReadPermission) scopes.Add(LibraryFileScopes.ModulePlan);

        var libraryFile = _db
            .LibraryFiles
            .Where(x => x.Id.Equals(query.Id))
            .Where(ScopeHelper.MatchScopesExpression(scopes.ToArray()))
            .Select(x => new { x.FileName, x.ContentType })
            .FirstOrDefault();

        if (libraryFile == null) throw new ItemNotFoundException();

        if (string.IsNullOrEmpty(libraryFile.FileName))
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["the_library_item_does_not_have_a_file"] }
            };
        }

        var stream = await _fileManager.GetAsync($"{Directories.Library}/{libraryFile.FileName}");

        return new FileContent(stream, libraryFile.ContentType);
    }
}
