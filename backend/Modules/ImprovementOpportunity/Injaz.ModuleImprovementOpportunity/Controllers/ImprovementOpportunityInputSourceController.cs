using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.ImprovementOpportunityInputSource;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleImprovementOpportunity.Controllers;

[Authorize(PermissionNameList.ImprovementOpportunityInputSource)]
public class ImprovementOpportunityInputSourceController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public ImprovementOpportunityInputSourceController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/improvement-opportunity-input-source")]
    public IActionResult List(
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.ToLower().Trim() ?? "";

        var items = _appDataContext
            .ImprovementOpportunityInputSources
            .AsExpandable()
            .Where(x => x.NameAr.Contains(keyword)
                        || x.NameEn.ToLower().Contains(keyword)
            );


        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: new
        {
            Items = items
                .OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .Select(ImprovementOpportunityInputSourceListDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.ImprovementOpportunityInputSources.Count(),
            FilteredCount = items.Count()
        });
    }

    [HttpGet]
    [Route("/improvement-opportunity-input-source/{id}")]
    public IActionResult Get(
        Guid id,
        bool forEdit = false
    )
    {
        var item = forEdit
            ? _appDataContext.ImprovementOpportunityInputSources.AsExpandable()
                .Select(ImprovementOpportunityInputSourceEditDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id)) as object
            : _appDataContext.ImprovementOpportunityInputSources.AsExpandable()
                .Select(ImprovementOpportunityInputSourceGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/improvement-opportunity-input-source")]
    public IActionResult Create(
        [BindBodySingleJson] ImprovementOpportunityInputSourceCreateDto improvementOpportunityInputSource
    )
    {
        return CreateOrUpdate(improvementOpportunityInputSource);
    }

    [HttpPut]
    [Route("/improvement-opportunity-input-source")]
    public IActionResult Update(
        [BindBodySingleJson] ImprovementOpportunityInputSourceEditDto improvementOpportunityInputSource
    )
    {
        return CreateOrUpdate(improvementOpportunityInputSource);
    }

    [HttpDelete]
    [Route("/improvement-opportunity-input-source/{id}")]
    public IActionResult Delete(
        Guid id
    )
    {
        // Get item.
        var data = _appDataContext.ImprovementOpportunityInputSources
            .Where(x => x.Id == id)
            .Select(x => new
            {
                ImprovementOpportunityInputSource = x,
                IsLinkedToOtherResources =
                    x.ImprovementOpportunityLinks.Any()
            })
            .SingleOrDefault();

        // Ensure the item exists.
        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Ensure nothing is linked to the improvementOpportunityInputSource.
        if (data.IsLinkedToOtherResources)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["cannot_delete_item_item_connected_to_other_resources"] }
            );
        }

        // _appDataContext.ImprovementOpportunityInputSourceUserLinks.RemoveRange(
        //     _appDataContext.ImprovementOpportunityInputSourceUserLinks.Where(x => x.ImprovementOpportunityInputSourceId.Equals(id)));

        // Remove the item.
        _appDataContext.ImprovementOpportunityInputSources.Remove(data.ImprovementOpportunityInputSource);

        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            messages: new string[] { _localizer["item_removed_successfully"] }
        );
    }

    private IActionResult CreateOrUpdate(
        ImprovementOpportunityInputSourceCreateDto improvementOpportunityInputSource)
    {
        if (!Validate(improvementOpportunityInputSource, out var errorResult))
        {
            return errorResult;
        }

        ImprovementOpportunityInputSource item;
        if (improvementOpportunityInputSource is ImprovementOpportunityInputSourceEditDto
            existingImprovementOpportunityInputSource)
        {
            item = _appDataContext
                .ImprovementOpportunityInputSources
                .AsExpandable()
                .FirstOrDefault(x => x.Id.Equals(existingImprovementOpportunityInputSource.Id));

            // Ensure the item exists.
            if (item == null)
            {
                return this.GetResponseObject(success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] }
                );
            }
        }
        else
        {
            item = new ImprovementOpportunityInputSource();
        }

        item.NameAr = improvementOpportunityInputSource.NameAr;
        item.NameEn = improvementOpportunityInputSource.NameEn;

        // Add the item if it is new.
        if (!(improvementOpportunityInputSource is ImprovementOpportunityInputSourceEditDto))
        {
            _appDataContext.ImprovementOpportunityInputSources.Add(item);
        }

        _appDataContext.SaveChanges();

        // Return the item using the appropriate dto.
        return this.GetResponseObject(extra: _appDataContext
            .ImprovementOpportunityInputSources.AsExpandable()
            .Select(ImprovementOpportunityInputSourceGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .First(x => x.Id.Equals(item.Id))
        );
    }

    private bool Validate(ImprovementOpportunityInputSourceCreateDto improvementOpportunityInputSource,
        out IActionResult errorResult)
    {
        errorResult = null;

        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            errorResult = this.GetResponseObject(success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage))
                    .ToArray()
            );
            return false;
        }

        // Clean
        improvementOpportunityInputSource.NameAr = improvementOpportunityInputSource.NameAr.Trim();
        improvementOpportunityInputSource.NameEn = HelperFunctions.Default(
            improvementOpportunityInputSource.NameEn?.Trim(), improvementOpportunityInputSource.NameAr);

        return true;
    }
}
