using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Identity;
using Injaz.Core.Extensions;
using Injaz.Core.Models;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.Core.Services.FileManager;
using Microsoft.AspNetCore.Authorization;

namespace Injaz.ModuleInnovation.Controllers;

[Authorize]
public partial class InnovationController: Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly FileTypeDetectorService _fileTypeDetectorService;
    private readonly UserManager<Core.Models.DomainClasses.App.User> _userManager;
    private readonly IFileManager _fileManager;

    public InnovationController(UserManager<Core.Models.DomainClasses.App.User> userManager,
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer,
        FileTypeDetectorService fileTypeDetectorService,
        IFileManager fileManager)
    {
        _userManager = userManager;
        _appDataContext = appDataContext;
        _localizer = localizer;
        _fileTypeDetectorService = fileTypeDetectorService;
        _fileManager = fileManager;
    }

    private async Task<(bool Succeeded, IActionResult ErrorResult)> CanAccessInnovation(Guid id)
    {
        var canManageInnovation = await this.EnsureUserHasPermission(PermissionNameList.Innovation);
        if(canManageInnovation)
        {
            return (true, null);
        }

        //get current logged in user
        var user = await _userManager.GetUserAsync(User);

        //check if the user has innovation
        var hasInnovation = _appDataContext
            .Innovators
            .Where(x => x.EmployeeNumber == user.EmployeeNumber)
            .Select(x => x.Innovations.Any(a => a.Id.Equals(id)))
            .FirstOrDefault();



        if(!hasInnovation)
        {
            return(false, this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["cannot_access_this_resource"] }
            ));

        }
        return (true, null);
    }

}
