using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using MNMWebApp.Controllers;
using Injaz.Core.Extensions;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;

namespace Injaz.ModuleInnovator.Controllers;

[Authorize]
public partial class InnovatorController : Controller
{
    private readonly UserManager<User> _userManager;
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public InnovatorController(UserManager<User> userManager, AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer)
    {
        _userManager = userManager;
        _appDataContext = appDataContext;
        _localizer = localizer;
    }

    private async Task<(bool Succeeded, IActionResult ErrorResult)> CanAccess(Guid id)
    {
        var canManageInnovation = await this.EnsureUserHasPermission(PermissionNameList.Innovation);

        if (canManageInnovation)
        {
            return (true, null);
        }

        var user = await _userManager.GetUserAsync(User);


        var innovator = _appDataContext
            .Innovators
            .Where(x => x.EmployeeNumber == user.EmployeeNumber)
            .Select(x => new { x.Id })
            .FirstOrDefault();

        if (innovator == null || !innovator.Id.Equals(id))
        {
            return (false, this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["cannot_access_this_resource"] }
            ));
        }

        return (true, null);
    }
}
