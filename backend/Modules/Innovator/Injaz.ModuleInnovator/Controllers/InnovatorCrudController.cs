using LinqKit;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;
using MNMWebApp.Binders;
using InnovatorModel = Injaz.Core.Models.DomainClasses.App.Innovator;
using Microsoft.AspNetCore.Authorization;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Innovator;
using Injaz.Core.Permission;

namespace Injaz.ModuleInnovator.Controllers;

public partial class InnovatorController
{

    [HttpGet]
    [Route("/innovator")]
    [Authorize(Policy = PermissionNameList.Innovation)]
    public IActionResult List(
        string keyword = "",
        string employeeNumber = "",
        string rank ="",
        bool? hasALogo = null,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.ToLower().Trim() ?? "";
        employeeNumber = employeeNumber?.ToLower().Trim() ?? "";
        rank = rank?.ToLower().Trim() ?? "";

        var query = _appDataContext
            .Innovators
            .AsExpandable();

        if (!string.IsNullOrEmpty(keyword))
        {
            query = query.Where(x => x.NameAr.ToLower().Contains(keyword) || x.NameEn.ToLower().Contains(keyword));
        }

        if (!string.IsNullOrEmpty(employeeNumber))
        {
            query = query.Where(x => x.EmployeeNumber.ToLower().Contains(employeeNumber));
        }

        if (!string.IsNullOrEmpty(rank))
        {
            query = query.Where(x => x.Rank.ToLower().Contains(rank));
        }

        if(hasALogo != null)
        {
            query = query.Where(x => x.HasALogo == (hasALogo.Value ? 1 : 0));
        }

        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: new
        {
            Items = query
                .OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .Select(InnovatorListDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.Innovators.Count(),
            FilteredCount = query.Count()
        });
    }

    [HttpGet]
    [Route("/innovator/{id:guid}")]
    [Authorize(Policy = PermissionNameList.Innovator)]
    public async Task<IActionResult> Get(Guid id, bool forEdit = false)
    {
        var hasPermission = await CanAccess(id);
        if(!hasPermission.Succeeded)
        {
            return hasPermission.ErrorResult;
        }

        var item = forEdit
            ? _appDataContext.Innovators
                .AsExpandable()
                .Select(InnovatorEditDto.Mapper())
                .FirstOrDefault(p => p.Id.Equals(id)) as object
            : _appDataContext.Innovators
                .AsExpandable()
                .Select(InnovatorGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(p => p.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/innovator")]
    [Authorize(Policy = PermissionNameList.Innovation)]
    public IActionResult Create([BindBodySingleJson] InnovatorCreateDto innovator)
    {
        return CreateOrUpdate(innovator);
    }

    [HttpPut]
    [Route("/innovator")]
    [Authorize(Policy = PermissionNameList.Innovation)]
    public IActionResult Update([BindBodySingleJson] InnovatorEditDto innovator)
    {
        return CreateOrUpdate(innovator);
    }

    [HttpDelete]
    [Route("/innovator/{id:guid}")]
    [Authorize(Policy = PermissionNameList.Innovation)]
    public IActionResult Delete(Guid id)
    {
        var data = _appDataContext.Innovators.Find(id);


        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        _appDataContext.Innovators.Remove(data);
        _appDataContext.SaveChanges();

        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }

    [HttpPost]
    [Route("/innovator/toggle-logo/{id:guid}")]
    [Authorize(Policy = PermissionNameList.Innovation)]
    public IActionResult ToggleLogo(Guid id)
    {
        var item = _appDataContext.Innovators.Find(id);
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        item.HasALogo = item.HasALogo == 0 ? 1 : 0;
        _appDataContext.SaveChanges();

        var message = item.HasALogo == 1 ? "logo_has_been_added" : "logo_has_been_removed";
        return this.GetResponseObject
        (
            messages: new string[] { _localizer[message] }
        );
    }

    [HttpGet]
    [Route("/innovator/check-employee-number")]
    [Authorize(Policy = PermissionNameList.Innovation)]
    public IActionResult CheckEmployeeNumber(string employeeNumber)
    {
        var user = _appDataContext.Users.FirstOrDefault(x => x.EmployeeNumber == employeeNumber);
        var isInnovator = _appDataContext.Innovators.Any(x => x.EmployeeNumber == employeeNumber);

        return this.GetResponseObject
        (
            extra: new
            {
                IsUser = user != null,
                IsInnovator = isInnovator,
                NameAr = user?.NameAr,
                NameEn = user?.NameEn
            }
        );
    }

    [HttpGet]
    [Route("/innovator/current")]
    public async Task<IActionResult> GetCurrentInnovator()
    {
        var user = await _userManager.GetUserAsync(User);
        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: _appDataContext
            .Innovators
            .AsExpandable()
            .Where(x => x.EmployeeNumber == user.EmployeeNumber)
            .Select(InnovatorSimpleDto.Mapper(lang))
            .FirstOrDefault()
        );
    }

    private IActionResult CreateOrUpdate(InnovatorCreateDto innovator)
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        //check if there is already an innovator with the same employee number
        var doesExist = _appDataContext.Innovators.Any(x => x.EmployeeNumber == innovator.EmployeeNumber);

        InnovatorModel item;
        if (innovator is InnovatorEditDto innovatorEditDto)
        {
            item = _appDataContext.Innovators.Find(innovatorEditDto.Id);

            if (item == null)
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] }
                );
            }

            if(item.EmployeeNumber != innovator.EmployeeNumber && doesExist)
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["there_is_already_an_innovator_with_that_employee_number"] }
                );
            }
        }
        else
        {
            if(doesExist)
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["there_is_already_an_innovator_with_that_employee_number"] }
                );
            }
            item = new InnovatorModel();
            _appDataContext.Innovators.Add(item);
        }

        // Fill the fields.
        item.NameAr = innovator.NameAr.Trim();
        item.NameEn = innovator.NameEn?.Trim() ?? innovator.NameAr.Trim();
        item.EmployeeNumber = innovator.EmployeeNumber;
        item.Rank = innovator.Rank;
        item.HasALogo = innovator.HasALogo ? 1 : 0;


        _appDataContext.SaveChanges();

        return this.GetResponseObject
        (
            extra: _appDataContext
                .Innovators
                .AsExpandable()
                .Select(InnovatorSimpleDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }


}
