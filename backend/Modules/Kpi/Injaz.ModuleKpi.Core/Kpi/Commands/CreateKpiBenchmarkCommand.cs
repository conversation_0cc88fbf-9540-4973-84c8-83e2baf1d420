using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.KpiBenchmark;
using Injaz.Core.Dtos.LibraryFile;
using MediatR;

namespace Injaz.ModuleKpi.Core.Kpi.Commands;

public class CreateKpiBenchmarkCommand : IRequest<KpiBenchmarkGetDto>
{
    [Display(Name = "entity_name")]
    [Required(ErrorMessage = "0_is_required")]
    public string EntityName { get; set; }

    [Display(Name = "year")]
    [Required(ErrorMessage = "0_is_required")]
    public int? Year { get; set; }

    [Display(Name = "external_result")] public double? Result { get; set; }

    [Display(Name = "library_file")] public LibraryFileSimpleDto LibraryFile { get; set; }

    [Display(Name = "competitive_effect")] public string CompetitiveEffect { get; set; }

    // Used by the handler.
    public Guid? KpiId { get; set; }
    public Guid CurrentUserId { get; set; }
    public bool CurrentUserHasFullAccessPermission { get; set; }
}