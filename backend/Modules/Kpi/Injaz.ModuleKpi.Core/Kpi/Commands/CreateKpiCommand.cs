using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.KpiBalancedBehaviorCard;
using Injaz.Core.Dtos.KpiTag;
using Injaz.Core.Dtos.KpiType;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.OperationProcedure;
using Injaz.Core.Dtos.StrategicGoal;
using MediatR;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;

namespace Injaz.ModuleKpi.Core.Kpi.Commands;

public class CreateKpiCommand : IRequest<KpiGetDto>
{
    [Display(Name = "code")]
    [Required(ErrorMessage = "0_is_required")]
    public string Code { get; set; }

    [Display(Name = "type")]
    [Required(ErrorMessage = "0_is_required")]
    public KpiTypeWithCodeDto Type { get; set; }

    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "units")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       KpiModel.UnitsNumber + "|" +
                       KpiModel.UnitsPercentage + "|" +
                       KpiModel.UnitsAverage + "|" +
                       KpiModel.UnitsRate + "|" +
                       KpiModel.UnitsCurrency + "|" +
                       KpiModel.UnitsTime + "|" +
                       KpiModel.UnitsMinute + "|" +
                       KpiModel.UnitsHour + ")$",
        ErrorMessage = "0_is_invalid")]
    public string Units { get; set; }

    public string UnitsDescription { get; set; }

    [Display(Name = "formula")]
    [Required(ErrorMessage = "0_is_required")]
    public string Formula { get; set; }

    [Display(Name = "formula_description_for_a_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string FormulaDescriptionAAr { get; set; }

    [Display(Name = "formula_description_for_a_in_english")]
    public string FormulaDescriptionAEn { get; set; }

    [Display(Name = "formula_description_for_b_in_arabic")]
    public string FormulaDescriptionBAr { get; set; }

    [Display(Name = "formula_description_for_b_in_english")]
    public string FormulaDescriptionBEn { get; set; }

    // [Display(Name = "direction")]
    // [Required(ErrorMessage = "0_is_required")]
    // [RegularExpression("^(?:" +
    //                    KpiModel.DirectionIncrease + "|" +
    //                    KpiModel.DirectionDecrease +
    //                    ")$",
    //     ErrorMessage = "0_is_invalid")]
    // public string Direction { get; set; }
    //

    [Display(Name = "decrease_is_best")]
    [Required(ErrorMessage = "0_is_required")]
    public bool DecreaseIsBest { get; set; }

    [Display(Name = "source")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       KpiModel.SourceExternal + "|" +
                       KpiModel.SourceInternal +
                       ")$",
        ErrorMessage = "0_is_invalid")]
    public string Source { get; set; }


    [Display(Name = "creation_year")]
    [Required(ErrorMessage = "0_is_required")]
    public int CreationYear { get; set; }

    [Display(Name = "description_in_arabic")]
    public string DescriptionAr { get; set; }

    [Display(Name = "description_in_english")]
    public string DescriptionEn { get; set; }

    public string CalculateAbType { get; set; }

    [Display(Name = "cycle")]
    [RegularExpression(
        "^(?:" +
        KpiModel.MeasurementCycleAnnual + "|" +
        KpiModel.MeasurementCycleSemiAnnual + "|" +
        KpiModel.MeasurementCycleQuarter + "|" +
        KpiModel.MeasurementCycleMonth +
        ")$",
        ErrorMessage = "0_is_invalid")]
    public string MeasurementCycle { get; set; }

    [Display(Name = "calculation_method")]
    [RegularExpression(
        "^(?:" + KpiModel.MeasurementMethodLast + "|" +
        KpiModel.MeasurementMethodSplit + "|" +
        KpiModel.MeasurementMethodRepeat + ")$",
        ErrorMessage = "0_is_invalid")]
    public string MeasurementMethod { get; set; }

    [Display(Name = "data_entry_method")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       KpiModel.DataEntryMethodUponRequest + "|" +
                       KpiModel.DataEntryMethodDynamic +
                       ")$",
        ErrorMessage = "0_is_invalid")]
    public string DataEntryMethod { get; set; }

    [Display(Name = "entity")] public string Entity { get; set; }

    [Display(Name = "balanced_behavior_card")]
    public KpiBalancedBehaviorCardSimpleDto BalancedBehaviorCard { get; set; }

    [Display(Name = "owning_department")]
    [Required(ErrorMessage = "0_is_required")]
    public DepartmentSimpleDto OwningDepartment { get; set; }

    [Display(Name = "measuring_department")]
    public DepartmentSimpleDto MeasuringDepartment { get; set; }

    [Display(Name = "aggregation_type_a")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression(
        "^(?:" +
        KpiModel.AggregationTypeSum + "|" +
        KpiModel.AggregationTypeAverage + "|" +
        KpiModel.AggregationTypeLast +
        ")$",
        ErrorMessage = "0_is_invalid")]
    public string AggregationTypeA { get; set; }

    [Display(Name = "aggregation_type_b")]
    [RegularExpression(
        "^(?:" +
        KpiModel.AggregationTypeSum + "|" +
        KpiModel.AggregationTypeAverage + "|" +
        KpiModel.AggregationTypeLast +
        ")$",
        ErrorMessage = "0_is_invalid")]
    public string AggregationTypeB { get; set; }

    [Display(Name = "initial_result_source")]
    [RegularExpression(
        "^(?:" +
        KpiModel.InitialSourceBaseline + "|" +
        KpiModel.InitialSourceHistoricalData + "|" +
        KpiModel.InitialSourceRead +
        ")$",
        ErrorMessage = "0_is_invalid")]
    public string InitialResultSource { get; set; }

    [Display(Name = "initial_result")] public double? InitialResult { get; set; }

    [Display(Name = "initial_result_details")]
    public string InitialResultDetails { get; set; }

    [Display(Name = "tags")] public IEnumerable<KpiTagSimpleDto> Tags { get; set; }

    [Display(Name = "departments")]
    [Required(ErrorMessage = "0_is_required")]
    [MinLength(1, ErrorMessage = "0_should_be_at_least_1")]
    public IEnumerable<DepartmentListDto> Departments { get; set; }

    [Display(Name = "operations")] public IEnumerable<OperationWithLevelDto> Operations { get; set; }

    [Display(Name = "operation_procedures")]
    public IEnumerable<OperationProcedureWithCodeDto> OperationProcedures { get; set; }

    [Display(Name = "goals")] public IEnumerable<StrategicGoalSimpleDto> StrategicGoals { get; set; }

    [Display(Name = "is_special")] public bool IsSpecial { get; set; }

    [Display(Name = "is_trend")] public bool IsTrend { get; set; }

    [Display(Name = "is_exempt_from_evaluation")]
    public bool IsExemptFromEvaluation { get; set; }
    [Display(Name = "decimal_places")]
    public int DecimalPlaces { get; set; }
    // Used by the handler:
    public Guid CurrentUserId { get; set; }
    public bool CurrentUserHasFullAccessPermission { get; set; }
}
