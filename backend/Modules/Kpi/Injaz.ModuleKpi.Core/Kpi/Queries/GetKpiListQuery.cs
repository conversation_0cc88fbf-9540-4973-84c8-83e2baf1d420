using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.Misc;
using MediatR;

namespace Injaz.ModuleKpi.Core.Kpi.Queries;

public class GetKpiListQuery : IRequest<TableResultDto<KpiListDto>>
{
    public string Keyword { get; set; }
    public Guid[] TagIds { get; set; }
    public Guid[] DepartmentIds { get; set; }
    public Guid[] MeasuringDepartmentIds { get; set; }
    public Guid[] GoalIds { get; set; }
    public string[] Cycles { get; set; }
    public Guid[] TypeIds { get; set; }
    public string[] Progresses { get; set; }
    public int HasBenchmarks { get; set; } // -1 has none, 0 all, 1 has some
    public Guid[] StandardIds { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public string KpiNumber { get; set; }
    public string Status { get; set; }
    public int EvaluationStatus { get; set; } // -1 has none, 0 all, 1 has some
    public Guid[] EvaluationScoreBandIds { get; set; }
    public int Year { get; set; }
    public Guid CurrentUserId { get; set; }

    public Guid? EvaluationGroupId { get; set; }

    public Guid[] ExcludeEvaluationIds { get; set; }
    public bool CurrentUserHasFullAccessPermission { get; set; }
    public bool IsSharedKpi { get; set; }
    public bool ExcludeChildDepartments { get; set; }

    public bool IsExemptFromEvaluation { get; set; }
}
