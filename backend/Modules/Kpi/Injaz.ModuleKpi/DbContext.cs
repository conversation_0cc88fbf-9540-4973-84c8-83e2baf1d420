using Injaz.Core.Evaluate.Interfaces;
using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleKpi;

public class DbContext : AppBaseDbContext, IEvaluateDbContext
{
    public DbContext(DbContextOptions<DbContext> options, IServiceProvider serviceProvider)
        : base(options, serviceProvider)
    {
    }

    public DbSet<Injaz.Core.Models.DomainClasses.App.Kpi> Kpis { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<OperationProcedure> OperationProcedures { get; set; }
    public DbSet<KpiKpiTagLink> KpisKpiTags { get; set; }
    public DbSet<DepartmentKpiLink> DepartmentKpiLinks { get; set; }
    public DbSet<OperationKpiLink> OperationKpiLinks { get; set; }
    public DbSet<OperationProcedureKpiLink> OperationProcedureKpiLinks { get; set; }
    public DbSet<KpiStrategicGoalLink> KpiStrategicGoalLinks { get; set; }
    public DbSet<KpiResultCapability> KpiResultCapabilities { get; set; }
    public DbSet<KpiResultAttachment> KpiResultAttachments { get; set; }
    public DbSet<KpiResultTargetSettingMethod> KpiResultTargetSettingMethods { get; set; }
    public DbSet<KpiResultPeriod> KpiResultPeriods { get; set; }
    public DbSet<KpiResult> KpiResults { get; set; }
    public DbSet<Injaz.Core.Models.DomainClasses.App.KpiBalancedBehaviorCard> KpiBalancedBehaviorCards { get; set; }
    public DbSet<KpiBenchmark> KpiBenchmarks { get; set; }
    public DbSet<Evaluation> Evaluations { get; set; }
    public DbSet<EvaluationStandard> EvaluationStandards { get; set; }
    public DbSet<EvaluationInstance> EvaluationInstances { get; set; }
    public DbSet<EvaluationStandardRecord> EvaluationStandardRecords { get; set; }
    public DbSet<EvaluationScoreBand> EvaluationScoreBands { get; set; }
    public DbSet<EvaluationEntityLink> EvaluationEntityLinks { get; set; }
    public DbSet<User> Users { get; set; }
}
