using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.ModuleKpi.Core.Kpi.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;

namespace Injaz.ModuleKpi.Kpi.Controllers;

public class AttachmentController : Controller
{
    private readonly UserManager<User> _userManager;
    private readonly IMediator _mediator;

    public AttachmentController(
        UserManager<User> userManager,
        IMediator mediator
    )
    {
        _userManager = userManager;
        _mediator = mediator;
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.KpiRead)]
    [Route("/kpi/aggregate-attachment/{id:guid}")]
    public async Task<IActionResult> ListAttachments(
        Guid id,
        string keyword,
        int[] years = null,
        Guid[] departmentIds = null,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var userId = new Guid(_userManager.GetUserId(User));
        var hasFullAccessPermission = await this.EnsureUserHasPermission(PermissionNameList.FullAccess);

        return this.GetResponseObject(extra: await _mediator.Send(new GetKpiAttachmentListQuery
        {
            Id = id,
            Keyword = keyword,
            Years = years,
            DepartmentIds = departmentIds,
            PageSize = pageSize,
            PageNumber = pageNumber,
            CurrentUserId = userId,
            CurrentUserHasFullAccessPermission = hasFullAccessPermission
        }));
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.KpiRead)]
    [Route("/kpi/aggregate-year/{id:guid}")]
    public async Task<IActionResult> GetAggregatedYears(Guid id, Guid[] departmentIds = null)
    {
        var userId = new Guid(_userManager.GetUserId(User));
        var hasFullAccessPermission = await this.EnsureUserHasPermission(PermissionNameList.FullAccess);

        return this.GetResponseObject(extra: await _mediator.Send(new GetKpiAggregatedYearListQuery
        {
            Id = id,
            DepartmentIds = departmentIds,
            CurrentUserId = userId,
            CurrentUserHasFullAccessPermission = hasFullAccessPermission
        }));
    }

    [HttpGet]
    [Authorize(Policy = PermissionNameList.KpiRead)]
    [Route("/kpi/aggregate-department/{id:guid}")]
    public async Task<IActionResult> GetAggregatedDepartments(Guid id, int[] years = null)
    {
        var userId = new Guid(_userManager.GetUserId(User));
        var hasFullAccessPermission = await this.EnsureUserHasPermission(PermissionNameList.FullAccess);

        return this.GetResponseObject(extra: await _mediator.Send(new GetKpiAggregatedDepartmentListQuery
        {
            Id = id,
            Years = years,
            CurrentUserId = userId,
            CurrentUserHasFullAccessPermission = hasFullAccessPermission
        }));
    }

    [HttpGet]
    [Route("/kpi/aggregate-attachment/download/{attachmentId:guid}")]
    [Authorize(Policy = PermissionNameList.KpiRead)]
    public async Task<IActionResult> Download(Guid attachmentId)
    {
        var userId = new Guid(_userManager.GetUserId(User));
        var hasFullAccessPermission = await this.EnsureUserHasPermission(PermissionNameList.FullAccess);
        var result = await _mediator.Send(new GetKpiAggregatedAttachmentByIdQuery
        {
            AttachmentId = attachmentId,
            CurrentUserId = userId,
            CurrentUserHasFullAccessPermission = hasFullAccessPermission
        });

        return File(result.Stream, result.ContentType);
    }
}
