using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Department;
using Injaz.ModuleKpi.Core.Kpi.Queries;
using MediatR;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;

namespace Injaz.ModuleKpi.Kpi.Handlers;

public class GetAggregatedDepartmentListHandler :
    IRequestHandler<GetKpiAggregatedDepartmentListQuery, IEnumerable<DepartmentSimpleDto>>
{
    private readonly DbContext _db;

    public GetAggregatedDepartmentListHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<IEnumerable<DepartmentSimpleDto>> Handle(
        GetKpiAggregatedDepartmentListQuery request,
        CancellationToken cancellationToken
    )
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(HelperFunctions.GetLanguageCode());
        var isInvolvedWithKpiExpression = HelperExpression
            .IsInvolvedWithKpi(request.CurrentUserId, _db.Departments, true)
            .Or(x => request.CurrentUserHasFullAccessPermission);

        var kpis = _db
            .Kpis
            .AsExpandable()
            .Where(x => x.Status.Equals(KpiModel.StatusActive))
            .Where(x => isInvolvedWithKpiExpression.Invoke(x));

        return Task.FromResult(kpis
            .Where(x => x.Id == request.Id)
            .SelectMany(x => x.Results)
            .Where(x =>
                request.Years == null ||
                request.Years.Length == 0 ||
                request.Years.Contains(x.Year)
            )
            .Select(x => departmentExpression.Invoke(x.Department))
            .Distinct()
            .OrderBy(x => x.Name)
            .AsEnumerable()
        );
    }
}
