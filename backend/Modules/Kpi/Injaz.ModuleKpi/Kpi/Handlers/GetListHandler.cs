using System.Data.Entity;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Evaluate.Services;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Services;
using Injaz.ModuleKpi.Core.Kpi.Queries;
using MediatR;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;

namespace Injaz.ModuleKpi.Kpi.Handlers;

public class GetListHandler : IRequestHandler<GetKpiListQuery, TableResultDto<KpiListDto>>
{
    private readonly DbContext _db;
    private readonly AppSettingService _appSettingService;
    private readonly EvaluationService<KpiResultPeriod> _kpiResultPeriodEvaluationService;
    private readonly PermissionEnsurerService _permissionEnsurerService;

    public GetListHandler(
        DbContext db,
        AppSettingService appSettingService,
        EvaluationService<KpiResultPeriod> kpiResultPeriodEvaluationService,
        PermissionEnsurerService permissionEnsurerService)
    {
        _db = db;
        _appSettingService = appSettingService;
        _kpiResultPeriodEvaluationService = kpiResultPeriodEvaluationService;
        _permissionEnsurerService = permissionEnsurerService;
    }

    public async Task<TableResultDto<KpiListDto>> Handle(GetKpiListQuery request, CancellationToken cancellationToken)
    {
        request.Keyword = request.Keyword?.ToLower().Trim() ?? "";
        request.KpiNumber = request.KpiNumber?.ToLower().Trim() ?? "";
        var evaluationScoreExpression = _kpiResultPeriodEvaluationService.GetScoreExpression();
        var evaluationScoreDetailExpression = _kpiResultPeriodEvaluationService.GetScoreDetailExpression();


        // Return empty list if the status is hidden and
        // the user does not have full access.
        if (!request.CurrentUserHasFullAccessPermission && request.Status == KpiModel.StatusHidden)
        {
            return new TableResultDto<KpiListDto>()
            {
                Items = Array.Empty<KpiListDto>(),
                Count = 0,
                FilteredCount = 0
            };
        }

        // A predicate for filtering out kpis where the
        // user has no permission to see them.
        var permissionPredicate =
            HelperExpression
                .IsInvolvedWithKpi(request.CurrentUserId, _db.Departments, true)
                .Or(x => request.CurrentUserHasFullAccessPermission);

        var baseItems = _db
            .Kpis
            .AsExpandable()
            .Where(x => x.Status.Equals(request.Status))
            .Where(permissionPredicate);

        var items = baseItems
            .Where(x => x.NameAr.Contains(request.Keyword) || x.NameEn.ToLower().Contains(request.Keyword));

        if (request.TagIds is { Length: > 0 })
        {
            items = items.Where(x => x.TagLinks.Any(y => request.TagIds.Contains(y.TagId)));
        }

        if (request.DepartmentIds is { Length: > 0 })
        {
            var hierarchyCodes = _db
                .Departments
                .Where(x => request.DepartmentIds.Contains(x.Id))
                .Select(x => x.HierarchyCode)
                .ToList();

            Expression<Func<Department, bool>> predicate = x => false;
            hierarchyCodes.ForEach(x => predicate = predicate.Or(y => y.HierarchyCode.StartsWith(x)));

            items = items.Where(x => x.DepartmentLinks.Any(y => predicate.Invoke(y.Department)));

            // Additional filter when ExcludeChildDepartments is true
            if (request.ExcludeChildDepartments)
            {
                items = items.Where(x => x.DepartmentLinks
                    .Any(y => hierarchyCodes.Contains(y.Department.HierarchyCode)));
            }
        }

        if (request.MeasuringDepartmentIds is { Length: > 0 })
        {
            var hierarchyCodes = _db
                .Departments
                .Where(x => request.MeasuringDepartmentIds.Contains(x.Id))
                .Select(x => x.HierarchyCode)
                .ToList();

            Expression<Func<Department, bool>> predicate = x => false;
            hierarchyCodes.ForEach(x => predicate = predicate.Or(y => y.HierarchyCode.StartsWith(x)));

            items = items.Where(x => x.MeasuringDepartment != null && predicate.Invoke(x.MeasuringDepartment));
        }

        if (request.GoalIds is { Length: > 0 })
        {
            items = items.Where(x => x.StrategicGoalLinks.Any(y => request.GoalIds.Contains(y.StrategicGoalId)));
        }

        if (!string.IsNullOrEmpty(request.KpiNumber))
        {
            items = items.Where(x => x.Code.Contains(request.KpiNumber));
        }

        if (request.Cycles is { Length: > 0 })
        {
            items = items.Where(x => request.Cycles.Contains(x.MeasurementCycle));
        }

        if (request.TypeIds is { Length: > 0 })
        {
            items = items.Where(x => request.TypeIds.Contains(x.TypeId));
        }

        if (request.HasBenchmarks == -1)
        {
            items = items.Where(x => x.Benchmarks.Count == 0);
        }
        else if (request.HasBenchmarks == 1)
        {
            items = items.Where(x => x.Benchmarks.Count > 0);
        }

        if (request.StandardIds is { Length: > 0 })
        {
            items = items.Where(x => x.StandardLinks.Any(y => request.StandardIds.Contains(y.StandardId)));
        }

        if (request.EvaluationScoreBandIds is { Length: > 0 })
        {
            items = items.Where(x =>
                request.EvaluationScoreBandIds.Contains(evaluationScoreDetailExpression.Invoke(x.Id).ScoreBandId));
        }

        if (request.IsExemptFromEvaluation)
        {
            items= items.Where(x => x.IsExemptFromEvaluation == 1);
        }

        //
        // var kpisQuery = items
        //     .Select(kpi => new
        //     {
        //         Kpi = kpi,
        //         Periods = kpi.Results
        //             .SelectMany(r => r.Periods)
        //     })
        //     .AsQueryable();
        //
        // if (request.EvaluationGroupId != null || request.ExcludeEvaluationIds.Any() || request.EvaluationStatus != 0)
        // {
        //     kpisQuery = kpisQuery.Where(k =>
        //         k.Periods.Any(p =>
        //             _db.EvaluationInstances.Any(ei =>
        //                 ei.EntityId == p.Id
        //             )
        //         ));
        //     if (request.EvaluationGroupId != null)
        //     {
        //         kpisQuery = kpisQuery.Where(x => x.Periods.)
        //     }
        //
        // }

        if (request.EvaluationGroupId != null)
        {
            var isNotGlobal = _db.EvaluationEntityLinks
                .Any(x => x.EvaluationId == request.EvaluationGroupId);
            if (isNotGlobal)
            {
                var linkedKpisIds = _db.EvaluationEntityLinks
                    .Where(x => x.EvaluationId == request.EvaluationGroupId)
                    .Select(a => a.EntityId)
                    .Distinct()
                    .ToArray();
                items = items.Where(x => linkedKpisIds.Contains(x.Id));
            }
        }

        if (request.ExcludeEvaluationIds.Any())
        {
            // get the kpis ids of the selected evaluation groups
            var excludedKpiIds = _db.EvaluationEntityLinks
                .Where(x => request.ExcludeEvaluationIds.Contains(x.EvaluationId))
                .Select(x => x.EntityId)
                .Distinct().
                ToArray();
            items = items.Where(x => !excludedKpiIds.Contains(x.Id));

        }

        if (request.EvaluationStatus == 1 || request.EvaluationStatus == -1)
        {
            // get all the evaluated kpis ids, then filter ad per the filter terms
            // 1: get the list of periods of all items kpis because its the entity_id ins the evaluation instance
            var evaluatedPeriodsIds = items
                .SelectMany(x => x.Results.Where(a => a.Year == request.Year))
                .SelectMany(x => x.Periods.Select(y => y.Id))
                .Distinct()
                .ToArray();
            var evaluatedKpiIds = _db.EvaluationInstances
                .Where(x => evaluatedPeriodsIds.Contains(x.EntityId))
                .Distinct()
                .AsNoTracking()
                .AsQueryable();

            if (request.EvaluationGroupId != null)
            {
                evaluatedKpiIds = evaluatedKpiIds
                    .Where(x => x.EvaluationId == request.EvaluationGroupId);
            }

            if (request.ExcludeEvaluationIds.Any())
            {
                evaluatedKpiIds = evaluatedKpiIds
                    .Where(x =>
                        x.EvaluationId != null
                        && !request.ExcludeEvaluationIds.Contains(x.EvaluationId.Value));
            }

            var results = evaluatedKpiIds
                .Select(a => a.EntityId)
                .Distinct()
                .ToArray();

            if (request.EvaluationStatus == 1)
            {
                items = items.Where(x => results.Contains(x.Id));

            } else
            if (request.EvaluationStatus == -1)
            {
                items = items.Where(x => !results.Contains(x.Id));
            }
        }

        if (request.IsSharedKpi)
        {
            items = items
                .Where(x =>
                    x.Results.Any(r =>
                        r.InputModeA == KpiResult.InputModeAuto &&
                        r.InputModeB == KpiResult.InputModeAuto
                    )
                );
        }

        // To search the progress, first apply the dto, since
        // it contains the achievement and then filter based on it!
        var lang = HelperFunctions.GetLanguageCode();
        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;
        var scoreDetailsExpression = _kpiResultPeriodEvaluationService.GetScoreDetailExpression();
        var evaluateActionAbilityAction =
            _kpiResultPeriodEvaluationService.GetEvaluateActionAbilityForCurrentUserExpression(_db.Departments);
        var scoreDetailsPeriodExpression = _kpiResultPeriodEvaluationService.GetScoreDetailExpression();
        var evaluateActionAbilityPeriodExpression =
            _kpiResultPeriodEvaluationService.GetEvaluateActionAbilityForCurrentUserExpression(_db.Departments);
        var hasEvaluationExportPermission =
            await _permissionEnsurerService.Ensure(PermissionNameList.KpiResultPeriodExportEvaluation);

        var projectedItems = items
            // Code is a string that contains a number, so,
            // first order by length, and then by value.
            .OrderBy(x => x.Code.Length)
            .ThenBy(x => x.Code)
            .Select(KpiListDto.Mapper(
                lang,
                canAchievedBeNegative,
                hasEvaluationExportPermission,
                scoreDetailsExpression,
                evaluateActionAbilityAction,
                scoreDetailsPeriodExpression,
                evaluateActionAbilityPeriodExpression,
                _db.EvaluationInstances.AsExpandable(),
                _db.Users.AsExpandable(),
                request.Year
            ));

        if (request.Progresses is { Length: > 0 } &&
            request.Progresses.All(x => App.KpiProgressStates.Select(y => y.Id).Contains(x)))
        {
            // Build the predicate.
            Expression<Func<KpiListDto, bool>> predicate = x => false;

            foreach (var progress in request.Progresses)
            {
                var p = App.KpiProgressStates.FirstOrDefault(x => x.Id == progress);
                if (p == null) continue;

                if (p.From == null || p.To == null) predicate = predicate.Or(x => x.Achieved == null);
                else
                    predicate = predicate.Or(x => p.From < x.Achieved && x.Achieved < p.To);
            }

            projectedItems = projectedItems.Where(predicate);
        }

        return new TableResultDto<KpiListDto>
        {
            Items = projectedItems
                .Skip(request.PageSize * request.PageNumber)
                .Take(request.PageSize)
                .ToList(),
            Count = baseItems.Count(),
            FilteredCount = projectedItems.Count()
        };
    }
}
