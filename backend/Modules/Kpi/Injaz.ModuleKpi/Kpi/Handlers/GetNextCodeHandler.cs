using Injaz.ModuleKpi.Core.Kpi.Queries;
using MediatR;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;

namespace Injaz.ModuleKpi.Kpi.Handlers;

public class GetNextCodeHandler : IRequestHandler<GetKpiNextCodeQuery, int>
{
    private readonly DbContext _db;

    public GetNextCodeHandler(DbContext db)
    {
        _db = db;
    }

    public Task<int> Handle(GetKpiNextCodeQuery request, CancellationToken cancellationToken)
    {
        var lastCode = _db
            .Kpis
            .OrderByDescending(x => x.Code.Length)
            .ThenByDescending(x => x.Code)
            .Select(x => x.Code)
            .FirstOrDefault();

        return Task.FromResult(lastCode != null && int.TryParse(lastCode, out var code) ? code + 1 : 1);
    }
}
