using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Exceptions;
using Injaz.ModuleKpi.Core.Kpi.Queries;
using MediatR;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;

namespace Injaz.ModuleKpi.Kpi.Handlers;

public class GetResultYearAndDepartmentInfoHandler : IRequestHandler<GetKpiResultYearAndDepartmentInfoQuery,
    KpiWithResultYearAndDepartmentInfoDto>
{
    private readonly DbContext _db;

    public GetResultYearAndDepartmentInfoHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<KpiWithResultYearAndDepartmentInfoDto> Handle(GetKpiResultYearAndDepartmentInfoQuery query,
        CancellationToken cancellationToken)
    {
        var item = _db
            .Kpis
            .AsExpandable()
            .Where(x => x.Id.Equals(query.Id))
            .Where(x => x.Status.Equals(KpiModel.StatusActive))
            .Select(KpiWithResultYearAndDepartmentInfoDto.Mapper(
                HelperFunctions.GetLanguageCode(),
                DateTime.UtcNow.Year + 1,
                _db.Departments.AsQueryable(),
                query.CurrentUserId,
                query.CurrentUserHasFullAccessPermission
            ))
            .SingleOrDefault();

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
