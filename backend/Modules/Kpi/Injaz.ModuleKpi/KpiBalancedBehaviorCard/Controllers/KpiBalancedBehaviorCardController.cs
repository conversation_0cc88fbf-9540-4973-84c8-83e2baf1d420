using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleKpi.Core.KpiBalancedBehaviorCard.Commands;
using Injaz.ModuleKpi.Core.KpiBalancedBehaviorCard.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleKpi.KpiBalancedBehaviorCard.Controllers;

[Route("/kpi-balanced-behavior-card")]
[Authorize(Policy = PermissionNameList.KpiBalancedBehaviorCard)]
public class KpiBalancedBehaviorCardController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public KpiBalancedBehaviorCardController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    public async Task<IActionResult> List(string keyword = "", int pageNumber = 0, int pageSize = 20)
    {
        var response = await _mediator.Send(new GetKpiBalancedBehaviorCardListQuery()
        {
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        });

        return this.GetResponseObject(extra: response);
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> Get(Guid id, bool forEdit = false)
    {
        var item = await _mediator.Send(forEdit
            ? new GetKpiBalancedBehaviorCardForEditByIdQuery { Id = id }
            : new GetKpiBalancedBehaviorCardByIdQuery { Id = id }
        );

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreateKpiBalancedBehaviorCardCommand kpiBalancedBehaviorCard
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(kpiBalancedBehaviorCard));
    }

    [HttpPut]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] UpdateKpiBalancedBehaviorCardCommand kpiBalancedBehaviorCard
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(kpiBalancedBehaviorCard));
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _mediator.Send(new DeleteKpiBalancedBehaviorCardCommand() { Id = id });
        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }
}
