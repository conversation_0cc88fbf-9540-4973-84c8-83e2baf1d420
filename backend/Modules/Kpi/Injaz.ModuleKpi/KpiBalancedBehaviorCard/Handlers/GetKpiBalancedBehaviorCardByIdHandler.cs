using System.Linq.Dynamic.Core;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.KpiBalancedBehaviorCard;
using Injaz.Core.Dtos.ServiceProviderChannel;
using Injaz.Core.Exceptions;
using Injaz.ModuleKpi.Core.KpiBalancedBehaviorCard.Queries;
using MediatR;

namespace Injaz.ModuleKpi.KpiBalancedBehaviorCard.Handlers;

public class GetKpiBalancedBehaviorCardByIdHandler :
    IRequestHandler<GetKpiBalancedBehaviorCardByIdQuery, KpiBalancedBehaviorCardGetDto>

{
    private readonly DbContext _db;

    public GetKpiBalancedBehaviorCardByIdHandler(DbContext db) => _db = db;

    public Task<KpiBalancedBehaviorCardGetDto> Handle(GetKpiBalancedBehaviorCardByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .KpiBalancedBehaviorCards
            .AsExpandable()
            .Select(KpiBalancedBehaviorCardGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
