using System.Reflection;
using Injaz.Core;
using Injaz.Core.Evaluate.Extensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.ModuleKpi;

public static class KpiModule
{
    public static void Setup(IServiceCollection services, string connectionString)
    {
        services.AddMediatR(typeof(KpiModule));

        services.AddEvaluationConfigurations();

        // Add DbContext.
        var migrationsAssembly = typeof(HelperFunctions).GetTypeInfo().Assembly.GetName().Name;
        services
            .AddDbContext<DbContext>(options =>
                options.UseSqlServer(
                    connectionString,
                    b => b.MigrationsAssembly(migrationsAssembly))
            );
    }
}
