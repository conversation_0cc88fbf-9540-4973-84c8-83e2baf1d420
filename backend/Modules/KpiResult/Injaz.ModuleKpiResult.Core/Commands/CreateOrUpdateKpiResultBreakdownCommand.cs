using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.KpiResultCategory;
using Injaz.Core.Dtos.KpiResultSubcategory;
using Injaz.Core.Models.DomainClasses.App;
using MediatR;

namespace Injaz.ModuleKpiResult.Core.Commands;

public class CreateOrUpdateKpiResultBreakdownCommand : IRequest
{
    [Display(Name = "category")]
    [Required(ErrorMessage = "0_is_required")]
    public KpiResultCategorySimpleDto Category { get; set; }

    [Display(Name = "parameter")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       KpiResultBreakdown.ParameterA + "|" +
                       KpiResultBreakdown.ParameterB + ")$",
        ErrorMessage = "0_is_invalid")]
    public string Parameter { get; set; }

    [Display(Name = "is_mandatory")] public bool IsMandatory { get; set; }

    [Display(Name = "should_match_original_value")]
    public bool ShouldMatchOriginalValue { get; set; }

    [Display(Name = "subcategories")]
    [MinLength(1, ErrorMessage = "0_should_be_at_least_1")]
    public IEnumerable<KpiResultSubcategorySimpleDto> Subcategories { get; set; }

    public Guid Id { get; set; }
    public Guid CurrentUserId { get; set; }
    public bool CurrentUserHasFullAccessPermission { get; set; }
}
