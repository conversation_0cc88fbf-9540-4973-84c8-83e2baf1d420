using Injaz.Core.Dtos.KpiResult;
using MediatR;

namespace Injaz.ModuleKpiResult.Core.Queries;

public class GetKpiResultBreakdownByCategoryAndParameterQuery : IRequest<KpiResultBreakdownDto>
{
    public Guid Id { get; set; }
    public Guid CategoryId { get; set; }
    public string Parameter { get; set; }
    public Guid CurrentUserId { get; set; }
    public bool CurrentUserHasFullAccessPermission { get; set; }
}
