using Injaz.Core.Dtos.KpiResult;
using Injaz.Core.Dtos.Misc;
using MediatR;

namespace Injaz.ModuleKpiResult.Core.Queries;

public class GetKpiResultOwningListQuery : IRequest<TableResultDto<KpiResultSummaryDto>>
{
    public string Keyword { get; set; }
    public int? Year { get; set; }
    public Guid[] ExcludedIds { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
}