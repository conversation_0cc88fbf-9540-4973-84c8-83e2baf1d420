using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleKpiResult.Core.Commands;
using Injaz.ModuleKpiResult.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.ModuleKpiResult.Controllers;

public class KpiDynamicDataEntryRequestController : Controller
{
    private readonly ISender _mediator;
    private readonly UserManager<User> _userManager;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public KpiDynamicDataEntryRequestController(
        ISender mediator,
        UserManager<User> userManager,
        IStringLocalizer<SharedResource> localizer)
    {
        _mediator = mediator;
        _userManager = userManager;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/kpi-result/dynamic-data-entry-request")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> List(
        string keyword,
        string status = null, // new, approved
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var userId = Guid.Parse((ReadOnlySpan<char>)_userManager.GetUserId(User));
        return this.GetResponseObject(extra: await _mediator.Send(new GetKpiDynamicDataEntryRequestListQuery
        {
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize,
            Status = status,
            CurrentUserId = userId
        }));
    }

    [HttpGet]
    [Route("/kpi-result/dynamic-data-entry-request/{id:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> Get(Guid id)
    {
        var userId = Guid.Parse((ReadOnlySpan<char>)_userManager.GetUserId(User));
        return this.GetResponseObject(extra: await _mediator.Send(new GetKpiDynamicDataEntryRequestByIdQuery
        {
            Id = id,
            CurrentUserId = userId
        }));
    }

    [HttpGet]
    [Route("/kpi-result/dynamic-data-entry-request/{id:guid}/library-file")]
    [Authorize(Policy = PermissionNameList.KpiResultDynamicApprove)]
    public async Task<IActionResult> ListLinkedFiles(Guid id, string keyword, int pageNumber, int pageSize)
    {
        return this.GetResponseObject(extra: await _mediator.Send(
            new GetKpiDynamicDataEntryRequestLinkedLibraryFileListQuery
            {
                Id = id,
                Keyword = keyword,
                PageNumber = pageNumber,
                PageSize = pageSize
            }));
    }

    [HttpGet]
    [Route("/kpi-result/dynamic-data-entry-request/{id:guid}/library-file/unlinked")]
    [Authorize(Policy = PermissionNameList.KpiResultDynamicApprove)]
    public async Task<IActionResult> ListUnlinkedFiles(Guid id, string keyword, int pageNumber, int pageSize)
    {
        return this.GetResponseObject(extra: await _mediator.Send(
            new GetKpiDynamicDataEntryRequestUnlinkedLibraryFileListQuery
            {
                Id = id,
                Keyword = keyword,
                PageNumber = pageNumber,
                PageSize = pageSize
            }));
    }

    [HttpPost]
    [Route("/kpi-result/dynamic-data-entry-request/{id:guid}/library-file/{libraryFileId:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultDynamicApprove)]
    public async Task<IActionResult> Link(Guid id, Guid libraryFileId)
    {
        await _mediator.Send(new LinkKpiDynamicDataEntryRequestWithLibraryFileCommand
        {
            Id = id,
            LibraryFileId = libraryFileId
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpDelete]
    [Route("/kpi-result/dynamic-data-entry-request/{id:guid}/library-file/{libraryFileId:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultDynamicApprove)]
    public async Task<IActionResult> Unlink(Guid id, Guid libraryFileId)
    {
        await _mediator.Send(new UnlinkKpiDynamicDataEntryRequestFromLibraryFileCommand
        {
            Id = id,
            LibraryFileId = libraryFileId
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }
}
