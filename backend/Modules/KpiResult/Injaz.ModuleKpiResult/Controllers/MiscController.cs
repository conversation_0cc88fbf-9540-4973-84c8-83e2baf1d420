using Injaz.ModuleKpiResult.Core.Queries.KpiResultTargetSettingMethod;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;

namespace Injaz.ModuleKpiResult.Controllers;

[Route("/misc")]
public class MiscController : Controller
{
    private readonly IMediator _mediator;

    public MiscController(IMediator mediator) => _mediator = mediator;

    [HttpGet("kpi-result-target-setting-method")]
    public async Task<IActionResult> KpiResultTargetSettingMethods(string keyword, int pageSize = -1)
    {
        return this.GetResponseObject(
            extra: await _mediator.Send(new GetKpiResultTargetSettingMethodListForMiscQuery
            {
                Keyword = keyword,
                PageSize = pageSize
            })
        );
    }
}
