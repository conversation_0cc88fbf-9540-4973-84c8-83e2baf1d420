using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleKpiResult.Core.Commands;
using Injaz.ModuleKpiResult.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleKpiResult.Controllers;

public class RequestController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly UserManager<User> _userManager;

    public RequestController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer,
        UserManager<User> userManager
    )
    {
        _mediator = mediator;
        _localizer = localizer;
        _userManager = userManager;
    }

    [HttpGet]
    [Route("/kpi-result/request/list/{id:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> List(Guid id)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetKpiResultRequestListQuery
        {
            Id = id
        }));
    }

    [HttpGet]
    [Route("/kpi-result/request/{requestId:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> Get(Guid requestId)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetKpiResultRequestQuery
        {
            RequestId = requestId
        }));
    }

    [HttpPost]
    [Route("/kpi-result/request/{id:guid}")]
    [Authorize(Policy = PermissionNameList.FullAccess)]
    public async Task<IActionResult> Create([BindBodySingleJson] CreateKpiResultRequestCommand request, Guid id)
    {
        return await CreateOrUpdateRequest(request, id);
    }

    [HttpPut]
    [Route("/kpi-result/request")]
    [Authorize(Policy = PermissionNameList.FullAccess)]
    public async Task<IActionResult> Update([BindBodySingleJson] UpdateKpiResultRequestCommand request)
    {
        return await CreateOrUpdateRequest(request, null);
    }

    [HttpDelete]
    [Route("/kpi-result/request/{requestId:guid}")]
    [Authorize(Policy = PermissionNameList.FullAccess)]
    public async Task<IActionResult> DeleteRequest(Guid requestId)
    {
        await _mediator.Send(new DeleteKpiResultRequestCommand());

        return this.GetResponseObject
        (
            messages: new string[] { _localizer["item_removed_successfully"] }
        );
    }

    [HttpPost]
    [Route("/kpi-result/request/toggle-status/{requestId:guid}")]
    [Authorize(Policy = PermissionNameList.FullAccess)]
    public async Task<IActionResult> ToggleStatus(Guid requestId)
    {
        var message = await _mediator.Send(new ToggleKpiResultRequestStatusCommand
        {
            RequestId = requestId
        })
            ? "request_closed"
            : "request_opened";
        return this.GetResponseObject
        (
            messages: new string[] { _localizer[message] }
        );
    }

    [HttpGet]
    [Route("/kpi-result/request/comment/{commentId:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> GetComment(Guid commentId)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetKpiResultRequestCommentQuery
        {
            CommentId = commentId
        }));
    }

    [HttpPost]
    [Route("/kpi-result/request/comment/{requestId:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> CreateRequestComment(
        [BindBodySingleJson] CreateKpiResultRequestCommentCommand requestComment,
        Guid requestId)
    {
        return await CreateOrUpdateComment(requestComment, requestId);
    }

    [HttpPut]
    [Route("/kpi-result/request/comment")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> UpdateRequestComment(
        [BindBodySingleJson] UpdateKpiResultRequestCommentCommand requestCommentEdit)
    {
        return await CreateOrUpdateComment(requestCommentEdit, null);
    }

    [HttpDelete]
    [Route("/kpi-result/request/comment/{commentId:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> DeleteRequestComment(Guid commentId)
    {
        await _mediator.Send(new DeleteKpiResultRequestCommentCommand
        {
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User))
        });

        return this.GetResponseObject
        (
            messages: new string[] { _localizer["item_removed_successfully"] }
        );
    }

    private async Task<IActionResult> CreateOrUpdateRequest(
        CreateKpiResultRequestCommand comment,
        Guid? id
    )
    {
        comment.ResultId = id;
        return this.GetResponseObject(extra: await _mediator.Send(comment));
    }

    private async Task<IActionResult> CreateOrUpdateComment(
        CreateKpiResultRequestCommentCommand comment,
        Guid? requestId
    )
    {
        comment.CurrentUserId = Guid.Parse(_userManager.GetUserId(User));
        comment.CurrentUserHasFullAccessPermission =
            await this.EnsureUserHasPermission(PermissionNameList.FullAccess);
        comment.RequestId = requestId;

        return this.GetResponseObject(extra: await _mediator.Send(comment));
    }
}