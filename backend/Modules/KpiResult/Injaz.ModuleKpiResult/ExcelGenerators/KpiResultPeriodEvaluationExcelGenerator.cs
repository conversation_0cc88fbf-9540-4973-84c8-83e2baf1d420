using System.Drawing;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.ExcelGenerators.Misc;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Injaz.Core.Resources.Shared;
using Microsoft.Extensions.Localization;
using OfficeOpenXml.Style;

namespace Injaz.ModuleKpiResult.ExcelGenerators;

public class YearRecord
{
    public int Year { get; set; }
    public List<StandardRecord> Standards { get; set; }
    public double? AverageValue { get; set; }
}

public class StandardRecord
{
    public string Name { get; set; }
    public List<InstanceRecord> Group { get; set; }
    public double? AverageValue { get; set; }
}

public class InstanceRecord
{
    public string NameAr { get; set; }
    public string NameEn { get; set; }
    public double Target { get; set; }
    public double Value { get; set; }
    public string Note { get; set; }
    public EvaluationInstance Instance { get; set; }
}

public class KpiResultPeriodEvaluationExcelGenerator : IDisposable
{
    private readonly IList<KpiWithResultPeriodEvaluationExportDto> _data;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IQueryable<EvaluationScoreBand> _evaluationScoreBands;
    private readonly AppSetting _appSetting;
    private readonly List<YearRecord> _yearsStandardRecords;
    private int _totalRows;
    private int _totalColumns;

    // Calculate RGB values for darker mainColor
    private readonly Color _borderColor = Color.FromArgb(177 - 40, 216 - 40, 174 - 40); // cspell:ignore Argb

    private BaseExcelFile _file;

    private readonly Guid? _evaluationId;
    private readonly Guid[]? _allowedTagIds;

    private readonly Dictionary<Guid, string> _instanceIdToNote;

    private readonly int _fixedColumnCount = 7;

    public KpiResultPeriodEvaluationExcelGenerator(
        IList<KpiWithResultPeriodEvaluationExportDto> data,
        int[] years,
        IStringLocalizer<SharedResource> localizer,
        AppSetting appSetting,
        IQueryable<EvaluationScoreBand> evaluationScoreBands,
        IQueryable<EvaluationInstance> evaluationInstances,
        Guid? evaluationId = null,
        Guid[]? allowedTagIds = null
    )
    {
        _data = data;
        _localizer = localizer;
        _appSetting = appSetting;
        _evaluationScoreBands = evaluationScoreBands;
        _evaluationId = evaluationId;
        _allowedTagIds = allowedTagIds;

        _file = new BaseExcelFile();

        var allYears = years.Length != 0
            ? years
            : _data
                .SelectMany(x => x.Results)
                .Select(x => x.Year)
                .Distinct()
                .ToArray();

        // Set _yearsStandardRecords property.
        {
            var periods = _data
                .SelectMany(x => x.Results)
                .SelectMany(x => x.Periods)
                .ToList();

            var periodIds = periods.Select(x => x.Id).ToList();

            var allEvaluationInstances = evaluationInstances
                .Where(x => x.Type == "kpi_result_period")
                .Where(x => _evaluationId == null || x.EvaluationId == _evaluationId)
                .Where(x => periodIds.Contains(x.EntityId))
                .Select(x => new
                {
                    x.Id,
                    x.EntityId,
                    x.CreationTime,
                    x.Note,
                    Records = x.Records.Select(y => new InstanceRecord
                    {
                        NameAr = y.NameAr,
                        NameEn = y.NameEn,
                        Instance = y.Instance,
                        Target = y.Target,
                        Value = y.Value,
                        Note = y.Note
                    })
                })
                .ToList();

            // Used later to get the note for a particular instance
            _instanceIdToNote = allEvaluationInstances.ToDictionary(x => x.Id, x => x.Note);

            var allYearsStandardRecords = allEvaluationInstances
                .GroupBy(x => x.EntityId)
                .Select(g => g.MaxBy(x => x.CreationTime))
                .SelectMany(x => x.Records)
                .Select(x => new InstanceRecord
                {
                    NameAr = x.NameAr,
                    NameEn = x.NameEn,
                    Instance = x.Instance,
                    Target = x.Target,
                    Value = x.Value,
                    Note = x.Note
                })
                .Where(x => periodIds.Contains(x.Instance.EntityId))
                .GroupBy(x => new { x.NameAr, x.NameEn })
                .ToList();

            _yearsStandardRecords = allYears
                .OrderBy(x => x)
                .Select(year => new YearRecord
                {
                    Year = year,
                    Standards = allYearsStandardRecords
                        .Select(group => new StandardRecord
                        {
                            Name = HelperFunctions.GetLanguageCode() == SupportedCultures.LanguageArabic
                                ? group.Key.NameAr
                                : group.Key.NameEn,
                            Group = group.ToList(),
                        })
                        .ToList()
                })
                .ToList();
        }

        PreProcess();
    }

    public byte[] Generate()
    {
        if (HelperFunctions.GetLanguageCode() == SupportedCultures.LanguageArabic) _file.SetRtl();

        // General formatting.
        _file
            .Select(0, 0, _totalRows, _totalColumns)
            .SetBorder(ExcelBorderStyle.Thin)
            .SetVerticalAlignment(ExcelVerticalAlignment.Center)
            .SetTextWrap(true)
            .SetFontSize(11)
            .SetFontName("Sakkal Majalla")
            .Deselect();

        PopulateData();

        return _file.GetBytes();
    }

    private void PreProcess()
    {
        _totalRows = 2 + _data.Count;
        _totalColumns = _fixedColumnCount + _yearsStandardRecords.Select(x => x.Standards.Count + 4).Sum();
    }

    private void PopulateData()
    {
        PopulateHeaders();
        PopulateDataRows();
    }

    private void PopulateHeaders()
    {
        PrepHeaderCells().SetValue(_localizer["kpi_code"]).SetColumnWidth(20).MoveRight();

        PrepHeaderCells().SetValue(_localizer["name"]).SetColumnWidth(45).SetTextWrap(true).MoveRight();

        PrepHeaderCells().SetValue(_localizer["owning_department"]).SetColumnWidth(35).SetTextWrap(true).MoveRight();

        PrepHeaderCells().SetValue(_localizer["kpi_year_basis"])
            .SetColumnWidth(20)
            .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
            .MoveRight();

        PrepHeaderCells().SetValue(_localizer["kpi_tag"]).SetColumnWidth(35).SetTextWrap(true).MoveRight();

        PrepHeaderCells().SetValue(_localizer["cycle"]).SetColumnWidth(15).MoveRight();

        foreach (var yearRecord in _yearsStandardRecords)
        {
            PrepHeaderCells(1, yearRecord.Standards.Count + 4)
                .SetRowHeight(30)
                .SetValue(yearRecord.Year)
                .SetFontSize(18)
                .SetHorizontalAlignment(ExcelHorizontalAlignment.Center);

            _file.MoveDown();

            PrepSubHeaderCells().SetValue(_localizer["target"]).MoveRight();

            PrepSubHeaderCells().SetValue(_localizer["result"]).MoveRight();

            foreach (var standardRecord in yearRecord.Standards)
            {
                PrepSubHeaderCells().SetValue(standardRecord.Name).MoveRight();
            }

            PrepSubHeaderCells().SetValue(_localizer["notes"]).MoveRight();

            PrepSubHeaderCells().SetValue(_localizer["final_result"]).MoveRight();

            _file.MoveUp();
        }

        PrepHeaderCells()
            .SetValue(_localizer["final_years_result"])
            .SetColumnWidth(25)
            .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
            .MoveRight();

        _file.Move(_file.Position.Row + 2, 0);
    }

    private void PopulateDataRows()
    {
        var evaluationScoreBands = _evaluationScoreBands
            .Where(x => x.Evaluation.Type == "kpi_result_period")
            .Where(x => _evaluationId == null || x.EvaluationId == _evaluationId)
            .ToList();

        var yearScoreSums = new Dictionary<int, double?>();
        var yearScoreCounts = new Dictionary<int, int>();

        var finalScoreSum = (double?)null;
        var finalScoreCount = 0;

        foreach (var kpi in _data.OrderBy(x => x.OwningDepartment.HierarchyCode).ThenBy(x => x.Name))
        {
            var kpiScoreSum = (double?)null;
            var kpiScoreCount = 0;

            PrepCells()
                .SetValue($"{_appSetting.AppId}-{kpi.Type.Code}-{kpi.Code}")
                .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
                .MoveRight();

            PrepCells().SetValue(kpi.Name).MoveRight();

            PrepCells()
                .SetValue(kpi.OwningDepartment.Name)
                .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
                .MoveRight();

            PrepCells()
                .SetValue(kpi.CreationYear)
                .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
                .MoveRight();

            PrepCells()
                .SetValue(string.Join(" - ",
                    kpi.Tags.Where(x =>
                            _allowedTagIds == null || _allowedTagIds.Length == 0 || _allowedTagIds.Contains(x.Id))
                        .Select(x => x.Name)))
                .MoveRight();

            PrepCells()
                .SetValue(_localizer[kpi.MeasurementCycle])
                .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
                .MoveRight();


            foreach (var yearRecord in _yearsStandardRecords)
            {
                var result = kpi.Results.FirstOrDefault(x => x.Year == yearRecord.Year);

                var resultPeriods = result?.Periods.ToList();

                // Target
                PrepCells()
                    .SetValue(result?.Target)
                    .SetNumberFormat("#0.00")
                    .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
                    .MoveRight();

                // Result
                PrepCells()
                    .SetValue(result?.Result)
                    .SetNumberFormat("#0.00")
                    .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
                    .MoveRight();

                var isExempt = kpi.IsExemptFromEvaluation || (result?.IsExemptFromEvaluation ?? false);
                if (yearRecord.Year < kpi.CreationYear || isExempt)
                {
                    // 2: notes and final result for the year.
                    _file.MoveRight(yearRecord.Standards.Count + 2);
                    continue;
                }

                // Standards
                var notes = "";

                foreach (var standardRecord in yearRecord.Standards)
                {
                    var resultStandardInstances = resultPeriods?.GroupJoin(standardRecord.Group, x => x.Id,
                            x => x.Instance.EntityId,
                            (p, i) => new
                            {
                                Period = p,
                                Instance = i.FirstOrDefault()
                            })
                        .Select(x => new
                        {
                            x.Instance?.Instance.Id,
                            x.Period,
                            Score = x.Instance?.Value,
                            x.Instance?.Target,
                        })
                        .Where(x => x.Score != null)
                        .ToList();

                    // XXX: Hacky way to get the instance notes due to poor code structure.
                    var firstInstanceId = resultStandardInstances?.FirstOrDefault(x => x.Id != null)?.Id;
                    if (string.IsNullOrEmpty(notes) && firstInstanceId != null)
                    {
                        var instanceNote = new List<string>();
                        resultStandardInstances!.ForEach(res =>
                        {
                            instanceNote.Add(_instanceIdToNote[res.Id!.Value]);
                        });
                        notes = string.Join(Environment.NewLine, instanceNote);
                    }

                    standardRecord.AverageValue = resultStandardInstances?.Average(x => x.Score) ?? 0;

                    var cell = PrepCells()
                        .SetValue(standardRecord.AverageValue)
                        .SetNumberFormat("#0.00")
                        .SetHorizontalAlignment(ExcelHorizontalAlignment.Center);


                    // var standardTarget = resultPeriodsStandards.FirstOrDefault()?.Target;
                    var standardTarget = resultStandardInstances?.FirstOrDefault()?.Target;

                    var percentage = standardRecord.AverageValue == null ? null :
                        standardTarget == null ? standardRecord.AverageValue :
                        standardRecord.AverageValue / standardTarget;

                    if (percentage != null)
                    {
                        var color = evaluationScoreBands
                            .Where(x =>
                                (x.From == null && percentage < x.To) ||
                                (x.From <= percentage && x.To == null) ||
                                (x.From <= percentage && percentage < x.To)
                            )
                            .Select(x => x.Color)
                            .FirstOrDefault();

                        if (color != null)
                        {
                            var colorValue = HelperFunctions.ExtractColorFromString(color);
                            cell
                                .SetFontColor(Color.White)
                                .SetPatternType(ExcelFillStyle.Solid)
                                .SetBackgroundColor(Color.FromArgb(colorValue.Red, colorValue.Green, colorValue.Blue));
                        }
                    }

                    cell.MoveRight();
                }

                // Notes
                PrepCells()
                    .SetFontColor(Color.Gray)
                    .SetValue(notes.Trim())
                    .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
                    .SetTextWrap(true)
                    .MoveRight();

                // Result
                {
                    var totalScore = result?.TotalPeriodsEvaluationScore?.Value ?? 0;

                    yearScoreSums.TryAdd(yearRecord.Year, 0);
                    yearScoreSums[yearRecord.Year] += totalScore;

                    yearScoreCounts.TryAdd(yearRecord.Year, 0);
                    yearScoreCounts[yearRecord.Year] += 1;

                    kpiScoreSum = (kpiScoreSum ?? 0) + totalScore;
                    kpiScoreCount++;

                    SetScoreCell(
                        totalScore,
                        result?.TotalPeriodsEvaluationScore?.Color);

                    _file.MoveRight();
                }
            }

            // Final Results
            if (kpiScoreSum != null)
            {
                var finalResult = kpiScoreSum / kpiScoreCount;

                SetScoreCell(finalResult);

                finalScoreSum = (finalScoreSum ?? 0) + finalResult;
                finalScoreCount += 1;
            }

            _file.Move(_file.Position.Row + 1, 0);
        }

        // Compute and show the average scores for every year
        // across all the kpis.
        _file.MoveRight(5);
        foreach (var record in _yearsStandardRecords)
        {
            _file.MoveRight(4 + record.Standards.Count);
            if (!yearScoreSums.TryGetValue(record.Year, out var sum)) continue;
            var average = sum / yearScoreCounts[record.Year];
            SetScoreCell(average);
            _file.SetBorder(ExcelBorderStyle.Thin);
        }


        // Compute and show the average score of final scores across
        // all kpis.
        _file.MoveRight();
        SetScoreCell(finalScoreSum / finalScoreCount);
        _file.SetBorder(ExcelBorderStyle.Thin);


        // The function below takes as input an evaluation score
        // percentage and optional score color and prints the value
        // with the appropriate color background into the current
        // cell of the Excel file.
        void SetScoreCell(double? value, string? scoreColor = null)
        {
            if (value == null) return;

            _file
                .SetValue(value)
                .SetFontColor(Color.White)
                .SetNumberFormat("#0.00%")
                .SetHorizontalAlignment(ExcelHorizontalAlignment.Center);

            scoreColor ??= evaluationScoreBands
                .Where(x =>
                    (x.From == null && value < x.To) ||
                    (x.From <= value && x.To == null) ||
                    (x.From <= value && value < x.To)
                )
                .Select(x => x.Color)
                .FirstOrDefault();

            if (scoreColor != null)
            {
                var color = HelperFunctions.ExtractColorFromString(scoreColor);
                _file.SetPatternType(ExcelFillStyle.Solid)
                    .SetBackgroundColor(Color.FromArgb(color.Red, color.Green, color.Blue));
            }
        }
    }

    private BaseExcelFile PrepCells()
    {
        return _file.Select(
                _file.Position.Row,
                _file.Position.Col,
                _file.Position.Row + 1,
                _file.Position.Col + 1
            )
            .Deselect();
    }

    private BaseExcelFile PrepHeaderCells(int height = 2, int width = 1)
    {
        return _file.Select(
                _file.Position.Row,
                _file.Position.Col,
                _file.Position.Row + height,
                _file.Position.Col + width
            )
            .Merge()
            .SetRowHeight(40)
            .SetBold(true)
            .SetIndent(1)
            .SetPatternType(ExcelFillStyle.Solid)
            .SetBackgroundColor(_borderColor)
            .Deselect();
    }

    private BaseExcelFile PrepSubHeaderCells()
    {
        return _file
            .SetRowHeight(40)
            .SetBold(true)
            .SetIndent(1)
            .SetPatternType(ExcelFillStyle.Solid)
            .SetBackgroundColor(_borderColor)
            .SetColumnWidth(15)
            .SetHorizontalAlignment(ExcelHorizontalAlignment.Center);
    }


    public void Dispose()
    {
        _file.Dispose();
    }
}
