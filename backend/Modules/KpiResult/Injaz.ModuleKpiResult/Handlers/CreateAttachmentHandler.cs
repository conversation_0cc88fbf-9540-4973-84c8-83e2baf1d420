using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.KpiResultPeriodAttachment;
using Injaz.Core.Models.DomainClasses.App;
using MediatR;
using LinqKit;
using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.Core.Services.FileManager;
using Injaz.ModuleKpiResult.Core.Commands;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleKpiResult.Handlers;

public class CreateAttachmentHandler : IRequestHandler<CreateKpiResultAttachmentCommand, KpiResultAttachmentListDto>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly FileTypeDetectorService _fileTypeDetectorService;
    private readonly IFileManager _fileManager;

    public CreateAttachmentHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer,
        FileTypeDetectorService fileTypeDetectorService,
        IFileManager fileManager
    )
    {
        _db = db;
        _localizer = localizer;
        _fileTypeDetectorService = fileTypeDetectorService;
        _fileManager = fileManager;
    }

    public async Task<KpiResultAttachmentListDto> Handle(CreateKpiResultAttachmentCommand command,
        CancellationToken cancellationToken)
    {
        if (command.By != "result" && command.By != "response")
        {
            throw new ArgumentException("`by` should be either `result` or `response`");
        }

        // Checking if the user can modify the period
        var expression = HelperExpression
            .IsInvolvedWithKpiResult(command.CurrentUserId, _db.Departments, true)
            .Or(x => command.CurrentUserHasFullAccessPermission);


        var item = new KpiResultAttachment();

        if (command.By == "result")
        {
            var period = _db
                .KpiResultPeriods
                .AsExpandable()
                .Where(x => expression.Invoke(x.KpiResult))
                .Where(x => x.Id == command.Period.Id)
                .Select(x => new
                {
                    x.KpiResultId,
                    x.Period,
                    x.KpiResult.MeasurementCycle,
                    x.IsApproved
                })
                .FirstOrDefault();

            if (period == null)
            {
                throw new GenericException
                {
                    Messages = new string[] { _localizer["kpi_result_period_does_not_exist"] }
                };
            }

            if (period.IsApproved == 1)
            {
                throw new GenericException
                {
                    Messages = new string[] { _localizer["cannot_add_attachments_to_approved_period"] }
                };
            }


            item.KpiResultId = period.KpiResultId;
            item.Period = period.Period;
            item.PeriodType = period.MeasurementCycle;
        }

        // Response
        else
        {
            var period = _db
                .KpiResultDataEntryResponsePeriods
                .AsExpandable()
                .Where(x => expression.Invoke(x.Response.Result))
                .Where(x => x.Id == command.Period.Id)
                .Select(x => new
                {
                    x.ResponseId,
                    x.Period,
                    x.Response.Request.MeasurementCycle,
                    Response = new
                    {
                        CurrentTransfer = new
                        {
                            x
                                .Response
                                .Transfers
                                .OrderByDescending(y => y.CreationTime)
                                .First()
                                .Assignee
                        }
                    }
                })
                .FirstOrDefault();

            if (period == null)
            {
                throw new GenericException
                {
                    Messages = new string[] { _localizer["kpi_result_period_does_not_exist"] }
                };
            }

            // Ensure that the response is in the data entry phase.
            if (period.Response.CurrentTransfer.Assignee != KpiResultDataEntryResponseTransfer.AssigneeDataEntry)
            {
                throw new GenericException
                {
                    Messages = new string[]
                        { _localizer["cannot_add_attachments_to_a_response_that_is_not_under_kpi_entry"] }
                };
            }

            item.KpiResultDataEntryResponseId = period.ResponseId;
            item.Period = period.Period;
            item.PeriodType = period.MeasurementCycle;
        }

        // Get the content type of the file.
        await using var ms = new MemoryStream(command.File);
        var contentType = await _fileTypeDetectorService.GetContentTypeAsync(ms);
        ms.Seek(0, SeekOrigin.Begin);

        // Try and upload the file.
        var fileName = Guid.NewGuid().ToString();
        var result = await _fileManager.PutAsync(ms, Directories.KpiResultAttachments, fileName, contentType);
        if (!result.Succeeded)
        {
            throw new GenericException
            {
                Messages = new[] { result.ErrorMessage }
            };
        }

        // Create and add the new item.
        item.NameAr = command.NameAr;
        item.NameEn = command.NameEn;
        item.FileName = fileName;
        item.ContentType = contentType;

        await _db.KpiResultAttachments.AddAsync(item);
        _db.SaveChanges();

        return _db
            .KpiResultAttachments
            .Select(KpiResultAttachmentListDto.Mapper(HelperFunctions.GetLanguageCode()))
            .First(x => x.Id.Equals(item.Id));
    }
}
