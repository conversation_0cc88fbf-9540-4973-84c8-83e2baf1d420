using Injaz.Core;
using Injaz.Core.Dtos.Department;
using MediatR;
using LinqKit;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.ModuleKpiResult.Core.Queries;

namespace Injaz.ModuleKpiResult.Handlers;

public class
    GetDepartmentListHandler : IRequestHandler<GetKpiResultDepartmentListQuery, IEnumerable<DepartmentSimpleDto>>
{
    private readonly DbContext _db;

    public GetDepartmentListHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<IEnumerable<DepartmentSimpleDto>> Handle(GetKpiResultDepartmentListQuery request,
        CancellationToken cancellationToken)
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(HelperFunctions.GetLanguageCode());
        var permissionExpression = HelperExpression
            .IsInvolvedWithDepartment(request.CurrentUserId, _db.Departments, true)
            .Or(x => request.CurrentUserHasFullAccessPermission);


        // Get all departments that have results to the kpi
        // at that year. This could happen, since departments
        // can potentially be removed from a kpi while still
        // having results. 
        var resultDepartments = _db
            .KpiResults
            .AsExpandable()
            .Where(x => x.KpiId.Equals(request.KpiId) && x.Year.Equals(request.Year))
            .Where(x => permissionExpression.Invoke(x.Department))
            .Select(x => new
            {
                Item = departmentExpression.Invoke(x.Department),

                // If the department is owner of the result,
                // give it a higher priority in terms of ordering.
                // Note that we give owning departments here a value of
                // 2 instead of 1. This is because we want to give
                // owning department from the results a higher priority 
                // than the ones coming from kpis. Ownership from kpis
                // represent current ownership, whereas from results, represent
                // the ownership at the result's year (more accurate).
                Order = x.IsOwning == 1 ? 2 : 0,
            })
            .ToList();

        // Get all departments that are linked to the 
        // kpi and are not in the result departments.
        var resultDepartmentIds = resultDepartments.Select(x => x.Item.Id);
        var kpiDepartments = _db
            .Kpis
            .AsExpandable()
            .Where(x => x.Id.Equals(request.KpiId))
            .Where(x => x.Status.Equals(Kpi.StatusActive))
            .SelectMany(x => x.DepartmentLinks)
            .Where(x => !resultDepartmentIds.Contains(x.DepartmentId))
            .Where(x => permissionExpression.Invoke(x.Department))
            .Select(x => new
            {
                Item = departmentExpression.Invoke(x.Department),

                // If the department is the owner, give it a higher
                // priority in terms of ordering.
                Order = x.DepartmentId == x.Kpi.OwningDepartmentId ? 1 : 0
            })
            .ToList();

        // Merge departments.
        var departments = kpiDepartments.Union(resultDepartments)
            .OrderByDescending(x => x.Order)
            .ThenBy(x => x.Item.Name)
            .Select(x => x.Item);

        return Task.FromResult(departments);
    }
}