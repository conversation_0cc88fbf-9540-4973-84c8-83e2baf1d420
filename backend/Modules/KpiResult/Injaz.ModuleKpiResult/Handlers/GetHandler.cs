using LinqKit;
using Injaz.Core;
using Injaz.Core.DtoMappingStrategies;
using Injaz.Core.Dtos.KpiResult;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Services;
using Injaz.ModuleKpiResult.Core.Queries;
using MediatR;

namespace Injaz.ModuleKpiResult.Handlers;

public class GetHandler : IRequestHandler<GetKpiResultQuery, (KpiResultDto, bool)>
{
    private readonly DbContext _db;
    private readonly AppSettingService _appSettingService;

    public GetHandler(
        DbContext db,
        AppSettingService appSettingService
    )
    {
        _db = db;
        _appSettingService = appSettingService;
    }

    public Task<(KpiResultDto, bool)> Handle(GetKpiResultQuery request, CancellationToken cancellationToken)
    {
        request.Year ??= DateTime.UtcNow.Year;

        // A flag that tells us whether there's a result for the
        // kpi with the specified year and department.
        var isItemNew = false;

        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;

        var item = _db
            .KpiResults
            .AsExpandable()
            .Where(HelperExpression.IsInvolvedWithKpiResult(request.CurrentUserId, _db.Departments, true)
                .Or(x => request.CurrentUserHasFullAccessPermission))
            .Where(x => x.KpiId.Equals(request.KpiId) && x.Year.Equals(request.Year.Value) &&
                        x.DepartmentId.Equals(request.DepartmentId))
            .Map(new KpiResultDtoMappingStrategy(
                request.CurrentUserId,
                _db.Departments,
                request.CurrentUserHasFullAccessPermission,
                canAchievedBeNegative
            ))
            .FirstOrDefault();

        if (item == null)
        {
            item = _db
                .Kpis
                .AsExpandable()
                .Where(x => x.Id.Equals(request.KpiId))
                .Where(x => x.Status.Equals(Kpi.StatusActive))
                .Select(KpiResultDto.MapperFromLastYearOrKpi(
                    HelperFunctions.GetLanguageCode(),
                    request.Year.Value,
                    request.DepartmentId,
                    request.CurrentUserId,
                    _db.Departments,
                    request.CurrentUserHasFullAccessPermission
                ))
                .FirstOrDefault();
            isItemNew = true;

            if (item == null)
            {
                throw new ItemNotFoundException();
            }
        }

        return Task.FromResult((item, isItemNew));
    }
}
