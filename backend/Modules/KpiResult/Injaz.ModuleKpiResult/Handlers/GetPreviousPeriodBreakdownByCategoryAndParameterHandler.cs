using LinqKit;
using Injaz.Core;
using Injaz.Core.DtoMappingStrategies;
using Injaz.Core.Dtos.KpiResult;
using Injaz.ModuleKpiResult.Core.Queries;
using MediatR;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;

namespace Injaz.ModuleKpiResult.Handlers;

public class GetPreviousPeriodBreakdownByCategoryAndParameterHandler : IRequestHandler<
    GetKpiResultPreviousPeriodBreakdownByCategoryAndParameterQuery,
    KpiResultPeriodBreakdownDto
>
{
    private readonly DbContext _db;

    public GetPreviousPeriodBreakdownByCategoryAndParameterHandler(DbContext db) => _db = db;

    public Task<KpiResultPeriodBreakdownDto> Handle(GetKpiResultPreviousPeriodBreakdownByCategoryAndParameterQuery query,
        CancellationToken cancellationToken)
    {
        var predicate = HelperExpression
            .IsInvolvedWithKpiResult(query.CurrentUserId, _db.Departments, true)
            .Or(x => query.CurrentUserHasFullAccessPermission);

        var currentPeriod = _db.KpiResultPeriods
            .AsExpandable()
            .FirstOrDefault(x => x.Id == query.PeriodId);

        var item = _db
            .KpiResultPeriods
            .AsExpandable()
            .Where(x => x.Period == currentPeriod!.Period - 1
                        && x.KpiResultId == currentPeriod.KpiResultId)
            .Where(x => predicate.Invoke(x.KpiResult))
            .Map(new KpiResultPeriodBreakdownMappingStrategy(
                HelperFunctions.GetLanguageCode(),
                query.CategoryId,
                query.Parameter
            ))
            .FirstOrDefault()?
            .FirstOrDefault();

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
