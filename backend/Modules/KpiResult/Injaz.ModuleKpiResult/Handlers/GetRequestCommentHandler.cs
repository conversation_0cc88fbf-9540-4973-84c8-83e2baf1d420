using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.KpiResultRequest;
using Injaz.Core.Exceptions;
using Injaz.ModuleKpiResult.Core.Queries;
using MediatR;

namespace Injaz.ModuleKpiResult.Handlers;

public class GetRequestCommentHandler : IRequestHandler<GetKpiResultRequestCommentQuery, KpiResultRequestCommentEditDto>
{
    private readonly DbContext _db;

    public GetRequestCommentHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<KpiResultRequestCommentEditDto> Handle(GetKpiResultRequestCommentQuery query,
        CancellationToken cancellationToken)
    {
        var item = _db.KpiResultRequestComments
            .AsExpandable()
            .Select(KpiResultRequestCommentEditDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(r => r.Id == query.CommentId);

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
