using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.KpiResultTargetSettingMethod;
using Injaz.Core.Dtos.Misc;
using Injaz.ModuleKpiResult.Core.Queries.KpiResultTargetSettingMethod;
using MediatR;

namespace Injaz.ModuleKpiResult.Handlers.KpiResultTargetSettingMethod;

public class GetKpiResultTargetSettingMethodListHandler :
    IRequestHandler<GetKpiResultTargetSettingMethodListQuery, TableResultDto<KpiResultTargetSettingMethodGetDto>>
{
    private readonly DbContext _db;

    public GetKpiResultTargetSettingMethodListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<KpiResultTargetSettingMethodGetDto>> Handle(
        GetKpiResultTargetSettingMethodListQuery query,
        CancellationToken cancellationToken)
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .KpiResultTargetSettingMethods
            .AsExpandable()
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return Task.FromResult(
            new TableResultDto<KpiResultTargetSettingMethodGetDto>
            {
                Items = items
                    .Select(KpiResultTargetSettingMethodGetDto.Mapper(lang))
                    .OrderBy(x => x.Name)
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = _db.KpiResultTargetSettingMethods.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
