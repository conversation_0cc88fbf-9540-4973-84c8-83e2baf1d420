using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleKpiResult.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleKpiResult.Handlers;

public class LinkKpiDynamicDataEntryRequestWithLibraryFileHandler : IRequestHandler<
    LinkKpiDynamicDataEntryRequestWithLibraryFileCommand>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public LinkKpiDynamicDataEntryRequestWithLibraryFileHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer)
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(
        LinkKpiDynamicDataEntryRequestWithLibraryFileCommand command, CancellationToken cancellationToken)
    {
        // Ensure that both request and file exist.
        var request = _db.KpiDynamicDataEntryRequests.Find(command.Id);
        var file = _db.LibraryFiles.Find(command.LibraryFileId);
        if (request == null || file == null)
        {
            throw new ItemNotFoundException();
        }

        // Ensure that they are not already linked.
        var exists = _db.KpiDynamicDataEntryRequestLibraryFileLinks.Find(command.Id, command.LibraryFileId) != null;
        if (exists)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["request_already_linked_to_file"] }
            };
        }

        // Link them.
        _db.KpiDynamicDataEntryRequestLibraryFileLinks.Add(new KpiDynamicDataEntryRequestLibraryFileLink
        {
            RequestId = command.Id,
            LibraryFileId = command.LibraryFileId
        });
        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
