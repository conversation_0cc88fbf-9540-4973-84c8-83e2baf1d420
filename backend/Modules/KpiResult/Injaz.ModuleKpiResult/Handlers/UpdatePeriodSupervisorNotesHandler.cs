using Injaz.Core.Exceptions;
using Injaz.ModuleKpiResult.Core.Commands;
using MediatR;

namespace Injaz.ModuleKpiResult.Handlers;

public class UpdatePeriodSupervisorNotesHandler : IRequestHandler<UpdateKpiResultPeriodSupervisorNoteCommand>
{
    private readonly DbContext _db;

    public UpdatePeriodSupervisorNotesHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<Unit> Handle(UpdateKpiResultPeriodSupervisorNoteCommand command, CancellationToken cancellationToken)
    {
        var period = _db.KpiResultPeriods.Find(command.PeriodId);
        if (period == null)
        {
            throw new ItemNotFoundException();
        }

        period.SupervisorNote = command.Note;
        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}