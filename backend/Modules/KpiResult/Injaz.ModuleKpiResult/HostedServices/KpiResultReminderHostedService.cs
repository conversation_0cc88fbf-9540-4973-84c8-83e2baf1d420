using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Abstraction;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.User;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.SqlFunctions;
using Injaz.Core.Permission;
using Injaz.Core.Services;
using Injaz.ModuleEmail.Core.Commands;
using Injaz.ModuleNotification.Core.Commands;
using MediatR;

namespace Injaz.ModuleKpiResult.HostedServices;

public class KpiResultReminderHostedService : IntervalBasedHostedService
{
    public KpiResultReminderHostedService(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task Execute(
        AppDataContext db,
        IMediator mediator,
        AppSettingService appSettingService
    )
    {
        var lang = HelperFunctions.GetLanguageCode();

        var reminderConfigurations = appSettingService.Get().KpiSetting.ResultReminderConfigurations.ToList();

        // Remove duplications (if any)
        reminderConfigurations = reminderConfigurations
            .Where(x => !string.IsNullOrEmpty(x.MeasurementCycle) && !string.IsNullOrEmpty(x.OffsetType))
            .DistinctBy(x => new { x.MeasurementCycle, x.OffsetInDays, x.OffsetType })
            .ToList();

        if (!reminderConfigurations.Any()) return;

        // Figure out notification dates for each
        // cycle based on current date.
        var measurementCycleToConfigurationId = GetNotificationDates(reminderConfigurations);

        // Get measurement cycles that should fire a notification
        // for them giving its configuration and current date.
        var notifiableCycles = measurementCycleToConfigurationId
            .Where(x => x.Value != null)
            .Select(x => x.Key)
            .ToArray();

        // Get periods that could potentially be notified.
        var kpiResultPeriods = db
            .KpiResultPeriods
            .AsExpandable()
            .Where(x =>
                x.KpiResult.Kpi.Status == Kpi.StatusActive &&
                x.KpiResult.Year == DateTime.UtcNow.Year &&
                x.Period == ObtainKpiResultCurrentPeriodSqlFunction.Call(
                    x.KpiResult.Year,
                    x.KpiResult.MeasurementCycle
                ) &&

                // Ensure that it has a notifiable measurement cycle.
                notifiableCycles.Contains(x.KpiResult.MeasurementCycle)
            )

            // Only notify periods that do not have all their entries
            // entered yet.
            .Where(x =>
                (
                    ObtainKpiResultPeriodParamSqlFunction.Call(x.Id, "a") == null &&
                    x.KpiResult.InputModeA == KpiResult.InputModeManual
                ) ||
                (
                    x.KpiResult.Formula.Contains("B") &&
                    ObtainKpiResultPeriodParamSqlFunction.Call(x.Id, "b") == null &&
                    x.KpiResult.InputModeB == KpiResult.InputModeManual
                )
            )
            .Select(x => new
            {
                x.Id,
                KpiResult = new { x.KpiResult.Id, x.KpiResult.Year, x.KpiResult.MeasurementCycle },
                Kpi = KpiSimpleDto.Mapper(lang).Invoke(x.KpiResult.Kpi),
                Department = DepartmentSimpleDto.Mapper(lang).Invoke(x.KpiResult.Department),
                x.Period,
                Users = db
                    .Users
                    .Where(y => y.Status == User.StatusActive)
                    .Select(UserSimpleDto.Mapper(lang))
                    .Where(y =>
                        x.KpiResult.Department.UserLinks.Any(z => z.UserId == y.Id) &&
                        EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.KpiResultEntry, y.Id)
                    )
                    .ToList()
            })
            .Where(x => x.Users.Any())
            .ToList();

        var kpiResultPeriodIds = kpiResultPeriods.Select(x => x.Id).ToList();

        // Get all the relevant notifications for the fetched periods.
        // We need this to determine if the period has been notified
        // before or not.
        var allPeriodsNotifications = db
            .Notifications
            .Where(x =>
                x.TargetType == Notification.TargetTypeKpiResultPeriod &&
                x.TargetId != null &&
                kpiResultPeriodIds.Contains(x.TargetId.Value) &&
                x.TargetMetadata != null
            )
            .ToList();

        foreach (var kpiResultPeriod in kpiResultPeriods)
        {
            var data = measurementCycleToConfigurationId[kpiResultPeriod.KpiResult.MeasurementCycle]!.Value;

            // Check if this period has been notified before using the
            // its appropriate configuration (i.e., based on measurement cycle).
            var hasBeenNotified = allPeriodsNotifications
                .Where(x => x.TargetId == kpiResultPeriod.Id)
                .Select(x => new
                    {
                        Notification = x,
                        Metadata = JsonSerializer.Deserialize<NotificationReminderMetadata>(x.TargetMetadata)
                    }
                )
                .Any(x => x.Metadata.ConfigurationId == data.ConfigurationId);

            // If the reminder is already sent, skip it
            if (hasBeenNotified) continue;

            var metadata = new NotificationReminderMetadata
            {
                ConfigurationId = data.ConfigurationId,
                KpiId = kpiResultPeriod.Kpi.Id,
                DepartmentId = kpiResultPeriod.Department.Id,
                Year = kpiResultPeriod.KpiResult.Year
            };

            var year = kpiResultPeriod.KpiResult.Year;
            var kpiName = kpiResultPeriod.Kpi.Name;
            var department = kpiResultPeriod.Department.Name;
            var periodNumber = kpiResultPeriod.Period + 1;

            string titleAr, titleEn, descriptionAr, descriptionEn, periodNameAr, periodNameEn;

            switch (kpiResultPeriod.KpiResult.MeasurementCycle)
            {
                case Kpi.MeasurementCycleMonth:
                    periodNameAr = $"الشهر {periodNumber}";
                    periodNameEn = $"Month {periodNumber}";
                    break;
                case Kpi.MeasurementCycleQuarter:
                    periodNameAr = $"الربع {periodNumber}";
                    periodNameEn = $"Quarter {periodNumber}";
                    break;
                case Kpi.MeasurementCycleSemiAnnual:
                    periodNameAr = $"النصف {periodNumber}";
                    periodNameEn = $"Half {periodNumber}";
                    break;
                case Kpi.MeasurementCycleAnnual:
                    periodNameAr = periodNameEn = $"{year}";
                    break;
                default:
                    periodNameAr = periodNameEn = "";
                    break;
            }

            // We treat configurations that have offset type of `after` as
            // a notification that the period has started, otherwise, we
            // treat it as a notification that the period is about to end.
            if (data.Configuration.OffsetType == KpiSetting.KpiResultReminderConfiguration.OffsetTypeAfterStart)
            {
                titleAr = $"بدات فترة {periodNameAr} لادخال النتائج للمؤشر {kpiName}";
                titleEn = $"{periodNameEn} period for KPI {kpiName} has started.";
                descriptionAr =
                    $"تم بدء فترة {periodNameAr} للمؤشر {kpiName} للسنة {year} في {department}. يتطلب منك الآن بدء عملية إدخال النتائج.";
                descriptionEn =
                    $"The {periodNameEn} period for the KPI {kpiName} for the year {year} in {department} has commenced. It is now necessary to start entering the results.";
            }
            else
            {
                titleAr = $"تذكير: ستنتهي فترة {periodNameAr} للمؤشر {kpiName} برجاء ادخال النتائج في أسرع وقت";
                titleEn = $"Reminder: {periodNameEn} period for KPI {kpiName} will end soon.";
                descriptionAr =
                    $"ملاحظة: فترة {periodNameAr} للمؤشر {kpiName} للسنة {year} في {department} تقترب من النهاية. لا تتردد في إدخال النتائج في أقرب وقت ممكن.";
                descriptionEn =
                    $"Note: The {periodNameEn} period for the KPI {kpiName} for the year {year} in {department} is quickly coming to an end. Do not hesitate to enter the results as soon as possible.";
            }

            await mediator.Send(new CreateNotificationCommand
            {
                Users = kpiResultPeriod.Users,
                TitleAr = titleAr,
                TitleEn = titleEn,
                DescriptionAr = descriptionAr,
                DescriptionEn = descriptionEn,
                TargetType = Notification.TargetTypeKpiResultPeriod,
                TargetId = kpiResultPeriod.Id,
                TargetMetadata = JsonSerializer.Serialize(metadata)
            });

            await mediator.Send(new SendDefaultEmailCommand
            {
                UserIds = kpiResultPeriod.Users.Select(x => x.Id).ToArray(),
                SubjectAr = titleAr,
                SubjectEn = titleEn,
                BodyAr = descriptionAr,
                BodyEn = descriptionEn
            });
        }
    }

    protected override int GetTimerIntervalInSeconds()
    {
        // Run every 6 hours
        return 6 * 60 * 60;
    }

    private static
        Dictionary<string, (string ConfigurationId, KpiSetting.KpiResultReminderConfiguration Configuration)?>
        GetNotificationDates(
            IEnumerable<KpiSetting.KpiResultReminderConfiguration> reminderConfigurations)

    {
        var dateOnlyNow = DateOnly.FromDateTime(DateTime.UtcNow);

        return reminderConfigurations
            .GroupBy(x => x.MeasurementCycle)
            .Select(group =>
            {
                var measurementCycle = group.Key;
                var periodRange = GetCurrentPeriodRangeFromCycle(measurementCycle);

                return new
                {
                    MeasurementCycle = measurementCycle,

                    Data = group.Select(configuration =>
                        {
                            var notificationDate = configuration.OffsetType switch
                            {
                                KpiSetting.KpiResultReminderConfiguration.OffsetTypeAfterStart =>
                                    periodRange.Start.AddDays(configuration.OffsetInDays),
                                KpiSetting.KpiResultReminderConfiguration.OffsetTypeBeforeEnd =>
                                    periodRange.End.AddDays(-configuration.OffsetInDays),
                                _ => throw new Exception("Invalid offset type")
                            };

                            // Create a base64 hash from cycle, offset, and type to
                            // uniquely identify the configuration based on its content.
                            return notificationDate == dateOnlyNow
                                ? (
                                    Convert.ToBase64String(Encoding.UTF8.GetBytes(
                                        $"{measurementCycle}-{configuration.OffsetInDays}-{configuration.OffsetType}")),
                                    configuration
                                )
                                : ((string, KpiSetting.KpiResultReminderConfiguration)?)null;
                        })
                        .FirstOrDefault(x => x != null)
                };
            })
            .ToDictionary(x => x.MeasurementCycle, x => x.Data);
    }

    private static (DateOnly Start, DateOnly End) GetCurrentPeriodRangeFromCycle(string measurementCycle)
    {
        var now = DateTime.UtcNow;

        var date = new DateOnly(now.Year, 1, 1);

        return measurementCycle switch
        {
            Kpi.MeasurementCycleAnnual => (date, date.AddYears(1).AddDays(-1)),

            Kpi.MeasurementCycleSemiAnnual => now.Month < 7
                ? (date, date.AddMonths(6).AddDays(-1))
                : (date.AddMonths(6), date.AddYears(1).AddDays(-1)),

            Kpi.MeasurementCycleQuarter => now.Month < 4
                ? (date, date.AddMonths(3).AddDays(-1))
                : now.Month < 7
                    ? (date.AddMonths(3), date.AddMonths(6).AddDays(-1))
                    : now.Month < 10
                        ? (date.AddMonths(6), date.AddMonths(9).AddDays(-1))
                        : (date.AddMonths(9), date.AddYears(1).AddDays(-1)),

            Kpi.MeasurementCycleMonth => (date.AddMonths(now.Month - 1),
                date.AddMonths(now.Month).AddDays(-1)),
            _ => throw new Exception("Invalid measurement cycle")
        };
    }
}

// public class NotificationReminderMetadata
// {
//     [JsonPropertyName("isReminder")] public bool IsReminder { get; set; }
//     [JsonPropertyName("reminderType")] public string ReminderType { get; set; }
//     [JsonPropertyName("kpiId")] public Guid KpiId { get; set; }
//     [JsonPropertyName("departmentId")] public Guid DepartmentId { get; set; }
//     [JsonPropertyName("year")] public int Year { get; set; }
// }

public class NotificationReminderMetadata
{
    [JsonPropertyName("configurationId")] public string ConfigurationId { get; set; }
    [JsonPropertyName("kpiId")] public Guid KpiId { get; set; }
    [JsonPropertyName("departmentId")] public Guid DepartmentId { get; set; }
    [JsonPropertyName("year")] public int Year { get; set; }
}
