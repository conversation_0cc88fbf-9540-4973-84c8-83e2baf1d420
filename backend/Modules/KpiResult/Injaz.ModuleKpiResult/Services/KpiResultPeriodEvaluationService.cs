using System.Linq.Expressions;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Evaluate.Dtos;
using Injaz.Core.Evaluate.Services;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.SqlFunctions;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleKpiResult.Services;

public class KpiResultPeriodEvaluationService : EvaluationService<KpiResultPeriod>
{
    public KpiResultPeriodEvaluationService(
        DbContext db,
        CurrentUserService currentUserService,
        IStringLocalizer<SharedResource> localizer
    ) : base(db, currentUserService, localizer)
    {
    }

    public override Expression<Func<KpiResultPeriod, Guid, bool>> CanReadExpression(IQueryable<Department> departments)
    {
        var expression = HelperExpression.IsInvolvedWithKpi(departments, true);

        return (item, userId) =>
            EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.KpiResultPeriodEvaluateGlobalRead, userId) ||
            (
                EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.KpiResultPeriodEvaluateRead, userId) &&
                expression.Invoke(item.KpiResult.Kpi, userId)
            );
    }

    public override Expression<Func<KpiResultPeriod, Guid, bool>> CanWriteExpression(IQueryable<Department> departments)
    {
        return (item, userId) =>
            item.KpiResult.Kpi.IsExemptFromEvaluation == 0 &&
            item.KpiResult.IsExemptFromEvaluation == 0 &&
            EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.KpiResultPeriodEvaluate, userId);
    }

    public override IQueryable<EvaluationEntitySimpleDto> GetEntityListQueryable(string? keyword = null)
    {
        keyword = keyword?.Trim() ?? string.Empty;

        var lang = HelperFunctions.GetLanguageCode();

        var searchByCode = int.TryParse(keyword, out _);

        return GetDb()
            .Set<Kpi>()
            .AsExpandable()
            .Where(x => (!searchByCode && (x.NameAr.Contains(keyword) || x.NameEn.Contains(keyword))) ||
                        (searchByCode && x.Code == keyword)
            )
            .Where(x => x.Status == Kpi.StatusActive)
            .Select(x => new EvaluationEntitySimpleDto
            {
                Id = x.Id,
                Name = lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn
            })
            .OrderBy(x => x.Name);
    }

    protected override Expression<Func<Guid, bool>> IsExempt()
    {
        return entityId => GetDb().Set<KpiResultPeriod>()
            .Any(x => x.Id == entityId &&
                      (x.KpiResult.IsExemptFromEvaluation == 1 || x.KpiResult.Kpi.IsExemptFromEvaluation == 1));
    }
}
