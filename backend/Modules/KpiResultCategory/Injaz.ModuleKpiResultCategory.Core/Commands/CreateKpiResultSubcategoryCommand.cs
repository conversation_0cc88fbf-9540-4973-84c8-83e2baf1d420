using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.KpiResultCategory;
using Injaz.Core.Dtos.KpiResultSubcategory;
using MediatR;

namespace Injaz.ModuleKpiResultCategory.Core.Commands;

public class CreateKpiResultSubcategoryCommand : IRequest<KpiResultSubcategoryGetDto>
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "category")]
    [Required(ErrorMessage = "0_is_required")]
    public KpiResultCategorySimpleDto Category { get; set; }
}
