using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.KpiResultSubcategory;
using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModuleKpiResultCategory.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleKpiResultCategory.Handlers;

public class CreateOrUpdateSubcategoryHandler :
    IRequestHandler<CreateKpiResultSubcategoryCommand, KpiResultSubcategoryGetDto>,
    IRequestHandler<UpdateKpiResultSubcategoryCommand, KpiResultSubcategoryGetDto>
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly DbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdateSubcategoryHandler(
        IStringLocalizer<SharedResource> localizer,
        DbContext db,
        ValidationService validationService
    )
    {
        _localizer = localizer;
        _db = db;
        _validationService = validationService;
    }

    public Task<KpiResultSubcategoryGetDto> Handle(CreateKpiResultSubcategoryCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<KpiResultSubcategoryGetDto> Handle(UpdateKpiResultSubcategoryCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private Task<KpiResultSubcategoryGetDto> CreateOrUpdate(CreateKpiResultSubcategoryCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }


        KpiResultSubcategory item;

        if (command is UpdateKpiResultSubcategoryCommand updateCommand)
        {
            item = _db.KpiResultSubcategories.Find(updateCommand.Id);

            if (item == null)
            {
                throw new ItemNotFoundException();
            }
        }
        else
        {
            item = new KpiResultSubcategory();
            _db.KpiResultSubcategories.Add(item);
        }


        item.NameAr = command.NameAr.Trim();
        item.NameEn = command.NameEn?.Trim() ?? item.NameAr;
        item.CategoryId = command.Category.Id;

        _db.SaveChanges();


        // Return the newly created item using dtos.
        return Task.FromResult(
            _db
                .KpiResultSubcategories
                .AsExpandable()
                .Select(KpiResultSubcategoryGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id)));
    }
}
