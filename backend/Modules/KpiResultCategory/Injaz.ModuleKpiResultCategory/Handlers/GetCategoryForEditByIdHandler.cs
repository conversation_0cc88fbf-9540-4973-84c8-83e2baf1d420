using LinqKit;
using Injaz.Core.Dtos.KpiResultCategory;
using Injaz.Core.Exceptions;
using Injaz.ModuleKpiResultCategory.Core.Queries;
using MediatR;

namespace Injaz.ModuleKpiResultCategory.Handlers;

public class
    GetCategoryForEditByIdHandler : IRequestHandler<GetKpiResultCategoryForEditByIdQuery, KpiResultCategoryEditDto>
{
    private readonly DbContext _db;

    public GetCategoryForEditByIdHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<KpiResultCategoryEditDto> Handle(GetKpiResultCategoryForEditByIdQuery request,
        CancellationToken cancellationToken)
    {
        var item = _db.KpiResultCategories
            .AsExpandable()
            .Select(KpiResultCategoryEditDto.Mapper())
            .FirstOrDefault(x => x.Id.Equals(request.Id));


        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
