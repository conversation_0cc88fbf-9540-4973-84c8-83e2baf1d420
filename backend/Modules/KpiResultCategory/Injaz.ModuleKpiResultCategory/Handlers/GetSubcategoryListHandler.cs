using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.KpiResultSubcategory;
using Injaz.Core.Dtos.Misc;
using Injaz.ModuleKpiResultCategory.Core.Queries;
using MediatR;

namespace Injaz.ModuleKpiResultCategory.Handlers;

public class
    GetSubcategoryListHandler : IRequestHandler<GetKpiResultSubcategoryListQuery,
        TableResultDto<KpiResultSubcategoryListDto>>
{
    private readonly DbContext _db;

    public GetSubcategoryListHandler(DbContext db)
    {
        _db = db;
    }

    public Task<TableResultDto<KpiResultSubcategoryListDto>> Handle(GetKpiResultSubcategoryListQuery query,
        CancellationToken cancellationToken)
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .KpiResultSubcategories
            .AsExpandable()
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword))
            .Where(x => query.CategoryIds == null ||
                        query.CategoryIds.Length == 0 ||
                        query.CategoryIds.Contains(x.CategoryId));

        var lang = HelperFunctions.GetLanguageCode();

        return Task.FromResult(
            new TableResultDto<KpiResultSubcategoryListDto>
            {
                Items = items
                    .Select(KpiResultSubcategoryListDto.Mapper(lang))
                    .OrderBy(x => x.Name)
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = _db.KpiResultSubcategories.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
