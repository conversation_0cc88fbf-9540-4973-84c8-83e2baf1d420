using System.Reflection;
using Injaz.Core;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.ModuleKpiResultCategory;

public static class KpiResultCategoryModule
{
    public static void Setup(IServiceCollection services, string connectionString)
    {
        services.AddMediatR(typeof(KpiResultCategoryModule));

        // Add DbContext.
        var migrationsAssembly = typeof(HelperFunctions).GetTypeInfo().Assembly.GetName().Name;
        services
            .AddDbContext<DbContext>(options =>
                options.UseSqlServer(
                    connectionString,
                    b => b.MigrationsAssembly(migrationsAssembly))
            );
    }
}
