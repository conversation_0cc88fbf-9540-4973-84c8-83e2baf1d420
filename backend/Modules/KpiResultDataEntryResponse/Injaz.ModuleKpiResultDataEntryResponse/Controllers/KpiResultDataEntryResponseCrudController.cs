using System.Linq.Expressions;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;
using LinqKit;
using Injaz.Core;
using Injaz.Core.DtoMappingStrategies;
using Injaz.Core.Dtos.KpiResultDataEntryResponse;
using Injaz.Core.Dtos.User;
using Injaz.Core.Exceptions;
using Injaz.Core.Expressions.Kpi.DataEntry;
using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.SqlFunctions;
using Injaz.Core.Permission;
using Injaz.ModuleEmail.Core.Commands;
using Injaz.ModuleNotification.Core.Commands;
using Microsoft.AspNetCore.Authorization;

namespace Injaz.ModuleKpiResultDataEntryResponse.Controllers;

public partial class KpiResultDataEntryResponseController
{
    [HttpGet]
    [Route("/kpi-result-data-entry-response")]
    [Authorize(Policy = PermissionNameList.KpiRead)]
    public async Task<IActionResult> List(
        string keyword,
        Guid[]? kpiIds = null,
        Guid[]? departmentIds = null,
        string[]? assignees = null, // 'all', 'running', 'done'
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.Trim().ToLower() ?? "";

        var items = await GetValidResponses();

        // Get how many of them we have for the
        // current user...
        var count = items.Count();

        // ...Then apply the filter.

        Expression<Func<KpiResultDataEntryResponse, KpiResultDataEntryResponseTransfer>>
            currentTransfer = x => x
                .Transfers
                .OrderByDescending(y => y.CreationTime)
                .First();

        if (departmentIds is { Length: > 0 })
        {
            items = items.Where(x => departmentIds.Contains(x.Result.DepartmentId));
        }

        if (kpiIds is { Length: > 0 })
        {
            items = items.Where(x => kpiIds.Contains(x.Result.KpiId));
        }

        if (assignees is { Length: > 0 })
        {
            items = items.Where(x => assignees.Contains(currentTransfer.Invoke(x).Assignee));
        }

        items = items.Where(x =>
            x.Result.Kpi.NameAr.Contains(keyword) ||
            x.Result.Kpi.NameEn.ToLower().Contains(keyword)
        );

        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra:
            new
            {
                Items = items
                    .OrderByDescending(x => x.Request.CreationTime)
                    .ThenByDescending(x => x.CreationTime)
                    .Skip(pageSize * pageNumber)
                    .Take(pageSize)
                    .Select(KpiResultDataEntryResponseListDto.Mapper(lang)),
                Count = count,
                FilteredCount = items.Count(),
            }
        );
    }

    [HttpGet]
    [Route("/kpi-result-data-entry-response/{id:guid}")]
    [Authorize(Policy = PermissionNameList.KpiRead)]
    public async Task<IActionResult> Get(Guid id)
    {
        var lang = HelperFunctions.GetLanguageCode();
        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;
        var userId = Guid.Parse(_userManager.GetUserId(User));
        var hasKpiResultFlowPermission = await this.EnsureUserHasPermission(PermissionNameList.KpiResultFlow);
        var hasKpiPermission = await this.EnsureUserHasPermission(PermissionNameList.Kpi);

        var item = (await GetValidResponses())
            .Select(KpiResultDataEntryResponseGetDto.Mapper(
                lang,
                canAchievedBeNegative,
                _appDataContext.Departments.AsExpandable(),
                userId,
                hasKpiResultFlowPermission,
                hasKpiPermission
            ))
            .SingleOrDefault(x => x.Id == id);

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPut("/kpi-result-data-entry-response")]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] KpiResultDataEntryResponseEditDto response
    )
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(m => m.Value!.Errors.Select(e => e.ErrorMessage)).ToArray()
            );
        }

        var hasKpiResultWritePermission = await this.EnsureUserHasPermission(PermissionNameList.KpiResultFlow);

        // The only users who can write results to the response
        // are the ones with the kpi result write permission.
        if (!hasKpiResultWritePermission)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 401,
                messages: new string[] { _localizer["you_are_not_authorized_to_perform_this_action"] }
            );
        }

        var item = (await GetValidResponses())
            .Where(x => x.Id == response.Id)
            .Select(x => new
            {
                x.Periods,
                CurrentTransfer = new
                {
                    x.Transfers.OrderByDescending(y => y.CreationTime).First().Assignee
                }
            })
            .SingleOrDefault();

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Ensure that the response is with the data entry.
        if (item.CurrentTransfer.Assignee != KpiResultDataEntryResponseTransfer.AssigneeDataEntry)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["you_cannot_update_the_result_at_this_stage"] }
            );
        }

        item.Periods.ToList().ForEach(existingPeriod =>
        {
            var updatedPeriod = response.Periods.SingleOrDefault(y => y.Period == existingPeriod.Period);
            if (updatedPeriod == null) return;

            existingPeriod.A = updatedPeriod.A;
            existingPeriod.B = updatedPeriod.B;
            existingPeriod.ResultAnalysis = updatedPeriod.ResultAnalysis;
            existingPeriod.ImprovementProcedure = updatedPeriod.ImprovementProcedure;
            existingPeriod.ImprovementProcedureCompletionPercentage =
                updatedPeriod.ImprovementProcedureCompletionPercentage;
            existingPeriod.ImprovementProcedureExpectedCompletionDate =
                updatedPeriod.ImprovementProcedureExpectedCompletionDate;
        });

        _appDataContext.SaveChanges();

        return await Get(response.Id);
    }

    [HttpPut("/kpi-result-data-entry-response/move")]
    public async Task<IActionResult> Move(
        [BindBodySingleJson] KpiResultDataEntryResponseMoveDto response,
        string decision // 'transfer', 'reject', 'forward_to_kpi_manager'
    )
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(m => m.Value!.Errors.Select(e => e.ErrorMessage)).ToArray()
            );
        }

        // Ensure a valid decision.
        var validDecisions = new[] { "transfer", "reject", "forward_to_kpi_manager" };
        if (!validDecisions.Contains(decision))
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["invalid_decision"] }
            );
        }

        var lang = HelperFunctions.GetLanguageCode();
        var showUsersWithSignatureApprovalInTopLevels =
            _appSettingService.Get().KpiSetting.ShowUsersWithSignatureApprovalInTopLevels;
        var userId = Guid.Parse(_userManager.GetUserId(User));
        var hasKpiPermission = await this.EnsureUserHasPermission(PermissionNameList.Kpi);
        var hasKpiResultFlowPermission = await this.EnsureUserHasPermission(PermissionNameList.KpiResultFlow);
        var hasKpiResultDirectApprovePermission =
            await this.EnsureUserHasPermission(PermissionNameList.KpiResultDirectApprove);

        var expression = KpiResultDataEntryResponseNextTransferDto.Mapper(lang, _appDataContext.Users, userId,
            showUsersWithSignatureApprovalInTopLevels);

        var data = (await GetValidResponses())
            .Where(x => x.Id == response.Id)
            .Select(x => new
            {
                Item = x,
                ItemWithNext = expression.Invoke(x),
                CurrentTransfer = x
                    .Transfers
                    .OrderByDescending(y => y.CreationTime)
                    .Select(y => new
                    {
                        y.Assignee,
                        UserIds = y.UserLinks.Select(z => z.UserId).ToList()
                    })
                    .First(),

                Periods = x.Periods.Select(y => new
                    {
                        Item = y,

                        // Nullify the response period id and set the result period id
                        // on `done`.
                        CapabilitiesToUpdate = y.Capabilities.ToList()
                    })
                    .ToList(),

                // Nullify the response id and set the result id
                // on `done`.
                AttachmentsToUpdate = x.Attachments.ToList(),

                PeriodsToUpdate = x
                    .Result
                    .Periods
                    .Where(y => x.Periods.Any(z => z.Period == y.Period))
                    .ToList(),

                Result = new
                {
                    x.Result.Year,
                    x.Result.Formula,
                    x.Result.InputModeA,
                    x.Result.InputModeB,
                    Department = new
                    {
                        x.Result.Department.Id
                    },
                    Kpi = new
                    {
                        x.Result.Kpi.Id,
                        x.Result.Kpi.NameAr,
                        x.Result.Kpi.NameEn,
                    }
                }
            })
            .SingleOrDefault();

        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Ensure that the response is not done.
        if (data.CurrentTransfer.Assignee == KpiResultDataEntryResponseTransfer.AssigneeDone)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["cannot_transfer_a_response_that_has_already_been_finalized"] }
            );
        }

        // Ensure that A, B, result analysis, improvement procedure, and
        // attachments were added before moving (only in data entry stage).
        if (data.CurrentTransfer.Assignee == KpiResultDataEntryResponseTransfer.AssigneeDataEntry)
        {
            var isAFilled = data.Periods.All(x => x.Item.A != null) ||
                            data.Result.InputModeA == KpiResult.InputModeAuto;

            var isBFilled = data.Periods.All(x => x.Item.B != null) ||
                            data.Result.InputModeB == KpiResult.InputModeAuto ||
                            !data.Result.Formula.Contains('B');

            var isResultAnalysisFilled = data.Periods.All(x => !string.IsNullOrEmpty(x.Item.ResultAnalysis));

            var isImprovementProcedureFilled =
                data.Periods.All(x => !string.IsNullOrEmpty(x.Item.ImprovementProcedure));

            var isAttachmentFilled = data.AttachmentsToUpdate.Count > 0;

            if (!isAFilled)
            {
                throw new GenericException
                    { Messages = new string[] { _localizer["ensure_that_all_a_values_have_been_filled"] } };
            }

            if (!isBFilled)
            {
                throw new GenericException
                    { Messages = new string[] { _localizer["ensure_that_all_b_values_have_been_filled"] } };
            }

            if (!isResultAnalysisFilled)
            {
                throw new GenericException
                    { Messages = new string[] { _localizer["ensure_that_all_result_analyses_have_been_filled"] } };
            }

            if (!isImprovementProcedureFilled)
            {
                throw new GenericException
                {
                    Messages = new string[] { _localizer["ensure_that_all_improvement_procedures_have_been_filled"] }
                };
            }


            var isAttachmentRequired =
                _appSettingService.Get().KpiSetting.IsKpiResultDataEntryResponseAttachmentRequired;
            if (!isAttachmentFilled && isAttachmentRequired)
            {
                throw new GenericException
                    { Messages = new string[] { _localizer["ensure_that_at_least_one_attachment_has_been_added"] } };
            }
        }

        // Check if the current user can perform the transfer.
        switch (data.CurrentTransfer.Assignee)
        {
            // Data entries should have the kpi result permission.
            case KpiResultDataEntryResponseTransfer.AssigneeDataEntry:
                if (!hasKpiResultFlowPermission)
                {
                    return this.GetResponseObject(
                        success: 0,
                        statusCode: 401,
                        messages: new string[] { _localizer["you_are_not_authorized_to_perform_this_action"] }
                    );
                }

                break;

            // Signatories should be in the current transfer.
            case KpiResultDataEntryResponseTransfer.AssigneeLevel1:
            case KpiResultDataEntryResponseTransfer.AssigneeLevel2:
                if (!data.CurrentTransfer.UserIds.Contains(userId))
                {
                    return this.GetResponseObject(
                        success: 0,
                        statusCode: 401,
                        messages: new string[] { _localizer["you_are_not_authorized_to_perform_this_action"] }
                    );
                }

                break;

            // Last step should be performed by a user with kpi permission.
            case KpiResultDataEntryResponseTransfer.AssigneeKpiManager:
                if (!hasKpiPermission)
                {
                    return this.GetResponseObject(
                        success: 0,
                        statusCode: 401,
                        messages: new string[] { _localizer["you_are_not_authorized_to_perform_this_action"] }
                    );
                }

                break;

            default:
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 423,
                    messages: new string[] { _localizer["invalid_current_transfer_assignee"] }
                );
        }

        switch (decision)
        {
            // Ensure that the user has the permission to move the response to kpi manager.
            case "forward_to_kpi_manager" when !hasKpiResultDirectApprovePermission:
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 401,
                    messages: new string[] { _localizer["you_are_not_authorized_to_perform_this_action"] }
                );

            // Ensure that the response can be skipped to the kpi manager only from data entry or level1.
            case "forward_to_kpi_manager"
                when data.CurrentTransfer.Assignee is not KpiResultDataEntryResponseTransfer.AssigneeLevel1:
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 423,
                    messages: new string[] { _localizer["you_can_forward_to_kpi_manager_from_only_level1"] }
                );

            // Ensure that the response cannot be rejected at
            // data entry.
            case "reject" when data.CurrentTransfer.Assignee == KpiResultDataEntryResponseTransfer.AssigneeDataEntry:
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 423,
                    messages: new string[] { _localizer["response_cannot_be_rejected"] }
                );
        }

        // If the next transfer is to level1 or level2, then ensure that
        // the next user(s) to approve is valid.
        if (
            (
                data.ItemWithNext.Next == KpiResultDataEntryResponseTransfer.AssigneeLevel1 ||
                data.ItemWithNext.Next == KpiResultDataEntryResponseTransfer.AssigneeLevel2
            ) && decision == "transfer"
        )
        {
            if (response.Users == null || !response.Users.Any())
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 423,
                    messages: new string[] { _localizer["signatories_have_not_been_specified"] }
                );
            }

            if (!response.Users.Select(x => x.Id).Intersect(data.ItemWithNext.Users.Select(x => x.Id)).Any())
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 423,
                    messages: new string[] { _localizer["some_or_all_of_the_provided_signatories_are_not_authorized"] }
                );
            }
        }

        var transfer = new KpiResultDataEntryResponseTransfer
        {
            ResponseId = response.Id,
            Assignee = decision == "forward_to_kpi_manager"
                ? KpiResultDataEntryResponseTransfer.AssigneeKpiManager
                : data.ItemWithNext.Next,
            Notes = response.Notes,
            Direction = KpiResultDataEntryResponseTransfer.DirectionForward
        };
        _appDataContext.KpiResultDataEntryResponseTransfers.Add(transfer);

        if (decision == "reject")
        {
            transfer.Assignee = KpiResultDataEntryResponseTransfer.AssigneeDataEntry;
            transfer.Direction = KpiResultDataEntryResponseTransfer.DirectionBackward;
        }

        else
        {
            // Set the signatories for the transfer.
            if (
                decision == "transfer" &&
                (
                    data.ItemWithNext.Next == KpiResultDataEntryResponseTransfer.AssigneeLevel1 ||
                    data.ItemWithNext.Next == KpiResultDataEntryResponseTransfer.AssigneeLevel2
                )
            )
            {
                transfer.UserLinks = response.Users.Select(x => new KpiResultDataEntryResponseTransferUserLink
                {
                    UserId = x.Id
                }).ToList();
            }

            // If the next transfer is to done, then write the values in
            // the response to the kpi result.
            if (data.ItemWithNext.Next == KpiResultDataEntryResponseTransfer.AssigneeDone)
            {
                // Update periods.
                data.PeriodsToUpdate.ForEach(resultPeriod =>
                {
                    var responsePeriod = data.Periods.Single(x => x.Item.Period == resultPeriod.Period);
                    resultPeriod.A = responsePeriod.Item.A;
                    resultPeriod.B = responsePeriod.Item.B;
                    resultPeriod.ResultAnalysis = responsePeriod.Item.ResultAnalysis;
                    resultPeriod.ImprovementProcedure = responsePeriod.Item.ImprovementProcedure;
                    resultPeriod.ImprovementProcedureCompletionPercentage =
                        responsePeriod.Item.ImprovementProcedureCompletionPercentage;
                    resultPeriod.ImprovementProcedureExpectedCompletionDate =
                        responsePeriod.Item.ImprovementProcedureExpectedCompletionDate;

                    // Update the capabilities too.
                    responsePeriod.CapabilitiesToUpdate.ForEach(x =>
                    {
                        // x.KpiResultDataEntryResponsePeriodId = null;
                        x.KpiResultPeriodId = resultPeriod.Id;
                    });
                });

                // Update attachments, move current attachments to the result.
                data.AttachmentsToUpdate.ForEach(x =>
                {
                    // x.KpiResultDataEntryResponseId = null;
                    x.KpiResultId = data.Item.ResultId;
                });
            }
        }

        _appDataContext.SaveChanges();


        // Notify users:
        if (decision == "reject")
        {
            var userExpression = UserSimpleDto.Mapper(HelperFunctions.GetLanguageCode());
            var usersToReceiveRejection = _appDataContext
                .Departments
                .AsExpandable()
                .Where(x => x.Id == data.Result.Department.Id)
                .SelectMany(x => x.UserLinks)
                .Select(x => x.User)
                .Where(x => EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.KpiResultFlow, x.Id))
                .Select(x => userExpression.Invoke(x))
                .ToArray();

            await _mediator.Send(new CreateNotificationCommand
            {
                Users = usersToReceiveRejection,
                TitleAr = $"لقد تم رفض طلبك لتحديث نتائج المؤشر \"{data.Result.Kpi.NameAr}\" لسنة {data.Result.Year}",
                TitleEn =
                    $"You request to update kpi \"{data.Result.Kpi.NameEn}\" for year {data.Result.Year} has been rejected.",
                TargetType = Notification.TargetTypeKpiResultDataEntryResponse,
                TargetId = response.Id
            });

            await _mediator.Send(new SendKpiResultDataEntryResponseRejectedEmailCommand
            {
                UserIds = usersToReceiveRejection.Select(x => x.Id).ToArray(),
                KpiNameAr = data.Result.Kpi.NameAr,
                KpiNameEn = data.Result.Kpi.NameEn,
                ResponseId = data.Item.Id,
                Year = data.Result.Year
            });
        }
        else
        {
            UserSimpleDto[] users;

            // Send notification to the set users if the
            // approval is in the middle.
            if (
                decision == "transfer" &&
                (
                    data.ItemWithNext.Next == KpiResultDataEntryResponseTransfer.AssigneeLevel1 ||
                    data.ItemWithNext.Next == KpiResultDataEntryResponseTransfer.AssigneeLevel2
                )
            )
            {
                users = response.Users.ToArray();
            }

            // Send notification to users with kpi permission
            // if approval is in the final stage.
            else
            {
                var userExpression = UserSimpleDto.Mapper(HelperFunctions.GetLanguageCode());
                users = _appDataContext
                    .Users
                    .AsExpandable()
                    .Where(x => EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.Kpi, x.Id))
                    .Select(x => userExpression.Invoke(x))
                    .ToArray();
            }

            // No users were found, return.
            if (!users.Any())
            {
                return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
            }

            await _mediator.Send(new CreateNotificationCommand
            {
                Users = users,
                TitleAr =
                    $"لديك طلب لاعتماد ادخال نتائج مؤشر الأداء \"{data.Result.Kpi.NameAr}\" لسنة {data.Result.Year}.",
                TitleEn =
                    $"You have a new request for updating kpi results {data.Result.Kpi.NameEn} year {data.Result.Year} waiting for approval.",
                TargetType = Notification.TargetTypeKpiResultDataEntryResponse,
                TargetId = response.Id
            });

            await _mediator.Send(new SendKpiResultDataEntryResponseAwaitingApprovalEmailCommand
            {
                UserIds = users.Select(x => x.Id).ToArray(),
                KpiNameAr = data.Result.Kpi.NameAr,
                KpiNameEn = data.Result.Kpi.NameEn,
                ResponseId = data.Item.Id,
                Year = data.Result.Year
            });
        }

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [Authorize(PermissionNameList.KpiResultResponseApprovalTransfer)]
    [HttpPut("/kpi-result-data-entry-response/transfer-approval")]
    public async Task<IActionResult> ChangeApprovers(
        [BindBodySingleJson] KpiResultDataEntryResponseMoveDto response
    )
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(m => m.Value!.Errors.Select(e => e.ErrorMessage)).ToArray()
            );
        }

        var showUsersWithSignatureApprovalInTopLevels =
            _appSettingService.Get().KpiSetting.ShowUsersWithSignatureApprovalInTopLevels;
        var data = (await GetValidResponses())
            .Where(x => x.Id == response.Id)
            .Select(x => new
            {
                CurrentTransfer = x
                    .Transfers
                    .OrderByDescending(y => y.CreationTime)
                    .Select(y => new
                    {
                        Item = y,
                        UserLinks = y.UserLinks.ToList()
                    })
                    .First(),

                Users = GetTransferApproversExpression
                    .Run(_appDataContext.Users, showUsersWithSignatureApprovalInTopLevels).Invoke(x),

                Result = new
                {
                    x.Result.Year,
                    KpiNameAr = x.Result.Kpi.NameAr,
                    KpiNameEn = x.Result.Kpi.NameEn,
                }
            })
            .SingleOrDefault();

        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Ensure that the response is in valid level.
        if (
            data.CurrentTransfer.Item.Assignee is not
            (KpiResultDataEntryResponseTransfer.AssigneeLevel1 or KpiResultDataEntryResponseTransfer.AssigneeLevel2)
        )
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["you_can_only_transfer_approval_at_level1_or_level2"] }
            );
        }

        // Ensure that signatories have been provided.
        if (!response.Users?.Any() ?? true)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["signatories_have_not_been_specified"] }
            );
        }

        // Ensure that the provided signatories are not the same as the current ones.
        if (response.Users.Select(x => x.Id).Intersect(data.CurrentTransfer.UserLinks.Select(y => y.UserId)).Any())
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[]
                    { _localizer["some_or_all_of_the_provided_signatories_are_already_assigned_to_the_transfer"] }
            );
        }

        // Ensure that the provided signatories are valid.
        if (!response.Users.Select(x => x.Id).All(x => data.Users?.Select(y => y.Id).Contains(x) ?? false))
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["some_or_all_of_the_provided_signatories_are_not_authorized"] }
            );
        }

        // remove current signatories
        _appDataContext.KpiResultDataEntryResponseTransferUserLinks.RemoveRange(data.CurrentTransfer.UserLinks);

        data.CurrentTransfer.Item.Notes = response.Notes;
        data.CurrentTransfer.Item.UserLinks = response.Users.Select(x => new KpiResultDataEntryResponseTransferUserLink
        {
            UserId = x.Id
        }).ToList();

        _appDataContext.SaveChanges();

        // notify new assigned signatories
        var users = response.Users.ToArray();
        await _mediator.Send(new CreateNotificationCommand
        {
            Users = users,
            TitleAr =
                $"لديك طلب لاعتماد ادخال نتائج مؤشر الأداء \"{data.Result.KpiNameAr}\" لسنة {data.Result.Year}.",
            TitleEn =
                $"You have a new request for updating kpi results {data.Result.KpiNameEn} for year {data.Result.Year} waiting for approval.",
            TargetType = Notification.TargetTypeKpiResultDataEntryResponse,
            TargetId = response.Id
        });

        await _mediator.Send(new SendKpiResultDataEntryResponseAwaitingApprovalEmailCommand
        {
            UserIds = users.Select(x => x.Id).ToArray(),
            KpiNameAr = data.Result.KpiNameAr,
            KpiNameEn = data.Result.KpiNameEn,
            ResponseId = response.Id,
            Year = data.Result.Year
        });

        // notify previous signatories
        var previousSignatories = data.Users.ToArray();
        await _mediator.Send(new CreateNotificationCommand
        {
            Users = previousSignatories,
            TitleAr =
                $"لقد تم الغاء طلب اعتماد ادخال نتائج مؤشر الأداء \"{data.Result.KpiNameAr}\" لسنة {data.Result.Year}.",
            TitleEn =
                $"The request for approving data entry results for kpi {data.Result.KpiNameEn} for year {data.Result.Year} has been transferred.",
            TargetType = Notification.TargetTypeKpiResultDataEntryResponse,
            TargetId = response.Id
        });

        await _mediator.Send(new SendKpiResultDataEntryResponseApprovalTransferredEmailCommand
        {
            UserIds = previousSignatories.Select(x => x.Id).ToArray(),
            KpiNameAr = data.Result.KpiNameAr,
            KpiNameEn = data.Result.KpiNameEn,
            ResponseId = response.Id,
            Year = data.Result.Year
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }
}
