using Injaz.ModuleKpiResultDataEntryResponse.HostedServices;
using MediatR;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.ModuleKpiResultDataEntryResponse;

public class KpiResultDataEntryResponseModule
{
    public static void Setup(IServiceCollection services, string connectionString)
    {
        services.AddMediatR(typeof(KpiResultDataEntryResponseModule));

        // Hosted services.
        //services.AddHostedService<KpiResultDataEntryResponseNotificationService>();
    }
}
