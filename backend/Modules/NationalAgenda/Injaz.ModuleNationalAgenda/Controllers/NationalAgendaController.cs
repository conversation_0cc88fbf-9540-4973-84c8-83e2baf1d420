using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.NationalAgenda;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleNationalAgenda.Controllers;

[Authorize(Policy = PermissionNameList.Partner)]
public class NationalAgendaController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public NationalAgendaController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/national-agenda")]
    public IActionResult List(
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.ToLower().Trim() ?? "";

        var items = _appDataContext
            .NationalAgendas
            .Where(x => x.NameAr.Contains(keyword) || x.NameEn.ToLower().Contains(keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: new
        {
            Items = items
                .OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .Select(NationalAgendaGetDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.CapabilityTypes.Count(),
            FilteredCount = items.Count()
        });
    }

    [HttpGet]
    [Route("/national-agenda/{id:guid}")]
    public IActionResult Get(
        Guid id,
        bool forEdit = false
    )
    {
        var item = forEdit
            ? _appDataContext.NationalAgendas
                .Select(NationalAgendaEditDto.Mapper())
                .FirstOrDefault(x => x.Id.Equals(id)) as object
            : _appDataContext.NationalAgendas
                .Select(NationalAgendaGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/national-agenda")]
    public IActionResult Create(
        [BindBodySingleJson] NationalAgendaCreateDto agenda
    )
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Clean
        agenda.NameAr = agenda.NameAr.Trim();
        agenda.NameEn = agenda.NameEn?.Trim() ?? agenda.NameAr;

        // Create and add the new item.
        var item = new NationalAgenda() { NameAr = agenda.NameAr.Trim(), NameEn = agenda.NameEn.Trim() };
        _appDataContext.NationalAgendas.Add(item);
        _appDataContext.SaveChanges();


        // Return the newly created item using dtos.
        return this.GetResponseObject(
            extra: _appDataContext
                .NationalAgendas
                .Select(NationalAgendaGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    [HttpPut]
    [Route("/national-agenda")]
    public IActionResult Update(
        [BindBodySingleJson] NationalAgendaEditDto agenda
    )
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Clean
        agenda.NameAr = agenda.NameAr.Trim();
        agenda.NameEn = agenda.NameEn?.Trim() ?? agenda.NameAr;

        // Get item.
        var item = _appDataContext.NationalAgendas.Find(agenda.Id);

        // Ensure the item exists.
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Update the item.
        item.NameAr = agenda.NameAr.Trim();
        item.NameEn = agenda.NameEn.Trim();
        _appDataContext.SaveChanges();

        // Return the newly created item using dtos.
        return this.GetResponseObject(
            extra: _appDataContext
                .NationalAgendas
                .Select(NationalAgendaGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    [HttpDelete]
    [Route("/national-agenda/{id:guid}")]
    public IActionResult Delete(
        Guid id
    )
    {
        // Get item.
        var item = _appDataContext.NationalAgendas
            .AsExpandable()
            .Where(x => x.Id.Equals(id))
            .Select(x => new
            {
                NationalAgenda = x,
                IsUsed = x.PartnershipGoalLinks.Any()
            }).FirstOrDefault();
        // Ensure the item exists.
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Ensure that the agenda does not have any partners under it.
        if (item.IsUsed)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["cannot_delete_item_there_are_partners_under_it"] }
            );
        }


        // Remove the item.
        _appDataContext.NationalAgendas.Remove(item.NationalAgenda);

        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            messages: new string[] { _localizer["item_removed_successfully"] }
        );
    }
}
