using System.Collections.Concurrent;
using System.Linq.Expressions;
using Injaz.Core;
using Injaz.Core.Dtos.Notification;
using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using MediatR;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Hubs.Default;
using Injaz.Core.Services;
using Injaz.ModuleNotification.Core.Commands;
using Microsoft.AspNetCore.SignalR;

namespace Injaz.ModuleNotification.Handlers;

public class CreateHandler :
    IRequestHandler<CreateNotificationCommand, NotificationSimpleDto>
{
    private readonly DbContext _db;
    private readonly IHubContext<DefaultHub, IDefaultClient> _defaultHubContext;
    private readonly ValidationService _validationService;

    public CreateHandler(
        DbContext db,
        IHubContext<DefaultHub, IDefaultClient> defaultHubContext,
        ValidationService validationService
    )
    {
        _db = db;
        _defaultHubContext = defaultHubContext;
        _validationService = validationService;
    }

    public async Task<NotificationSimpleDto> Handle(
        CreateNotificationCommand command,
        CancellationToken cancellationToken
    )
    {
        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }

        // Figure out the users to send the notifications to.
        // If no departments, teams, or users were passed, we're
        // gonna assume that the notification is to be sent to all
        // users in the system. Otherwise, we're gonna only
        // send the notification to the specified entities.
        Guid[] userIds;
        if (
            (command.Departments?.Count() ?? 0) == 0 &&
            (command.Teams?.Count() ?? 0) == 0 &&
            (command.Users?.Count() ?? 0) == 0
        )
        {
            userIds = _db.Users.Select(x => x.Id).ToArray();
        }
        else
        {
            var selectedDepartmentIds = command.Departments?.Select(x => x.Id) ?? new Guid[] { };
            var selectedTeamIds = command.Teams?.Select(x => x.Id) ?? new Guid[] { };
            var selectedUserIds = command.Users?.Select(x => x.Id) ?? new Guid[] { };

            var departmentCodes = _db
                .Departments
                .Where(x => selectedDepartmentIds.Contains(x.Id))
                .Select(x => x.HierarchyCode)
                .ToList();

            Expression<Func<User, bool>> expression = x => false;
            departmentCodes.ForEach(
                x => expression =
                    expression.Or(y => y.DepartmentLinks.Any(z => z.Department.HierarchyCode.StartsWith(x)))
            );

            userIds = _db
                .Users
                .AsExpandable()
                .Where(x =>
                    expression.Invoke(x) ||
                    x.TeamLinks.Any(y => selectedTeamIds.Contains(y.TeamId)) ||
                    selectedUserIds.Contains(x.Id)
                )
                .Select(x => x.Id)
                .ToArray();
        }


        var item = new Notification();

        item.TitleAr = command.TitleAr.Trim();
        item.TitleEn = command.TitleEn?.Trim() ?? item.TitleAr;
        item.DescriptionAr = command.DescriptionAr?.Trim();
        item.DescriptionEn = command.DescriptionEn?.Trim() ?? item.DescriptionAr;
        item.TargetType = command.TargetType;
        item.TargetId = command.TargetId;
        item.TargetMetadata = command.TargetMetadata;
        item.UserLinks = userIds.Select(x => new NotificationUserLink
            {
                UserId = x,
                ReadTime = null
            })
            .ToArray();


        _db.Notifications.Add(item);

        _db.ForceSaveChanges();

        await NotifyUsers(userIds);

        return _db
            .Notifications
            .AsExpandable()
            .Select(NotificationSimpleDto.Mapper(HelperFunctions.GetLanguageCode()))
            .First(p => p.Id.Equals(item.Id));
    }

    private async Task NotifyUsers(Guid[] userIds)
    {
        await _defaultHubContext
            .Clients
            .Users(userIds.Select(x => x.ToString()))
            .Message(new
            {
                Type = MessageTypeList.NotificationCountUpdate
            });
    }

}
