using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.OperationSpecification;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleOperation.Controllers;

[Authorize(Policy = PermissionNameList.OperationSpecification)]
public class OperationSpecificationController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public OperationSpecificationController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/operation-specification")]
    public IActionResult List(
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.ToLower().Trim() ?? "";

        var items = _appDataContext
            .OperationSpecifications
            .Where(x => x.NameAr.Contains(keyword) || x.NameEn.ToLower().Contains(keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: new
        {
            Items = items
                .OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .Select(OperationSpecificationGetDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.OperationSpecifications.Count(),
            FilteredCount = items.Count()
        });
    }

    [HttpGet]
    [Route("/operation-specification/{id}")]
    public IActionResult Get(
        Guid id,
        bool forEdit = false
    )
    {
        var item = forEdit
            ? _appDataContext.OperationSpecifications
                .Select(OperationSpecificationEditDto.Mapper())
                .FirstOrDefault(x => x.Id.Equals(id)) as object
            : _appDataContext.OperationSpecifications
                .Select(OperationSpecificationGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] {_localizer["item_not_found"]}
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/operation-specification")]
    public IActionResult Create(
        [BindBodySingleJson] OperationSpecificationCreateDto operationSpecification
    )
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Create and add the new item.
        var item = new OperationSpecification
            {NameAr = operationSpecification.NameAr.Trim(), NameEn = operationSpecification.NameEn.Trim()};
        _appDataContext.OperationSpecifications.Add(item);
        _appDataContext.SaveChanges();


        // Return the newly created item using dtos.
        return this.GetResponseObject(
            extra: _appDataContext
                .OperationSpecifications
                .Select(OperationSpecificationGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    [HttpPut]
    [Route("/operation-specification")]
    public IActionResult Update(
        [BindBodySingleJson] OperationSpecificationEditDto operationSpecification
    )
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Get item.
        var item = _appDataContext.OperationSpecifications.Find(operationSpecification.Id);

        // Ensure the item exists.
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] {_localizer["item_not_found"]}
            );
        }

        // Update the item.
        item.NameAr = operationSpecification.NameAr.Trim();
        item.NameEn = operationSpecification.NameEn.Trim();
        _appDataContext.SaveChanges();

        // Return the newly created item using dtos.
        return this.GetResponseObject(
            extra: _appDataContext
                .OperationSpecifications
                .Select(OperationSpecificationGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    [HttpDelete]
    [Route("/operation-specification/{id}")]
    public IActionResult Delete(
        Guid id
    )
    {
        // Get item.
        var item = _appDataContext.OperationSpecifications.Find(id);

        // Ensure the item exists.
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] {_localizer["item_not_found"]}
            );
        }

        // Ensure that the specification does not have any operations under it.
        if (_appDataContext.OperationSpecificationLinks.Any(x => x.SpecificationId.Equals(id)))
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[]
                    {_localizer["cannot_delete_specification_since_some_operations_are_linked_to_it"]}
            );
        }


        // Remove the item.
        _appDataContext.OperationSpecifications.Remove(item);

        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            messages: new string[] {_localizer["item_removed_successfully"]}
        );
    }
}
