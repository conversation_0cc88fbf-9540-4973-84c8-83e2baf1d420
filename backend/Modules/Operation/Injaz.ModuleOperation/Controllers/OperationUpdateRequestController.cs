using System.Text.Json;
using System.Text.Json.Serialization;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.OperationUpdateRequest;
using Injaz.Core.Dtos.OperationUpdateRequestItemType;
using Injaz.Core.Exceptions;
using Injaz.Core.Flow.Services;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleOperation.FlowServices;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleOperation.Controllers;

[Authorize]
public class OperationUpdateRequestController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly FlowService<OperationUpdateRequest> _flowService;
    private readonly UserManager<User> _userManager;

    public OperationUpdateRequestController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer,
        FlowService<OperationUpdateRequest> flowService,
        UserManager<User> userManager)
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
        _flowService = flowService;
        _userManager = userManager;
    }

    [HttpGet]
    [Route("/operation/update-request")]
    public IActionResult List(
        string? keyword = "",
        int pageNumber = 0,
        int pageSize = 20)
    {
        keyword = keyword?.ToLower().Trim() ?? "";

        var items = _appDataContext
            .OperationUpdateRequests
            .AsExpandable()
            .Where(x => x.Operation.NameAr.Contains(keyword) || x.Operation.NameAr.ToLower().Contains(keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: new
        {
            Items = items
                .OrderByDescending(x => x.CreationTime)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .Select(OperationUpdateRequestListDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.OperationUpdateRequests.Count(),
            FilteredCount = items.Count()
        });
    }


    [HttpGet]
    [Route("/operation/update-request/{id:guid}")]
    public IActionResult Get(Guid id, bool forEdit)
    {
        var lang = HelperFunctions.GetLanguageCode();
        var userId = Guid.Parse(_userManager.GetUserId(User));

        var item = forEdit
            ? _appDataContext.OperationUpdateRequests
                .AsExpandable()
                .Select(OperationUpdateRequestEditDto.Mapper(lang))
                .FirstOrDefault(x => x.Id.Equals(id)) as object
            : _appDataContext
                .OperationUpdateRequests
                .AsExpandable()
                .Select(OperationUpdateRequestGetDto.Mapper(
                    HelperFunctions.GetLanguageCode(),
                    userId,
                    _flowService.GenerateActionAvailability()))
                .FirstOrDefault(x => x.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }


    [HttpPost]
    [Route("/operation/update-request")]
    public IActionResult Create(
        Guid operationId,
        [BindBodySingleJson] OperationUpdateRequestCreateDto request,
        [BindBodySingleJson] OperationEditDto operation)
    {
        return CreateOrUpdate(request, operation, operationId);
    }


    [HttpPut]
    [Route("/operation/update-request")]
    public IActionResult Update(
        [BindBodySingleJson] OperationUpdateRequestEditDto request,
        [BindBodySingleJson] OperationCreateDto operation)
    {
        return CreateOrUpdate(request, operation, null);
    }

    [HttpGet]
    [Route("/operation/update-request/type")]
    public IActionResult Types()
    {
        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: _appDataContext.OperationUpdateRequestItemTypes
            .Select(OperationUpdateRequestItemTypeSimpleDto.Mapper(lang))
            .OrderBy(x => x.Name)
            .ToList()
        );
    }


    private IActionResult CreateOrUpdate(
        OperationUpdateRequestCreateDto request,
        OperationCreateDto operationDto,
        Guid? operationId)
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState
                    .SelectMany(x => x.Value?.Errors.Select(y => y.ErrorMessage) ?? new string[] { })
                    .ToArray()
            );
        }

        OperationUpdateRequest? item;
        Guid? updateRequestId = null;

        if (request is OperationUpdateRequestEditDto editDto)
        {
            item = _appDataContext.OperationUpdateRequests.SingleOrDefault(x => x.Id == editDto.Id);
            updateRequestId = editDto.Id;

            // Ensure the item exists.
            if (item == null)
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] }
                );
            }

            operationId = item.OperationId;

            _appDataContext.RemoveRange(
                _appDataContext.OperationUpdateRequestItems.Where(x => x.UpdateRequestId == item.Id));
        }
        else
        {
            item = new OperationUpdateRequest
            {
                OperationId = operationId!.Value,
            };
            _appDataContext.OperationUpdateRequests.Add(item);
        }

        var hasPendingUpdateRequestExpression = HelperExpression.HasPendingUpdateRequest();
        var rejectedRequestExpression = HelperExpression.RejectedUpdateRequest();

        var data = _appDataContext.Operations
            .AsExpandable()
            .Include(x => x.OwnerDepartment)
            .Include(x => x.MainOperationOwner)
            .Include(x => x.Parent)
            .Include(x => x.GoalLinks)
            .ThenInclude(x => x.StrategicGoal)
            .Include(x => x.SuccessFactorOperationLinks)
            .ThenInclude(x => x.SuccessFactor)
            .Include(x => x.SpecificationLinks)
            .ThenInclude(x => x.Specification)
            .Include(x => x.PolicyLinks)
            .ThenInclude(x => x.Policy)
            .Include(x => x.PartnerLinks)
            .ThenInclude(x => x.Partner)
            .Where(x => x.Id == operationId)
            .Select(x => new
            {
                Item = x,
                HasPendingUpdateRequest = hasPendingUpdateRequestExpression.Invoke(x),
                RejectedUpdateRequest = rejectedRequestExpression.Invoke(x)
            })
            .SingleOrDefault();


        if (data == null)
        {
            throw new ItemNotFoundException();
        }

        if (data.HasPendingUpdateRequest &&
            (updateRequestId == null || updateRequestId != data.RejectedUpdateRequest?.Id))
        {
            throw new GenericException { Messages = new string[] { _localizer["operation_has_open_update_request"] } };
        }

        var operationItem = data.Item;

        var options = new JsonSerializerOptions()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = false,
            ReferenceHandler = ReferenceHandler.IgnoreCycles,
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder
                .UnsafeRelaxedJsonEscaping // This will prevent the escape sequences.
        };


        var oldData = JsonSerializer.SerializeToNode(operationItem, options);
        var newData = JsonSerializer.SerializeToNode(operationDto, options);

        // Create and add the new item.
        item.FlowState = OperationUpdateRequestFlowService.States.Submitted;
        item.OldData = oldData;
        item.NewData = newData;
        item.Items = request.Items.Select(x => new OperationUpdateRequestItem
        {
            TypeId = x.Type.Id,
            Field = x.Field,
            OldValue = x.OldValue,
            NewValue = x.NewValue,
            Notes = x.Notes
        }).ToList();

        _appDataContext.SaveChanges();

        // Return the newly created item using dtos.
        var userId = Guid.Parse(_userManager.GetUserId(User));

        return this.GetResponseObject(
            extra: _appDataContext
                .OperationUpdateRequests
                .AsExpandable()
                .Select(OperationUpdateRequestGetDto.Mapper(
                    HelperFunctions.GetLanguageCode(),
                    userId,
                    _flowService.GenerateActionAvailability()))
                .First(x => x.Id.Equals(item.Id))
        );
    }
}
