using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePartner.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModulePartner.Handlers;

public class DeletePartnerHandler : IRequestHandler<DeletePartnerCommand>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeletePartnerHandler(DbContext db, IStringLocalizer<SharedResource> localizer)
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(DeletePartnerCommand command, CancellationToken cancellationToken)
    {
        var data = _db
            .Partners
            .Where(x => x.Id == command.Id)
            .Select(x => new
                {
                    Item = x,
                    IsLinkedToOtherResources =
                        x.Benchmarks.Any() ||
                        x.PartnershipContracts.Any() ||
                        x.ServiceLinks.Any()
                }
            )
            .SingleOrDefault();

        if (data == null) throw new ItemNotFoundException();

        // Ensure nothing is linked to the team.
        if (data.IsLinkedToOtherResources)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["cannot_delete_item_item_connected_to_other_resources"] }
            };
        }

        _db.Partners.Remove(data.Item);

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
