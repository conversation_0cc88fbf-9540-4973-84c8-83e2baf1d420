using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Partner;
using Injaz.ModulePartner.Core.Queries;
using MediatR;

namespace Injaz.ModulePartner.Handlers;

public class GetPartnerListForMiscHandler : IRequestHandler<GetPartnerListForMiscQuery, IEnumerable<PartnerSimpleDto>>
{
    private readonly DbContext _db;

    public GetPartnerListForMiscHandler(DbContext db) => _db = db;

    public Task<IEnumerable<PartnerSimpleDto>> Handle(
        GetPartnerListForMiscQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .Partners
            .AsExpandable()
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword));

        var lang = HelperFunctions.GetLanguageCode();

        var miscItems = items
            .OrderBy(x => x.CreationTime)
            .Select(PartnerSimpleDto.Mapper(lang));

        if (query.PageSize > -1) miscItems = miscItems.Take(query.PageSize);

        return Task.FromResult(miscItems.AsEnumerable());
    }
}
