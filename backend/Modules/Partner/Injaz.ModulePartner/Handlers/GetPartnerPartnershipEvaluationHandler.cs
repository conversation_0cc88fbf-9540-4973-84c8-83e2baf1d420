using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.Partner;
using Injaz.ModulePartner.Core.Queries;
using MediatR;

namespace Injaz.ModulePartner.Handlers;

public class GetPartnerPartnershipEvaluationHandler :
    IRequestHandler<GetPartnerPartnershipEvaluationQuery, TableResultDto<PartnerPartnershipEvaluationDto>>
{
    private readonly DbContext _db;

    public GetPartnerPartnershipEvaluationHandler(DbContext db) => _db = db;

    public Task<TableResultDto<PartnerPartnershipEvaluationDto>> Handle(
        GetPartnerPartnershipEvaluationQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var lang = HelperFunctions.GetLanguageCode();

        var items = _db.PartnershipContracts
            .AsExpandable()
            .Where(x => x.PartnerId == query.PartnerId && x.PartnershipPartnerEvaluations.Any());

        var filteredItems = items
            .Where(x => x.TitleAr.Contains(query.Keyword) || x.TitleEn.ToLower().Contains(query.Keyword));

        return Task.FromResult(
            new TableResultDto<PartnerPartnershipEvaluationDto>
            {
                Items = filteredItems
                    .Select(PartnerPartnershipEvaluationDto.Mapper(lang))
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = items.Count(),
                FilteredCount = filteredItems.Count()
            }
        );
    }
}
