using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.PartnerModel;
using Injaz.ModulePartnership.Core.Dtos.PartnershipContract;
using Injaz.ModulePartnership.Core.Dtos.PartnershipField;
using MediatR;
using PartnershipTypeModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipType;
using PartnershipFrameworkModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipFramework;
using PartnerStandardModel = Injaz.Core.Models.DomainClasses.App.PartnerModel.PartnerStandard;
using PartnershipContractModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract;

namespace Injaz.ModulePartnership.Core.Commands.PartnershipContract;

public class CreatePartnershipContractCommand : IRequest<PartnershipContractGetDto>
{
    [Display(Name = "title_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string TitleAr { get; set; }

    public string TitleEn { get; set; }

    public Partner? Partner { get; set; }

    public string? OtherPartner { get; set; }

    [Display(Name = "organization_type")]
    [MinLength(1, ErrorMessage = "0_should_be_at_least_1")]
    public IEnumerable<PartnerStandardModel> PartnerStandards { get; set; }

    public IEnumerable<PartnershipFieldSimpleDto>? Fields { get; set; }

    [Display(Name = "department")]
    [Required(ErrorMessage = "0_is_required")]
    public Department Department { get; set; }

    [Display(Name = "start_date")]
    [Required(ErrorMessage = "0_is_required")]
    public DateTime StartDate { get; set; }


    [Display(Name = "time_frame")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression(
            "^(?:" +
            PartnershipContractModel.TimeFrameAnnual + "|" +
            PartnershipContractModel.TimeFrameSemiAnnual + "|" +
            PartnershipContractModel.TimeFrameQuarter + "|" +
            PartnershipContractModel.TimeFrameMonth + ")$",
            ErrorMessage = "0_is_invalid"
        )
    ]
    public string TimeFrame { get; set; }

    public bool IsRenewable { get; set; }


    [Display(Name = "sector_type")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression(
            "^(?:" +
            PartnerStandardModel.SectorTypeGovernment + "|" +
            PartnerStandardModel.SectorTypePrivate + ")$",
            ErrorMessage = "0_is_invalid"
        )
    ]
    public string SectorType { get; set; }

    public PartnershipTypeModel? PartnershipType { get; set; }

    public IEnumerable<PartnershipFrameworkModel>? PartnershipFrameworks { get; set; }

    public string? PartnershipScope { get; set; }

    public string? PartnerCapabilities { get; set; }

    public string? Resources { get; set; }

    public string? Purpose { get; set; }

    public string? RolesAndResponsibilities { get; set; }

    public string? DealingWithConflicts { get; set; }

    public string? Arrangements { get; set; }

    public string? CurrentSituation { get; set; }

    public string? Membership { get; set; }

    public LibraryFileSimpleDto AgreementFile { get; set; }

    public Guid UserId { get; set; }

    public IEnumerable<PartnershipContractTermDto>? Terms { get; set; }

    public bool HasPartnershipPermission { get; set; }
}
