using System.ComponentModel.DataAnnotations;
using Injaz.ModulePartnership.Core.Dtos.PartnershipPartnerEvaluation;
using MediatR;

namespace Injaz.ModulePartnership.Core.Commands.PartnershipPartnerEvaluation;

public class UpdatePartnershipPartnerEvaluationCommand : IRequest
{
    public Guid PartnershipContractId { get; set; }

    [Display(Name = "rating")] public IEnumerable<PartnershipPartnerEvaluationListDto> Rating { get; set; }
}
