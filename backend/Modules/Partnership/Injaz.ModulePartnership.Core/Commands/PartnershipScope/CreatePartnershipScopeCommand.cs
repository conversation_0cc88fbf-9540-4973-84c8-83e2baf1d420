using System.ComponentModel.DataAnnotations;
using Injaz.ModulePartnership.Core.Dtos.PartnershipField;
using Injaz.ModulePartnership.Core.Dtos.PartnershipScope;
using MediatR;

namespace Injaz.ModulePartnership.Core.Commands.PartnershipScope;

public class CreatePartnershipScopeCommand : IRequest<PartnershipScopeGetDto>
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "partnership_field")]
    [Required(ErrorMessage = "0_is_required")]
    public PartnershipFieldSimpleDto PartnershipField { get; set; }
}
