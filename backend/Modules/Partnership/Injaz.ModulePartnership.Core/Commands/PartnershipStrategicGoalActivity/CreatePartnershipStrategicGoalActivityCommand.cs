using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.ValidationAttributes;
using MediatR;

namespace Injaz.ModulePartnership.Core.Commands.PartnershipStrategicGoalActivity;

public class CreatePartnershipStrategicGoalActivityCommand : IRequest
{
    public Guid PartnershipGoalId { get; set; }

    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    public string? NameEn { get; set; }

    public string Benefits { get; set; }

    public string? Responsibilities { get; set; }

    public DateTime? EndDate { get; set; }

    public string? Risks { get; set; }

    public string? Targets { get; set; }

    public string? TargetKnowledge { get; set; }

    public IEnumerable<KpiSimpleDto>? Kpis { get; set; }

    [Display(Name = "file")]
    [MaxFileSize(150 * 1024 * 1024, ErrorMessage = "0_should_not_be_more_than_150mb")]
    public byte[]? File { get; set; }
}
