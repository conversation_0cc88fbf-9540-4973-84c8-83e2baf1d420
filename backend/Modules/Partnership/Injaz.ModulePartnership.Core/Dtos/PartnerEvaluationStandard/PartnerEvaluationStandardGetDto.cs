using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.ModulePartnership.Core.Dtos.PartnerEvaluationStandard;

public class PartnerEvaluationStandardGetDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PartnerModel.PartnerEvaluationStandard, PartnerEvaluationStandardGetDto>> Mapper(string lang)
    {
        return item => new PartnerEvaluationStandardGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Value = item.Value
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public double Value { get; set; }
}
