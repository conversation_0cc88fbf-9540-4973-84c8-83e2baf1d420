using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace Injaz.ModulePartnership.Core.Dtos.PartnerStandard;

public class PartnerStandardEditDto
{
    public static Expression<
        Func<Injaz.Core.Models.DomainClasses.App.PartnerModel.PartnerStandard, PartnerStandardEditDto>> Mapper()
    {
        return item => new PartnerStandardEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            SectorType = item.SectorType,
        };
    }

    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }

    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "sector_type")]
    [Required(ErrorMessage = "0_is_required")]
    public string SectorType { get; set; }
}
