using System.Linq.Expressions;
using LinqKit;
using Injaz.ModulePartnership.Core.Dtos.PartnershipActivityCommunicationTool;
using Injaz.ModulePartnership.Core.Dtos.PartnershipActivityPeriod;
using PartnershipActivityModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivity;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipActivity;

public class PartnershipActivityGetDto
{
    public static Expression<Func<PartnershipActivityModel, PartnershipActivityGetDto>> Mapper(string lang)
    {
        var partnershipActivityCommunicationToolExpression = PartnershipActivityCommunicationToolGetDto.Mapper(lang);
        var partnershipActivityPeriodExpression = PartnershipActivityPeriodDto.Mapper();

        return item => new PartnershipActivityGetDto
        {
            Id = item.Id,
            Purpose = item.Purpose,
            CommunicationTool = partnershipActivityCommunicationToolExpression.Invoke(item.CommunicationTool),
            Periods = item.Periods.Select(x => partnershipActivityPeriodExpression.Invoke(x)).ToList(),
            Year = item.Year
        };
    }

    public Guid Id { get; set; }

    public string Purpose { get; set; }

    public PartnershipActivityCommunicationToolGetDto CommunicationTool { get; set; }

    public int Year { get; set; }

    public IEnumerable<PartnershipActivityPeriodDto> Periods { get; set; }
}
