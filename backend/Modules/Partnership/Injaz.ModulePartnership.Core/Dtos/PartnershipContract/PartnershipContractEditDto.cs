using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Dtos.Partner;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using Injaz.ModulePartnership.Core.Dtos.PartnershipField;
using Injaz.ModulePartnership.Core.Dtos.PartnershipFramework;
using Injaz.ModulePartnership.Core.Dtos.PartnershipScope;
using Injaz.ModulePartnership.Core.Dtos.PartnerStandard;
using PartnershipContractModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract;
using PartnershipTypeGetDto = Injaz.ModulePartnership.Core.Dtos.PartnershipType.PartnershipTypeGetDto;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipContract;

public class PartnershipContractEditDto
{
    public static Expression<Func<PartnershipContractModel, PartnershipContractEditDto>> Mapper(string lang)
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var partnerExpression = PartnerSimpleDto.Mapper(lang);
        var partnerStandardExpression = PartnerStandardGetDto.Mapper(lang);
        var partnershipFrameworkExpression = PartnershipFrameworkGetDto.Mapper(lang);
        var partnershipScopeExpression = PartnershipScopeGetDto.Mapper(lang);
        var partnershipTypeExpression = PartnershipTypeGetDto.Mapper(lang);
        var fieldExpression = PartnershipFieldSimpleDto.Mapper(lang);
        var libraryFileExpression = LibraryFileSimpleDto.Mapper(lang);

        return item => new PartnershipContractEditDto
        {
            Id = item.Id,
            TitleAr = item.TitleAr,
            TitleEn = item.TitleEn,
            Department = departmentExpression.Invoke(item.Department),
            Partner = item.Partner != null ? partnerExpression.Invoke(item.Partner) : null,
            OtherPartner = item.Partner == null ? item.OtherPartner : null,
            StartDate = item.StartDate,
            TimeFrame = item.TimeFrame,
            IsRenewable = item.IsRenewable,
            FlowState = item.FlowState,
            PartnershipType = item.PartnershipType != null
                ? partnershipTypeExpression.Invoke(item.PartnershipType)
                : null,
            Fields =
                item
                    .PartnershipPartnershipFieldLinks
                    .Select(x => fieldExpression.Invoke(x.PartnershipField))
                    .ToList(),

            PartnerStandards =
                item
                    .PartnershipPartnerStandardLinks
                    .Select(x => partnerStandardExpression.Invoke(x.PartnerStandard))
                    .ToList(),
            PartnershipFrameworks =
                item
                    .PartnershipFrameworkLinks
                    .Select(x => partnershipFrameworkExpression.Invoke(x.PartnershipFramework))
                    .ToList(),
            PartnershipScope = !string.IsNullOrEmpty(item.PartnershipScope)
                ? item.PartnershipScope
                : string.Join(" - ", item.PartnershipScopeLinks
                    .Select(x => partnershipScopeExpression.Invoke(x.PartnershipScope).Name)
                    .ToList()),
            PartnerCapabilities = item.PartnerCapabilities,
            Resources = item.Resources,
            Purpose = item.Purpose,
            RolesAndResponsibilities = item.RolesAndResponsibilities,
            DealingWithConflicts = item.DealingWithConflicts,
            Arrangements = item.Arrangements,
            CurrentSituation = item.CurrentSituation,
            Membership = item.Membership,
            SectorType = item.SectorType,
            AgreementFile = item.AgreementFile != null
                ? libraryFileExpression.Invoke(item.AgreementFile)
                : null,
            Terms = item.Terms
        };
    }

    public Guid Id { get; set; }
    public string TitleAr { get; set; }
    public string TitleEn { get; set; }
    public DepartmentSimpleDto Department { get; set; }
    public PartnerSimpleDto? Partner { get; set; }
    public string? OtherPartner { get; set; }
    public string FlowState { get; set; }
    public DateTime StartDate { get; set; }
    public string TimeFrame { get; set; }
    public bool IsRenewable { get; set; }
    public PartnershipTypeGetDto? PartnershipType { get; set; }
    public IEnumerable<PartnershipFieldSimpleDto> Fields { get; set; }
    public IEnumerable<PartnerStandardGetDto> PartnerStandards { get; set; }
    public IEnumerable<PartnershipFrameworkGetDto> PartnershipFrameworks { get; set; }
    public string PartnershipScope { get; set; }
    public string PartnerCapabilities { get; set; }
    public string Resources { get; set; }
    public string Purpose { get; set; }
    public string RolesAndResponsibilities { get; set; }
    public string DealingWithConflicts { get; set; }
    public string Arrangements { get; set; }
    public string CurrentSituation { get; set; }
    public string Membership { get; set; }
    public string SectorType { get; set; }
    public LibraryFileSimpleDto? AgreementFile { get; set; }
    public IEnumerable<PartnershipContractTerm> Terms { get; set; }
}
