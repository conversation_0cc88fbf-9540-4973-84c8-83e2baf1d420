using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipField;

public class PartnershipFieldGetDto
{
    public static Expression<
            Func<Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipField, PartnershipFieldGetDto>>
        Mapper(string lang)
    {
        return item => new PartnershipFieldGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
