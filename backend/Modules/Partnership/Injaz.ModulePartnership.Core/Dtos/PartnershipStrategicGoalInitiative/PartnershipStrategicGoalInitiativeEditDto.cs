using System.Linq.Expressions;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalInitiative;

public class PartnershipStrategicGoalInitiativeEditDto
{
    public static Expression<Func<PartnershipGoalInitiative, PartnershipStrategicGoalInitiativeEditDto>> Mapper(string lang)
    {
        return item => new PartnershipStrategicGoalInitiativeEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            Activities = item.Activities,
            Benefits = item.Benefits,
            Responsibilities = item.Responsibilities,
        };
    }

    public Guid Id { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }

    public string Activities { get; set; }

    public string Benefits { get; set; }

    public string Responsibilities { get; set; }
}
