using System.Linq.Expressions;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalInitiative;

public class PartnershipStrategicGoalInitiativeGetDto
{
    public static Expression<Func<PartnershipGoalInitiative, PartnershipStrategicGoalInitiativeGetDto>> Mapper(string lang)
    {
        return item => new PartnershipStrategicGoalInitiativeGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Activities = item.Activities,
            Benefits = item.Benefits,
            Responsibilities = item.Responsibilities,
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public string Activities { get; set; }

    public string Benefits { get; set; }

    public string Responsibilities { get; set; }
}
