using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.NationalAgenda;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalNationalAgenda;

public class PartnershipStrategicGoalNationalAgendaListDto
{
    public static Expression<Func<PartnershipGoalNationalAgendaLink, PartnershipStrategicGoalNationalAgendaListDto>>
        Mapper(string lang)
    {
        var serviceExpression = NationalAgendaSimpleDto.Mapper(lang);

        return item => new PartnershipStrategicGoalNationalAgendaListDto
        {
            Id = item.NationalAgendaId,
            NationalAgenda = serviceExpression.Invoke(item.NationalAgenda),
        };
    }

    public Guid Id { get; set; }

    public NationalAgendaSimpleDto NationalAgenda { get; set; }
}
