using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalOperation;

public class PartnershipStrategicGoalOperationListDto
{
    public static Expression<Func<PartnershipGoalOperationLink, PartnershipStrategicGoalOperationListDto>>
        Mapper(string lang)
    {
        var operationExpression = OperationSimpleDto.Mapper(lang);

        return item => new PartnershipStrategicGoalOperationListDto
        {
            Id = item.OperationId,
            Operation = operationExpression.Invoke(item.Operation),
        };
    }

    public Guid Id { get; set; }

    public OperationSimpleDto Operation { get; set; }
}
