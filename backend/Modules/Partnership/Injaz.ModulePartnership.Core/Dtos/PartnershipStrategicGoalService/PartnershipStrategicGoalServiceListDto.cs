using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Service;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalService;

public class PartnershipStrategicGoalServiceListDto
{
    public static Expression<Func<PartnershipGoalServiceLink, PartnershipStrategicGoalServiceListDto>>
        Mapper(string lang)
    {
        var serviceExpression = ServiceWithSubServiceDto.Mapper(lang);

        return item => new PartnershipStrategicGoalServiceListDto
        {
            Id = item.ServiceId,
            Service = serviceExpression.Invoke(item.Service),
        };
    }

    public Guid Id { get; set; }

    public ServiceWithSubServiceDto Service { get; set; }
}
