using System.Linq.Expressions;
using LinqKit;
using Injaz.ModulePartnership.Core.Dtos.PartnershipContract;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipTerminationRequest;
using PartnershipTerminationRequestModel =
    Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipTerminationRequest;

public class PartnershipTerminationRequestExportDto
{
    public static Expression<Func<PartnershipTerminationRequestModel, PartnershipTerminationRequestExportDto>>
        Mapper(
            string lang
        )
    {
        var partnershipContractExpression = PartnershipContractSimpleDto.Mapper(lang);

        return item => new PartnershipTerminationRequestExportDto
        {
            Id = item.Id,
            FlowState = item.FlowState,
            TerminationDate = item.TerminationDate,
            TerminationReason = item.TerminationReason,
            ProgressSummary = item.ProgressSummary,
            Successes = item.Successes,
            LessonsLearned = item.LessonsLearned,
            PartnerNotes = item.PartnerNotes,
            PartnershipContract = partnershipContractExpression.Invoke(item.PartnershipContract),
            CreationTime = item.CreationTime
        };
    }
    public Guid Id { get; set; }
    public PartnershipContractSimpleDto PartnershipContract { get; set; }
    public string FlowState { get; set; }
    public DateTime TerminationDate { get; set; }
    public string TerminationReason { get; set; }
    public string ProgressSummary { get; set; }
    public string Successes { get; set; }
    public string LessonsLearned { get; set; }
    public string PartnerNotes { get; set; }
    public DateTime CreationTime { get; set; }
}
