using System.Linq.Expressions;
using LinqKit;
using Injaz.ModulePartnership.Core.Dtos.PartnershipContract;
using PartnershipTerminationRequestModel =
    Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipTerminationRequest;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipTerminationRequest;

public class PartnershipTerminationRequestListDto
{
    public static Expression<Func<PartnershipTerminationRequestModel, PartnershipTerminationRequestListDto>> Mapper(
        string lang,
        Guid userId,
        Expression<Func<PartnershipTerminationRequestModel, Guid, object>> flowActionAvailabilityExpression
    )
    {
        var partnershipContractExpression = PartnershipContractSimpleDto.Mapper(lang);

        return item => new PartnershipTerminationRequestListDto
        {
            Id = item.Id,
            FlowState = item.FlowState,
            TerminationDate = item.TerminationDate,
            PartnershipContract = partnershipContractExpression.Invoke(item.PartnershipContract),
            FlowActionAvailability = flowActionAvailabilityExpression.Invoke(item, userId),
        };
    }

    public Guid Id { get; set; }
    public PartnershipContractSimpleDto PartnershipContract { get; set; }
    public string FlowState { get; set; }
    public DateTime TerminationDate { get; set; }
    public object FlowActionAvailability { get; set; }
}
