using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipActivityCommunicationTool;
using MediatR;

namespace Injaz.ModulePartnership.Core.Queries.PartnershipActivityCommunicationTool;

public class GetPartnershipActivityCommunicationToolListQuery :
    IRequest<TableResultDto<PartnershipActivityCommunicationToolGetDto>>
{
    public string Keyword { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
