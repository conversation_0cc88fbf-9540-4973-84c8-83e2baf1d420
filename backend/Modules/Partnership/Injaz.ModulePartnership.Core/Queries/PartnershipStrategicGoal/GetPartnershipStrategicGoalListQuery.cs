using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoal;
using MediatR;

namespace Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoal;

public class GetPartnershipStrategicGoalListQuery : IRequest<TableResultDto<PartnershipStrategicGoalGetDto>>
{
    public Guid PartnershipContractId { get; set; }

    public string? Keyword { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
