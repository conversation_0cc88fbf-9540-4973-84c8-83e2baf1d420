using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalNationalAgenda;
using MediatR;

namespace Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoalNationalAgenda;

public class GetPartnershipStrategicGoalNationalAgendaListQuery :
    IRequest<TableResultDto<PartnershipStrategicGoalNationalAgendaListDto>>
{
    public Guid PartnershipGoalId { get; set; }

    public string Keyword { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
