using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalOperation;
using MediatR;

namespace Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoalOperation;

public class GetPartnershipStrategicGoalOperationListQuery :
    IRequest<TableResultDto<PartnershipStrategicGoalOperationListDto>>
{
    public Guid PartnershipGoalId { get; set; }

    public string Keyword { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
