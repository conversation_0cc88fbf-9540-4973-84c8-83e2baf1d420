using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipTerminationRequest;
using MediatR;

namespace Injaz.ModulePartnership.Core.Queries.PartnershipTerminationRequest;

public class GetPartnershipTerminationRequestListQuery : IRequest<TableResultDto<PartnershipTerminationRequestListDto>>
{
    public Guid UserId { get; set; }
    public bool HasPartnershipPermission { get; set; }
    public string Keyword { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
}
