using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePartnership.Core.Commands.PartnerEvaluationStandard;
using Injaz.ModulePartnership.Core.Queries.PartnerEvaluationStandard;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePartnership.Controllers;

[Route("/partner-evaluation-standard")]
[Authorize(Policy = PermissionNameList.Partner)]
public class PartnerEvaluationStandardController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PartnerEvaluationStandardController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    public async Task<IActionResult> List(string keyword = "", int pageNumber = 0, int pageSize = 20)
    {
        var response = await _mediator.Send(new GetPartnerEvaluationStandardListQuery()
        {
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        });

        return this.GetResponseObject(extra: response);
    }


    [HttpGet("{id:guid}")]
    public async Task<IActionResult> Get(Guid id, bool forEdit = false)
    {
        var item = await _mediator.Send(forEdit
            ? new GetPartnerEvaluationStandardForEditByIdQuery { Id = id }
            : new GetPartnerEvaluationStandardByIdQuery { Id = id }
        );

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreatePartnerEvaluationStandardCommand partnerEvaluationStandard
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(partnerEvaluationStandard));
    }

    [HttpPut]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] UpdatePartnerEvaluationStandardCommand partnerEvaluationStandard
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(partnerEvaluationStandard));
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _mediator.Send(new DeletePartnerEvaluationStandardCommand() { Id = id });
        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }
}
