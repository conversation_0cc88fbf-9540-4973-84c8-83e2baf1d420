using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePartnership.Core.Commands.PartnershipStrategicGoalActivity;
using Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoalActivity;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePartnership.Controllers;

[Route("/partnership-contract/strategic-goal/{partnershipGoalId:guid}/activity")]
[Authorize(Policy = PermissionNameList.PartnershipRead)]
public class PartnershipStrategicGoalActivityController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PartnershipStrategicGoalActivityController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    public async Task<IActionResult> List(
        Guid partnershipGoalId,
        string keyword,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetPartnershipStrategicGoalActivityListQuery()
                {
                    PartnershipGoalId = partnershipGoalId,
                    Keyword = keyword,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                }
            )
        );
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> Get(Guid id, bool forEdit = false)
    {
        return this.GetResponseObject(
            extra: await _mediator.Send(
                forEdit
                    ? new GetPartnershipStrategicGoalActivityForEditByIdQuery() { Id = id }
                    : new GetPartnershipStrategicGoalActivityByIdQuery() { Id = id }
            )
        );
    }

    [HttpPost]
    [Authorize(Policy = PermissionNameList.PartnershipWrite)]
    public async Task<IActionResult> Create(
        Guid partnershipGoalId,
        [BindBodySingleJson] CreatePartnershipStrategicGoalActivityCommand partnershipGoalActivity
    )
    {
        partnershipGoalActivity.PartnershipGoalId = partnershipGoalId;
        await _mediator.Send(partnershipGoalActivity);
        return this.GetResponseObject(messages: new string[] { _localizer["item_created_successfully"] });
    }

    [HttpPut]
    [Authorize(Policy = PermissionNameList.PartnershipWrite)]
    public async Task<IActionResult> Update(
        Guid partnershipGoalId,
        [BindBodySingleJson] UpdatePartnershipStrategicGoalActivityCommand partnershipGoalActivity
    )
    {
        partnershipGoalActivity.PartnershipGoalId = partnershipGoalId;
        await _mediator.Send(partnershipGoalActivity);
        return this.GetResponseObject(messages: new string[] { _localizer["item_updated_successfully"] });
    }

    [HttpDelete("{id:guid}")]
    [Authorize(Policy = PermissionNameList.PartnershipWrite)]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _mediator.Send(new DeletePartnershipStrategicGoalActivityCommand() { Id = id });
        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }


    [HttpGet("{id:guid}/attachment")]
    [Authorize(Policy = PermissionNameList.PartnershipWrite)]
    public async Task<IActionResult> Download(Guid id)
    {
        var result = await _mediator.Send(new DownloadPartnershipStrategicGoalActivityAttachmentCommand() { Id = id });
        return File(result.Stream, result.ContentType);
    }
}
