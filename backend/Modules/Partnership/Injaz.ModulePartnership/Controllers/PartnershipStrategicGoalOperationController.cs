using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePartnership.Core.Commands.PartnershipStrategicGoalOperation;
using Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoalOperation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePartnership.Controllers;

[Route(
    "/partnership-contract/strategic-goal/{partnershipGoalId:guid}/operation")]
[Authorize(Policy = PermissionNameList.PartnershipRead)]
public class PartnershipStrategicGoalOperationController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PartnershipStrategicGoalOperationController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    public async Task<IActionResult> List(
        Guid partnershipGoalId,
        string keyword,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetPartnershipStrategicGoalOperationListQuery()
                {
                    PartnershipGoalId = partnershipGoalId,
                    Keyword = keyword,
                    PageNumber = pageNumber,
                    PageSize = pageSize
                }
            )
        );
    }

    [HttpPost]
    [Authorize(Policy = PermissionNameList.PartnershipWrite)]
    public async Task<IActionResult> Create(
        Guid partnershipGoalId,
        [BindBodySingleJson] CreatePartnershipStrategicGoalOperationCommand partnershipGoalOperation
    )
    {
        partnershipGoalOperation.PartnershipGoalId = partnershipGoalId;
        await _mediator.Send(partnershipGoalOperation);
        return this.GetResponseObject(messages: new string[] { _localizer["item_created_successfully"] });
    }

    [HttpDelete("{id:guid}")]
    [Authorize(Policy = PermissionNameList.PartnershipWrite)]
    public async Task<IActionResult> Delete(
        Guid partnershipGoalId,
        Guid id
    )
    {
        await _mediator.Send(new DeletePartnershipStrategicGoalOperationCommand()
            { Id = id, PartnershipGoalId = partnershipGoalId }
        );

        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }
}
