using LinqKit;
using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.ModulePartnership.Core.Dtos.PartnerEvaluationStandard;
using Injaz.ModulePartnership.Core.Queries.PartnerEvaluationStandard;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnerEvaluationStandard;

public class GetPartnerEvaluationStandardByIdHandler :
    IRequestHandler<GetPartnerEvaluationStandardByIdQuery, PartnerEvaluationStandardGetDto>
{
    private readonly DbContext _db;

    public GetPartnerEvaluationStandardByIdHandler(DbContext db) => _db = db;

    public Task<PartnerEvaluationStandardGetDto> Handle(
        GetPartnerEvaluationStandardByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .PartnerEvaluationStandards
            .AsExpandable()
            .Select(PartnerEvaluationStandardGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
