using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnerEvaluationStandard;
using Injaz.ModulePartnership.Core.Queries.PartnerEvaluationStandard;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnerEvaluationStandard;

public class GetPartnerEvaluationStandardListHandler :
    IRequestHandler<GetPartnerEvaluationStandardListQuery, TableResultDto<PartnerEvaluationStandardGetDto>>
{
    private readonly DbContext _db;
    public GetPartnerEvaluationStandardListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<PartnerEvaluationStandardGetDto>> Handle(
        GetPartnerEvaluationStandardListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .PartnerEvaluationStandards
            .AsExpandable()
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return Task.FromResult(
            new TableResultDto<PartnerEvaluationStandardGetDto>
            {
                Items = items
                    .Select(PartnerEvaluationStandardGetDto.Mapper(lang))
                    .OrderBy(x => x.Name)
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = _db.PartnerEvaluationStandards.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
