using LinqKit;
using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.Core.Services;
using Injaz.ModulePartnership.Core.Commands.PartnershipActivity;
using Injaz.ModulePartnership.Core.Dtos.PartnershipActivity;
using MediatR;
using Microsoft.EntityFrameworkCore;
using PartnershipActivityModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivity;
using PartnershipContractModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract;
using PartnershipActivityPeriodModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityPeriod;

namespace Injaz.ModulePartnership.Handlers.PartnershipActivity;

public class CreateOrUpdatePartnershipActivityHandler :
    IRequestHandler<CreatePartnershipActivityCommand, PartnershipActivityGetDto>,
    IRequestHandler<UpdatePartnershipActivityCommand, PartnershipActivityGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdatePartnershipActivityHandler(
        DbContext db,
        ValidationService validationService
    )
    {
        _db = db;
        _validationService = validationService;
    }

    public Task<PartnershipActivityGetDto> Handle(
        CreatePartnershipActivityCommand command,
        CancellationToken cancellationToken
    ) => CreateOrUpdate(command);

    public Task<PartnershipActivityGetDto> Handle(
        UpdatePartnershipActivityCommand command,
        CancellationToken cancellationToken
    ) => CreateOrUpdate(command);

    private Task<PartnershipActivityGetDto> CreateOrUpdate(
        CreatePartnershipActivityCommand command)
    {
        Validate(command);

        PartnershipActivityModel? item;

        if (command is UpdatePartnershipActivityCommand updateCommand)
        {
            item = _db
                .PartnershipActivities
                .Include(x => x.Periods)
                .Include(partnershipActivity => partnershipActivity.PartnershipContract)
                .FirstOrDefault(x => x.Id.Equals(updateCommand.Id));

            if (item == null) throw new ItemNotFoundException();

            if (
                item.PartnershipContract.FlowState != PartnershipContractModel.StatusNew &&
                item.PartnershipContract.FlowState != PartnershipContractModel.StatusActive &&
                item.PartnershipContract.FlowState != PartnershipContractModel.StatusRejected
            )
                throw new GenericException() { Messages = new[] { "you_cannot_edit_this_contract" } };

            foreach (var period in item.Periods)
            {
                if (period.IsLocked) continue;

                var commandPeriod = updateCommand
                    .Periods
                    .First(x => x.Id.Equals(period.Id));

                period.Target = commandPeriod.Target;

                if (item.PartnershipContract.FlowState == PartnershipContractModel.StatusActive)
                {
                    period.Value = commandPeriod.Value;
                }
            }
        }
        else
        {
            item = new PartnershipActivityModel();

            _db.PartnershipActivities.Add(item);

            item.PartnershipContractId = command.PartnershipContractId;
            item.Periods = command
                .Periods
                .Select(x => new PartnershipActivityPeriodModel()
                {
                    Period = x.Period,
                    Target = x.Target,
                    IsLocked = false,
                })
                .ToList();
        }

        item.Purpose = command.Purpose;
        item.CommunicationToolId = command.CommunicationTool.Id;
        item.Year = command.Year;

        _db.SaveChanges();

        return Task.FromResult(
            _db
                .PartnershipActivities
                .AsExpandable()
                .Select(PartnershipActivityGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    private void Validate(CreatePartnershipActivityCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
            throw new GenericException { Messages = errors.Select(x => x.ErrorMessage).ToArray() };

        var periodsCount = command.Periods.Count();

        if (periodsCount != 4)
            throw new GenericException() { Messages = new[] { "periods_must_be_four" } };

        for (var i = 0; i < 4; i++)
        {
            if (command.Periods.FirstOrDefault(x => x.Period == i) == null)
                throw new GenericException() { Messages = new[] { $"period_number_{i + 1}_is_missing" } };
        }
    }
}
