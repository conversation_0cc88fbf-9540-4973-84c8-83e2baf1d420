using LinqKit;
using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.Core.Services;
using Injaz.ModulePartnership.Core.Commands.PartnershipActivityCommunicationTool;
using Injaz.ModulePartnership.Core.Dtos.PartnershipActivityCommunicationTool;
using MediatR;
using PartnershipActivityCommunicationToolModel =
    Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityCommunicationTool;

namespace Injaz.ModulePartnership.Handlers.PartnershipActivityCommunicationTool;

public class CreateOrUpdatePartnershipActivityCommunicationToolHandler :
    IRequestHandler<CreatePartnershipActivityCommunicationToolCommand, PartnershipActivityCommunicationToolGetDto>,
    IRequestHandler<UpdatePartnershipActivityCommunicationToolCommand, PartnershipActivityCommunicationToolGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdatePartnershipActivityCommunicationToolHandler(
        DbContext db,
        ValidationService validationService
    )
    {
        _db = db;
        _validationService = validationService;
    }

    public Task<PartnershipActivityCommunicationToolGetDto> Handle(
        CreatePartnershipActivityCommunicationToolCommand command,
        CancellationToken cancellationToken
    ) => CreateOrUpdate(command);

    public Task<PartnershipActivityCommunicationToolGetDto> Handle(
        UpdatePartnershipActivityCommunicationToolCommand command,
        CancellationToken cancellationToken
    ) => CreateOrUpdate(command);

    private Task<PartnershipActivityCommunicationToolGetDto> CreateOrUpdate(
        CreatePartnershipActivityCommunicationToolCommand command
    )
    {
        if (!_validationService.IsValid(command, out var errors))
            throw new GenericException { Messages = errors.Select(x => x.ErrorMessage).ToArray() };

        PartnershipActivityCommunicationToolModel? item;

        if (command is UpdatePartnershipActivityCommunicationToolCommand updateCommand)
        {
            item = _db.PartnershipActivityCommunicationTools.Find(updateCommand.Id);
            if (item == null) throw new ItemNotFoundException();
        }
        else
        {
            item = new PartnershipActivityCommunicationToolModel();
            _db.PartnershipActivityCommunicationTools.Add(item);
        }

        item.NameAr = command.NameAr.Trim();
        item.NameEn = HelperFunctions.Default(command.NameEn?.Trim(), command.NameAr);

        _db.SaveChanges();

        return Task.FromResult(
            _db
                .PartnershipActivityCommunicationTools
                .AsExpandable()
                .Select(PartnershipActivityCommunicationToolGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }
}
