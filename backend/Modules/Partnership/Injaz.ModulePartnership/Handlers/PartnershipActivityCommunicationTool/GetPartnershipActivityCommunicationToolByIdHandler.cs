using LinqKit;
using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.ModulePartnership.Core.Dtos.PartnershipActivityCommunicationTool;
using Injaz.ModulePartnership.Core.Queries.PartnershipActivityCommunicationTool;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipActivityCommunicationTool;

public class GetPartnershipActivityCommunicationToolByIdHandler :
    IRequestHandler<GetPartnershipActivityCommunicationToolByIdQuery, PartnershipActivityCommunicationToolGetDto>
{
    private readonly DbContext _db;

    public GetPartnershipActivityCommunicationToolByIdHandler(DbContext db) => _db = db;

    public Task<PartnershipActivityCommunicationToolGetDto> Handle(
        GetPartnershipActivityCommunicationToolByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .PartnershipActivityCommunicationTools
            .AsExpandable()
            .Select(PartnershipActivityCommunicationToolGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
