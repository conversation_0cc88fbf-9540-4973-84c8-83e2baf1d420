using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Exceptions;
using Injaz.Core.Misc;
using Injaz.Core.Services.FileManager;
using Injaz.ModulePartnership.Core.Queries.PartnershipActivityPeriodAttachment;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipActivityPeriodAttachment;

public class GetPartnershipActivityPeriodAttachmentStreamHandler :
    IRequestHandler<GetPartnershipActivityPeriodAttachmentStreamQuery, FileContent>
{
    private readonly DbContext _db;
    private readonly IFileManager _fileManager;

    public GetPartnershipActivityPeriodAttachmentStreamHandler(
        DbContext db,
        IFileManager fileManager
    )
    {
        _db = db;
        _fileManager = fileManager;
    }

    public async Task<FileContent> Handle(
        GetPartnershipActivityPeriodAttachmentStreamQuery query,
        CancellationToken cancellationToken
    )
    {
        var attachment = _db
            .PartnershipActivityPeriodAttachments
            .AsExpandable()
            .Where(x => x.Id == query.AttachmentId)
            .Where(x =>
                query.CurrentUserHasPartnershipPermission ||
                x.Period.PartnershipActivity.PartnershipContract.CreatedById == query.CurrentUserId
            )
            .Select(x => new { x.FileName, x.ContentType })
            .FirstOrDefault();

        if (attachment == null) throw new ItemNotFoundException();

        var stream =
            await _fileManager.GetAsync($"{Directories.PartnershipActivityPeriodAttachments}/{attachment.FileName}");

        return new FileContent(stream, attachment.ContentType);
    }
}
