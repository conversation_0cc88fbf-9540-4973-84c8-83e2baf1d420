using Injaz.Core.Exceptions;
using Injaz.ModulePartnership.Core.Commands.PartnershipContract;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipContract;

public class DeletePartnershipContractHandler : IRequestHandler<DeletePartnershipContractCommand>
{
    private readonly DbContext _db;

    public DeletePartnershipContractHandler(DbContext db) => _db = db;

    public Task<Unit> Handle(DeletePartnershipContractCommand command, CancellationToken cancellationToken)
    {
        var item = _db
            .PartnershipContracts
            .Where(x => command.HasPartnershipPermission || x.CreatedById == command.UserId)
            .FirstOrDefault(x => x.Id == command.Id);

        if (item == null) throw new ItemNotFoundException();

        _db.PartnershipContracts.Remove(item);

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
