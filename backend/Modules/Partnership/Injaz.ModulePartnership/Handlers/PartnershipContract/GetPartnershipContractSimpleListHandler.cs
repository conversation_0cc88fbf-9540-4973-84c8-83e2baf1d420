using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipContract;
using Injaz.ModulePartnership.Core.Queries.PartnershipContract;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipContract;

internal class GetPartnershipContractSimpleListHandler :
    IRequestHandler<GetPartnershipContractSimpleListQuery, TableResultDto<PartnershipContractSimpleListDto>>
{
    private readonly DbContext _db;

    public GetPartnershipContractSimpleListHandler(DbContext db)
    {
        _db = db;
    }

    public Task<TableResultDto<PartnershipContractSimpleListDto>> Handle(
        GetPartnershipContractSimpleListQuery query,
        CancellationToken cancellationToken)
    {
        var items = _db.PartnershipContracts
            .AsExpandable()
            .Where(x => x.PartnerId == query.PartnerId);

        return Task.FromResult(
            new TableResultDto<PartnershipContractSimpleListDto>
            {
                Items = items
                    .OrderBy(x => x.StartDate)
                    .Select(
                        PartnershipContractSimpleListDto.Mapper(
                            HelperFunctions.GetLanguageCode()
                        )
                    )
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = _db.PartnershipContracts.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
