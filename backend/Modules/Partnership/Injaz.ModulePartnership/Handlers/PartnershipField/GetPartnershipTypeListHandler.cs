using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipField;
using Injaz.ModulePartnership.Core.Queries.PartnershipField;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipField;

public class
    GetPartnershipFieldListHandler : IRequestHandler<GetPartnershipFieldListQuery, TableResultDto<PartnershipFieldGetDto>>
{
    private readonly DbContext _db;

    public GetPartnershipFieldListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<PartnershipFieldGetDto>> Handle(
        GetPartnershipFieldListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .PartnershipFields
            .AsExpandable()
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword));

        var lang = HelperFunctions.GetLanguageCode();


        return Task.FromResult(
            new TableResultDto<PartnershipFieldGetDto>
            {
                Items = items
                    .Select(PartnershipFieldGetDto.Mapper(lang))
                    .OrderBy(x => x.Name)
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = _db.PartnershipFields.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
