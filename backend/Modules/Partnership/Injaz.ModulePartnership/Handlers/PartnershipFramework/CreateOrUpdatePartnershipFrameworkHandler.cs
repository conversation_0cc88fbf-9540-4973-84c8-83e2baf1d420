using LinqKit;
using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.Core.Services;
using Injaz.ModulePartnership.Core.Commands.PartnershipFramework;
using Injaz.ModulePartnership.Core.Dtos.PartnershipFramework;
using MediatR;
using PartnershipFrameworkModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipFramework;

namespace Injaz.ModulePartnership.Handlers.PartnershipFramework;

public class CreateOrUpdatePartnershipFrameworkHandler :
    IRequestHandler<CreatePartnershipFrameworkCommand, PartnershipFrameworkGetDto>,
    IRequestHandler<UpdatePartnershipFrameworkCommand, PartnershipFrameworkGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdatePartnershipFrameworkHandler(
        DbContext db,
        ValidationService validationService
    )
    {
        _db = db;
        _validationService = validationService;
    }

    public Task<PartnershipFrameworkGetDto> Handle(CreatePartnershipFrameworkCommand command,
        CancellationToken cancellationToken)
        => CreateOrUpdate(command);

    public Task<PartnershipFrameworkGetDto> Handle(UpdatePartnershipFrameworkCommand command,
        CancellationToken cancellationToken)
        => CreateOrUpdate(command);

    private Task<PartnershipFrameworkGetDto> CreateOrUpdate(CreatePartnershipFrameworkCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
            throw new GenericException { Messages = errors.Select(x => x.ErrorMessage).ToArray() };

        PartnershipFrameworkModel? item;

        if (command is UpdatePartnershipFrameworkCommand updateCommand)
        {
            item = _db.PartnershipFrameworks.Find(updateCommand.Id);
            if (item == null) throw new ItemNotFoundException();
        }
        else
        {
            item = new PartnershipFrameworkModel();
            _db.PartnershipFrameworks.Add(item);
        }

        item.NameAr = command.NameAr.Trim();
        item.NameEn = HelperFunctions.Default(command.NameEn?.Trim(), command.NameAr);

        _db.SaveChanges();

        return Task.FromResult(
            _db
                .PartnershipFrameworks
                .AsExpandable()
                .Select(PartnershipFrameworkGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }
}
