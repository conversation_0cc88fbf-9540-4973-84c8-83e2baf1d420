using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipFramework;
using Injaz.ModulePartnership.Core.Queries.PartnershipFramework;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipFramework;

public class GetPartnershipFrameworkListHandler :
    IRequestHandler<GetPartnershipFrameworkListQuery, TableResultDto<PartnershipFrameworkGetDto>>
{
    private readonly DbContext _db;
    public GetPartnershipFrameworkListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<PartnershipFrameworkGetDto>> Handle(
        GetPartnershipFrameworkListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .PartnershipFrameworks
            .AsExpandable()
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword));

        var lang = HelperFunctions.GetLanguageCode();


        return Task.FromResult(
            new TableResultDto<PartnershipFrameworkGetDto>
            {
                Items = items
                    .Select(PartnershipFrameworkGetDto.Mapper(lang))
                    .OrderBy(x => x.Name)
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = _db.PartnershipFrameworks.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
