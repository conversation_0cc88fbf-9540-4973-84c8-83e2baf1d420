using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePartnership.Core.Commands.PartnershipScope;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModulePartnership.Handlers.PartnershipScope;

public class DeletePartnershipScopeHandler : IRequestHandler<DeletePartnershipScopeCommand>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeletePartnershipScopeHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(DeletePartnershipScopeCommand command, CancellationToken cancellationToken)
    {
        var data = _db.PartnershipScopes.Where(x => x.Id == command.Id)
            .Select(x => new { Item = x, IsLinkedToOtherResources = x.PartnershipContractLinks.Any() })
            .FirstOrDefault();

        if (data == null) throw new ItemNotFoundException();

        // Ensure that the item does not have any links under it.
        if (data.IsLinkedToOtherResources)
            throw new GenericException
                { Messages = new string[] { _localizer["cannot_delete_item_there_are_contracts_under_it"] } };

        _db.PartnershipScopes.Remove(data.Item);

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
