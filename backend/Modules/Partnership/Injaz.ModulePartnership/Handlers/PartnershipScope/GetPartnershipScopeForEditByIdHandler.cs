using LinqKit;
using Injaz.Core.Exceptions;
using Injaz.ModulePartnership.Core.Dtos.PartnershipScope;
using Injaz.ModulePartnership.Core.Queries.PartnershipScope;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipScope;

public class GetPartnershipScopeForEditByIdHandler :
    IRequestHandler<GetPartnershipScopeForEditByIdQuery, PartnershipScopeEditDto>
{
    private readonly DbContext _db;

    public GetPartnershipScopeForEditByIdHandler(DbContext db) => _db = db;


    public Task<PartnershipScopeEditDto> Handle(
        GetPartnershipScopeForEditByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .PartnershipScopes
            .AsExpandable()
            .Select(PartnershipScopeEditDto.Mapper())
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
