using Injaz.Core.Exceptions;
using Injaz.ModulePartnership.Core.Commands.PartnershipStrategicGoal;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipStrategicGoal;

public class DeletePartnershipStrategicGoalHandler : IRequestHandler<DeletePartnershipStrategicGoalCommand>
{
    private readonly DbContext _db;

    public DeletePartnershipStrategicGoalHandler(DbContext db) => _db = db;

    public Task<Unit> Handle(
        DeletePartnershipStrategicGoalCommand command,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .PartnershipGoals
            .FirstOrDefault(x => x.Id == command.Id);

        if (item == null) throw new ItemNotFoundException();

        _db.PartnershipGoalActivities.RemoveRange(
            _db.PartnershipGoalActivities.Where(x => x.PartnershipGoalId == item.Id)
        );

        _db.PartnershipGoalOperationLinks.RemoveRange(
            _db.PartnershipGoalOperationLinks.Where(x => x.PartnershipGoalId == item.Id)
        );

        _db.PartnershipGoalServiceLinks.RemoveRange(
            _db.PartnershipGoalServiceLinks.Where(x => x.PartnershipGoalId == item.Id)
        );

        _db.PartnershipGoalNationalAgendaLinks.RemoveRange(
            _db.PartnershipGoalNationalAgendaLinks.Where(x => x.PartnershipGoalId == item.Id)
        );

        _db.PartnershipGoals.Remove(item);

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
