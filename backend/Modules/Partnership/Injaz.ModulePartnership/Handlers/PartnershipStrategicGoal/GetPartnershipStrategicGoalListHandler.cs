using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoal;
using Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoal;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipStrategicGoal;

public class GetPartnershipStrategicGoalListHandler :
    IRequestHandler<GetPartnershipStrategicGoalListQuery, TableResultDto<PartnershipStrategicGoalGetDto>>
{
    private readonly DbContext _db;
    public GetPartnershipStrategicGoalListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<PartnershipStrategicGoalGetDto>> Handle(
        GetPartnershipStrategicGoalListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";
        var lang = HelperFunctions.GetLanguageCode();

        var items = _db
            .PartnershipGoals
            .AsExpandable()
            .Where(x => x.PartnershipContractId.Equals(query.PartnershipContractId));

        var filteredItems = items
            .Select(PartnershipStrategicGoalGetDto.Mapper(lang))
            .OrderBy(x => x.StrategicGoal.Name)
            .Where(x => x.StrategicGoal.Name.ToLower().Contains(query.Keyword));


        return Task.FromResult(
            new TableResultDto<PartnershipStrategicGoalGetDto>
            {
                Items = filteredItems
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = items.Count(),
                FilteredCount = filteredItems.Count()
            }
        );
    }
}
