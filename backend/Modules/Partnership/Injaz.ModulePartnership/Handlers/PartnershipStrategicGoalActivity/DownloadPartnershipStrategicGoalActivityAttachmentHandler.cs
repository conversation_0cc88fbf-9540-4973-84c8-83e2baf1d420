using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Exceptions;
using Injaz.Core.Misc;
using Injaz.Core.Services.FileManager;
using Injaz.ModulePartnership.Core.Commands.PartnershipStrategicGoalActivity;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipStrategicGoalActivity;

public class DownloadPartnershipStrategicGoalActivityAttachmentHandler :
    IRequestHandler<DownloadPartnershipStrategicGoalActivityAttachmentCommand, FileContent>
{
    private readonly DbContext _db;
    private readonly IFileManager _fileManager;

    public DownloadPartnershipStrategicGoalActivityAttachmentHandler(
        DbContext db,
        IFileManager fileManager
    )
    {
        _db = db;
        _fileManager = fileManager;
    }

    public async Task<FileContent> Handle(
        DownloadPartnershipStrategicGoalActivityAttachmentCommand command,
        CancellationToken cancellationToken
    )
    {
        var attachment = _db
            .PartnershipGoalActivities
            .AsExpandable()
            .Where(x => x.FileName == command.Id.ToString())
            .Select(x => new { x.FileName, x.ContentType })
            .FirstOrDefault();

        if (attachment == null) throw new ItemNotFoundException();

        var stream = await _fileManager.GetAsync(
            $"{Directories.PartnershipStrategicGoalActivityAttachments}/{attachment.FileName}"
        );

        return new FileContent(stream, attachment.ContentType);
    }
}
