using LinqKit;
using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalInitiative;
using Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoalInitiative;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipStrategicGoalInitiative;

public class GetPartnershipStrategicGoalInitiativeForEditByIdHandler :
    IRequestHandler<GetPartnershipStrategicGoalInitiativeForEditByIdQuery, PartnershipStrategicGoalInitiativeEditDto>
{
    private readonly DbContext _db;

    public GetPartnershipStrategicGoalInitiativeForEditByIdHandler(DbContext db) => _db = db;


    public Task<PartnershipStrategicGoalInitiativeEditDto> Handle(
        GetPartnershipStrategicGoalInitiativeForEditByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var lang = HelperFunctions.GetLanguageCode();

        var item = _db
            .PartnershipGoalInitiatives
            .AsExpandable()
            .Select(PartnershipStrategicGoalInitiativeEditDto.Mapper(lang))
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
