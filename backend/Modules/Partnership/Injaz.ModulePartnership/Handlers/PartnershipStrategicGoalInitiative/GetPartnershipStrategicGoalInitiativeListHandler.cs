using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalInitiative;
using Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoalInitiative;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipStrategicGoalInitiative;

public class GetPartnershipStrategicGoalInitiativeListHandler :
    IRequestHandler<GetPartnershipStrategicGoalInitiativeListQuery,
        TableResultDto<PartnershipStrategicGoalInitiativeListDto>>
{
    private readonly DbContext _db;

    public GetPartnershipStrategicGoalInitiativeListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<PartnershipStrategicGoalInitiativeListDto>> Handle(
        GetPartnershipStrategicGoalInitiativeListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var lang = HelperFunctions.GetLanguageCode();

        var items = _db
            .PartnershipGoalInitiatives
            .AsExpandable()
            .Where(x => x.PartnershipGoalId.Equals(query.PartnershipGoalId));

        var filteredItems = items
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword))
            .Select(PartnershipStrategicGoalInitiativeListDto.Mapper(lang))
            .OrderBy(x => x.Name);


        return Task.FromResult(
            new TableResultDto<PartnershipStrategicGoalInitiativeListDto>
            {
                Items = filteredItems
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = items.Count(),
                FilteredCount = filteredItems.Count()
            }
        );
    }
}
