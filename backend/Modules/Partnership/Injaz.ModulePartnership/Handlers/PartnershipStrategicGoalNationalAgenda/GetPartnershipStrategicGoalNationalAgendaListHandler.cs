using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalNationalAgenda;
using Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoalNationalAgenda;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipStrategicGoalNationalAgenda;

public class GetPartnershipStrategicGoalNationalAgendaListHandler :
    IRequestHandler<GetPartnershipStrategicGoalNationalAgendaListQuery,
        TableResultDto<PartnershipStrategicGoalNationalAgendaListDto>>
{
    private readonly DbContext _db;
    public GetPartnershipStrategicGoalNationalAgendaListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<PartnershipStrategicGoalNationalAgendaListDto>> Handle(
        GetPartnershipStrategicGoalNationalAgendaListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";
        var lang = HelperFunctions.GetLanguageCode();

        var items = _db
            .PartnershipGoalNationalAgendaLinks
            .AsExpandable()
            .Where(x => x.PartnershipGoalId.Equals(query.PartnershipGoalId));

        var filteredItems = items
            .Select(PartnershipStrategicGoalNationalAgendaListDto.Mapper(lang))
            .OrderBy(x => x.NationalAgenda.Name)
            .Where(x => x.NationalAgenda.Name.ToLower().Contains(query.Keyword));

        return Task.FromResult(
            new TableResultDto<PartnershipStrategicGoalNationalAgendaListDto>
            {
                Items = filteredItems
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = items.Count(),
                FilteredCount = filteredItems.Count()
            }
        );
    }
}
