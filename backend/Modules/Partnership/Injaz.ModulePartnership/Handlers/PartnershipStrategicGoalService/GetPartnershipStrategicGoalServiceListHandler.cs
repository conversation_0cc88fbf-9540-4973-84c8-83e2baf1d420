using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.ModulePartnership.Core.Dtos.PartnershipStrategicGoalService;
using Injaz.ModulePartnership.Core.Queries.PartnershipStrategicGoalService;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipStrategicGoalService;

public class GetPartnershipStrategicGoalServiceListHandler :
    IRequestHandler<GetPartnershipStrategicGoalServiceListQuery, TableResultDto<PartnershipStrategicGoalServiceListDto>>
{
    private readonly DbContext _db;
    public GetPartnershipStrategicGoalServiceListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<PartnershipStrategicGoalServiceListDto>> Handle(
        GetPartnershipStrategicGoalServiceListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";
        var lang = HelperFunctions.GetLanguageCode();

        var items = _db
            .PartnershipGoalServiceLinks
            .AsExpandable()
            .Where(x => x.PartnershipGoalId.Equals(query.PartnershipGoalId));

        var filteredItems = items
            .Select(PartnershipStrategicGoalServiceListDto.Mapper(lang))
            .OrderBy(x => x.Service.SupplementaryServiceName ?? x.Service.SubServiceName ?? x.Service.MainServiceName)
            .Where(x =>
                x.Service.SupplementaryServiceName.ToLower().Contains(query.Keyword) ||
                x.Service.SubServiceName.ToLower().Contains(query.Keyword) ||
                x.Service.MainServiceName.ToLower().Contains(query.Keyword)
            );

        return Task.FromResult(
            new TableResultDto<PartnershipStrategicGoalServiceListDto>
            {
                Items = filteredItems
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = items.Count(),
                FilteredCount = filteredItems.Count()
            }
        );
    }
}
