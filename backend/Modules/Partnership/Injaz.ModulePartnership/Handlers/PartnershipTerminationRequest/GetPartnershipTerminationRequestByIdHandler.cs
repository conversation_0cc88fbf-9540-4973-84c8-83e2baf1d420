using LinqKit;
using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.Core.Flow.Services;
using Injaz.ModulePartnership.Core.Dtos.PartnershipTerminationRequest;
using Injaz.ModulePartnership.Core.Queries.PartnershipTerminationRequest;
using MediatR;
using PartnershipTerminationRequestModel =
    Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipTerminationRequest;

namespace Injaz.ModulePartnership.Handlers.PartnershipTerminationRequest;

public class GetPartnershipTerminationRequestByIdHandler :
    IRequestHandler<GetPartnershipTerminationRequestByIdQuery, PartnershipTerminationRequestGetDto>
{
    private readonly DbContext _db;
    private readonly FlowService<PartnershipTerminationRequestModel> _flowService;

    public GetPartnershipTerminationRequestByIdHandler(
        DbContext db,
        FlowService<PartnershipTerminationRequestModel> flowService
    )
    {
        _db = db;
        _flowService = flowService;
    }

    public Task<PartnershipTerminationRequestGetDto> Handle(
        GetPartnershipTerminationRequestByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var flowActionAvailabilityExpression = _flowService.GenerateActionAvailability();

        var item = _db
            .PartnershipTerminationRequests
            .AsExpandable()
            .Where(x => x.Id.Equals(query.Id))
            .Where(x => query.HasPartnershipPermission || x.CreatedById == query.UserId)
            .Select(PartnershipTerminationRequestGetDto.Mapper(
                HelperFunctions.GetLanguageCode(),
                query.UserId,
                flowActionAvailabilityExpression
            ))
            .FirstOrDefault();

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
