using LinqKit;
using Injaz.Core.Exceptions;
using Injaz.ModulePartnership.Core.Dtos.PartnershipType;
using Injaz.ModulePartnership.Core.Queries.PartnershipType;
using MediatR;

namespace Injaz.ModulePartnership.Handlers.PartnershipType;

public class GetPartnershipTypeForEditByIdHandler :
    IRequestHandler<GetPartnershipTypeForEditByIdQuery, PartnershipTypeEditDto>
{
    private readonly DbContext _db;

    public GetPartnershipTypeForEditByIdHandler(DbContext db) => _db = db;


    public Task<PartnershipTypeEditDto> Handle(
        GetPartnershipTypeForEditByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .PartnershipTypes
            .AsExpandable()
            .Select(PartnershipTypeEditDto.Mapper())
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
