using Injaz.Core;
using Injaz.Core.Dtos.User;
using Injaz.Core.Models.DomainClasses.App.PartnershipModel;
using Injaz.ModuleNotification.Core.Commands;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using LinqKit;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.ModuleEmail.Core.Commands;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModulePartnership.HostedServices;

public class PartnershipActivityPeriodReminderHostedService : BackgroundService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;

    public PartnershipActivityPeriodReminderHostedService(IServiceScopeFactory serviceScopeFactory) =>
        _serviceScopeFactory = serviceScopeFactory;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await RunPeriodicTask();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message}");
            }

            try
            {
                // TODO: we need to check this, the hosted service keeps crashing here.
                await Task.Delay(TimeSpan.FromDays(1), stoppingToken);
            }
            catch
            {
                Console.WriteLine("Partnership Activity Period Reminder Hosted Service Failure");
            }
        }
    }

    private async Task RunPeriodicTask()
    {
        using var scope = _serviceScopeFactory.CreateScope();
        var dbContext = scope.ServiceProvider.GetRequiredService<DbContext>();
        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var currentYear = DateTime.Now.Year;
        var currentPeriod = GetCurrentPeriod();
        var periodName = GetPeriodName();

        var periodsToNotify = dbContext
            .PartnershipContracts
            .Where(x => x.FlowState == PartnershipContract.StatusActive)
            .SelectMany(x => x.Activities)
            .Where(x => x.Year == currentYear)
            .SelectMany(a => a.Periods)
            .Where(p =>
                p.Period == currentPeriod &&
                p.Value == null &&
                !p.IsLocked &&
                !p.IsReminderNotificationSent
            )
            .Include(x => x.PartnershipActivity)
            .ThenInclude(x => x.PartnershipContract)
            .ToList();

        foreach (var period in periodsToNotify)
        {
            var users = GetUserCreatorOfThePartnershipContract(
                dbContext,
                period.PartnershipActivity.PartnershipContract.CreatedById
            );

            var activity = period.PartnershipActivity;
            var contract = activity.PartnershipContract;

            var messageAr =
                $"برجاء ادخال نتيجة {periodName[0]} في خطة الاتصال {activity.Purpose} في العقد {contract.TitleAr} ";
            var messageEn =
                $"Please enter the result of {periodName[1]} in the communication plan {activity.Purpose} in the contract {contract.TitleEn}.";

            await SendNotification(
                users,
                messageAr,
                messageEn,
                messageAr,
                messageEn,
                contract.Id,
                mediator
            );

            period.IsReminderNotificationSent = true;
        }

        await dbContext.SaveChangesAsync();
    }

    private static async Task SendNotification(
        UserSimpleDto[] users,
        string titleAr,
        string titleEn,
        string descriptionAr,
        string descriptionEn,
        Guid targetId,
        IMediator mediator
    )
    {
        var notificationCommand = new CreateNotificationCommand
        {
            Users = users,
            TitleAr = titleAr,
            TitleEn = titleEn,
            DescriptionAr = descriptionAr,
            DescriptionEn = descriptionEn,
            TargetType = Notification.TargetTypePartnershipContract,
            TargetId = targetId,
        };

        var emailCommand = new SendDefaultEmailCommand
        {
            UserIds = users.Select(u => u.Id).ToArray(),
            SubjectAr = titleAr,
            SubjectEn = titleEn,
            BodyAr = descriptionAr,
            BodyEn = descriptionEn
        };

        await mediator.Send(emailCommand);
        await mediator.Send(notificationCommand);
    }

    private static UserSimpleDto[] GetUserCreatorOfThePartnershipContract(DbContext dbContext, Guid? creatorId)
    {
        if (creatorId == null) return Array.Empty<UserSimpleDto>();
        var userExpression = UserSimpleDto.Mapper(HelperFunctions.GetLanguageCode());
        var creator = dbContext.Users.FirstOrDefault(x => x.Id == creatorId);
        return creator != null ? new[] { userExpression.Invoke(creator) } : Array.Empty<UserSimpleDto>();
    }

    private static int GetCurrentPeriod()
    {
        var month = DateTime.Now.Month;

        return month switch
        {
            >= 1 and <= 3 => 0, // Q1: January, February, March
            >= 4 and <= 6 => 1, // Q2: April, May, June
            >= 7 and <= 9 => 2, // Q3: July, August, September
            _ => 3, // Q4: October, November, December
        };
    }

    private static string[] GetPeriodName()
    {
        var month = DateTime.Now.Month;

        return month switch
        {
            >= 1 and <= 3 => new[] { "الربع الأول", "First quarter" }, // Q1: January, February, March
            >= 4 and <= 6 => new[] { "الربع الثاني", "Second quarter" }, // Q2: April, May, June
            >= 7 and <= 9 => new[] { "الربع الثالث", "Third quarter" }, // Q3: July, August, September
            _ => new[] { "الربع الرابع", "Fourth quarter" }, // Q4: October, November, December
        };
    }
}
