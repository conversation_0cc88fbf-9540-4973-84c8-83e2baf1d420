using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.PlanDtos.PlanCategories;
using MediatR;

namespace Injaz.ModulePlan.Core.Commands.PlanCategories;

public class CreatePlanCategoryCommand : IRequest<PlanCategoryGetDto>
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "description_ar")] public string DescriptionAr { get; set; }

    [Display(Name = "description_en")] public string DescriptionEn { get; set; }

    [Display(Name = "type")] public string Type { get; set; }
}
