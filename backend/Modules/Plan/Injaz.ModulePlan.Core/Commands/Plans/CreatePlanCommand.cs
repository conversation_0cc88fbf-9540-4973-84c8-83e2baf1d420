using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.GovernmentStrategicGoal;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.MinistryStrategicGoal;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.Partner;
using Injaz.Core.Dtos.PlanDtos.PlanCategories;
using Injaz.Core.Dtos.PlanDtos.PlanFuturePlans;
using Injaz.Core.Dtos.PlanDtos.Plans;
using Injaz.Core.Dtos.StrategicGoal;
using Injaz.Core.Dtos.Team;
using Injaz.Core.Dtos.User;
using MediatR;

namespace Injaz.ModulePlan.Core.Commands.Plans;

public class CreatePlanCommand : IRequest<PlanGetDto>
{
    [Display(Name = "code")]
    [Required(ErrorMessage = "0_is_required")]
    public string Code { get; set; }

    [Display(Name = "year")]
    [Required(ErrorMessage = "0_is_required")]
    public int Year { get; set; }

    [Display(Name = "assigned_department")]
    public DepartmentSimpleDto AssignedDepartment { get; set; }

    [Display(Name = "assigned_team")] public TeamSimpleDto AssignedTeam { get; set; }

    [Display(Name = "assigned_user")] public UserSimpleDto AssignedUser { get; set; }

    [Display(Name = "title_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "title_in_english")] public string NameEn { get; set; }

    [Display(Name = "category")]
    [Required(ErrorMessage = "0_is_required")]
    public PlanCategorySimpleDto Category { get; set; }

    [Display(Name = "from")]
    [Required(ErrorMessage = "0_is_required")]
    public DateTime? From { get; set; }

    [Display(Name = "to")]
    [Required(ErrorMessage = "0_is_required")]
    public DateTime? To { get; set; }

    [Display(Name = "government_strategic_goal")]
    public GovernmentStrategicGoalSimpleDto GovernmentStrategicGoal { get; set; }

    //this is a bad coding example, please code better, we expect better from you, do not disappoint us
    [Display(Name = "government_directions")]
    public string GovernmentDirections { get; set; }

    [Display(Name = "other_partners")] public string OtherPartners { get; set; }

    [Display(Name = "initiatives")] public string Initiatives { get; set; }

    [Display(Name = "description")]
    [MaxLength(1024)]
    public string Description { get; set; }

    [Display(Name = "work_scope")]
    [MaxLength(1024)]
    public string WorkScope { get; set; }

    [Display(Name = "implementation_requirement")]
    [MaxLength(1024)]
    public string ImplementationRequirement { get; set; }

    [Display(Name = "resources")]
    [MinLength(1, ErrorMessage = "0_should_have_at_least_1_item")]
    public IEnumerable<PlanResourceDto> Resources { get; set; }

    [Display(Name = "strategic_goals")] public IEnumerable<StrategicGoalSimpleDto> StrategicGoals { get; set; }
    [Display(Name = "future_plans")] public IEnumerable<PlanFuturePlanSimpleDto> FuturePlans { get; set; }

    [Display(Name = "ministry_strategic_goals")]
    public IEnumerable<MinistryStrategicGoalSimpleDto> MinistryStrategicGoals { get; set; }

    [Display(Name = "inputs")]
    [MinLength(1, ErrorMessage = "0_should_have_at_least_1_item")]
    public IEnumerable<PlanInputWithTextDto> Inputs { get; set; }

    [Display(Name = "partners")] public IEnumerable<PartnerSimpleDto> Partners { get; set; }
    [Display(Name = "internal_partners")] public IEnumerable<DepartmentSimpleDto> PartneringDepartments { get; set; }
    [Display(Name = "operations")] public IEnumerable<OperationSimpleDto> Operations { get; set; }

    [Display(Name = "kpis")] public IEnumerable<KpiSimpleDto> Kpis { get; set; }

    [Display(Name = "expected_outputs")] public IEnumerable<PlanExpectedOutputDto> ExpectedOutputs { get; set; }

    [Display(Name = "lessons_learned")] public IEnumerable<PlanLessonLearnedDto> LessonsLearned { get; set; }

    [Display(Name = "challenges")] public IEnumerable<PlanChallengeDto> Challenges { get; set; }

    [Display(Name = "communication_processes")]
    public IEnumerable<PlanCommunicationProcessDto> CommunicationProcesses { get; set; }

    [Display(Name = "risks")] public IEnumerable<PlanRiskDto> Risks { get; set; }

    [Display(Name = "dependencies")] public IEnumerable<PlanDependencyDto> Dependencies { get; set; }

    [Display(Name = "policies")] public IEnumerable<PlanPolicyDto> Policies { get; set; }

    [Display(Name = "is_budget_allocated")]
    public bool IsBudgetAllocated { get; set; }

    [Display(Name = "financial_requirements")]
    public IEnumerable<PlanFinancialRequirementDto> FinancialRequirements { get; set; }

    [Display(Name = "financial_stages")] public IEnumerable<PlanFinancialStageDto> FinancialStages { get; set; }

    [Display(Name = "expected_benefits")] public IEnumerable<PlanExpectedBenefitDto> ExpectedBenefits { get; set; }
}
