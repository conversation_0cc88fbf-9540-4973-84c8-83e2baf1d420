using System;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.PlanDtos.PlanSubsubtasks;
using MediatR;

namespace Injaz.ModulePlan.Core.Queries.PlanSubtasks;

public class GetApprovableAndFinalizableSubsubtaskListQuery : IRequest<TableResultDto<PlanSubsubtaskDto>>
{
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public Guid[] DepartmentIds { get; set; }
    public Guid[] TeamIds { get; set; }
    public Guid[] UserIds { get; set; }
    public Guid[] SubtaskIds { get; set; }
    public Guid[] TaskIds { get; set; }
    public Guid[] PlanIds { get; set; }
    public string ApprovalStatus { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
}
