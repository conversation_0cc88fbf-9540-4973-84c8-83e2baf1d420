using System;
using MediatR;

namespace Injaz.ModulePlan.Core.Queries.Plans;

public class ExportPlanListQuery : IRequest<byte[]>
{
    public string Keyword { get; set; }
    public string Initiatives { get; set; }
    public int[] Years { get; set; }
    public string AssigneeType { get; set; }
    public Guid[] DepartmentIds { get; set; }
    public bool IncludeChildDepartments { get; set; }
    public Guid[] TeamIds { get; set; }
    public Guid[] UserIds { get; set; }
    public Guid[] CategoryIds { get; set; }
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public Guid[] GovernmentStrategicGoalIds { get; set; }
    public Guid[] KpiIds { get; set; }
    public Guid[] StrategicGoalIds { get; set; }
    public Guid[] PartnerIds { get; set; }
    public string Status { get; set; }
    public string ProgressStatus { get; set; }

    public string OrderBy { get; set; }
    public bool OnlyApprovable { get; set; }
}
