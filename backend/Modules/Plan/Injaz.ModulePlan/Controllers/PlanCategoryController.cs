using System;
using System.Threading.Tasks;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePlan.Core.Commands.PlanCategories;
using Injaz.ModulePlan.Core.Queries.PlanCategories;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePlan.Controllers;

[Authorize(Policy = PermissionNameList.PlanCategory)]
public class PlanCategoryController : Controller
{
    private readonly ISender _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PlanCategoryController(ISender mediator, IStringLocalizer<SharedResource> localizer)
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/plan-category")]
    public async Task<IActionResult> List(
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var query = new GetPlanCategoryListQuery
        {
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        return this.GetResponseObject(extra: await _mediator.Send(query));
    }

    [HttpGet]
    [Route("/plan-category/{id:guid}")]
    public async Task<IActionResult> Get(
        Guid id,
        bool forEdit = false
    )
    {
        return this.GetResponseObject(extra: forEdit
            ? await _mediator.Send(new GetPlanCategoryForEditByIdQuery { Id = id })
            : await _mediator.Send(new GetPlanCategoryByIdQuery { Id = id }));
    }

    [HttpPost]
    [Route("/plan-category")]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreatePlanCategoryCommand planCategory
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(planCategory));
    }

    [HttpPut]
    [Route("/plan-category")]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] UpdatePlanCategoryCommand planCategory
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(planCategory));
    }

    [HttpDelete]
    [Route("/plan-category/{id:guid}")]
    public async Task<IActionResult> Delete(
        Guid id
    )
    {
        await _mediator.Send(new DeletePlanCategoryCommand { Id = id });
        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }
}
