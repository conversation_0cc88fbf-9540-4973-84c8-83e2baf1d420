using System;
using System.Threading.Tasks;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePlan.Core.Commands.PlanFuturePlans;
using Injaz.ModulePlan.Core.Queries.PlanFuturePlans;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePlan.Controllers;

[Route("/plan-future-plan")]
[Authorize]
public class PlanFuturePlanController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PlanFuturePlanController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    public async Task<IActionResult> List(string keyword = "", int pageNumber = 0, int pageSize = 20)
    {
        var response = await _mediator.Send(new GetPlanFuturePlanListQuery()
        {
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        });

        return this.GetResponseObject(extra: response);
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> Get(Guid id, bool forEdit = false)
    {
        var item = await _mediator.Send(forEdit
            ? new GetPlanFuturePlanForEditByIdQuery { Id = id }
            : new GetPlanFuturePlanByIdQuery { Id = id }
        );

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreatePlanFuturePlanCommand planFuturePlan
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(planFuturePlan));
    }

    [HttpPut]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] UpdatePlanFuturePlanCommand planFuturePlan
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(planFuturePlan));
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _mediator.Send(new DeletePlanFuturePlanCommand() { Id = id });
        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }
}
