using System;
using System.Threading.Tasks;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePlan.Core.Commands.PlanInputs;
using Injaz.ModulePlan.Core.Queries.PlanInputs;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePlan.Controllers;

[Authorize(Policy = PermissionNameList.PlanInput)]
public class PlanInputController : Controller
{
    private readonly ISender _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public PlanInputController(ISender mediator, IStringLocalizer<SharedResource> localizer)
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/plan-input")]
    public async Task<IActionResult> List(
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var query = new GetPlanInputListQuery
        {
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        };

        return this.GetResponseObject(extra: await _mediator.Send(query));
    }

    [HttpGet]
    [Route("/plan-input/{id:guid}")]
    public async Task<IActionResult> Get(
        Guid id,
        bool forEdit = false
    )
    {
        return this.GetResponseObject(extra: forEdit
            ? await _mediator.Send(new GetPlanInputForEditByIdQuery { Id = id })
            : await _mediator.Send(new GetPlanInputByIdQuery { Id = id }));
    }

    [HttpPost]
    [Route("/plan-input")]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreatePlanInputCommand planInput
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(planInput));
    }

    [HttpPut]
    [Route("/plan-input")]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] UpdatePlanInputCommand planInput
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(planInput));
    }

    [HttpDelete]
    [Route("/plan-input/{id:guid}")]
    public async Task<IActionResult> Delete(
        Guid id
    )
    {
        await _mediator.Send(new DeletePlanInputCommand { Id = id });
        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }
}
