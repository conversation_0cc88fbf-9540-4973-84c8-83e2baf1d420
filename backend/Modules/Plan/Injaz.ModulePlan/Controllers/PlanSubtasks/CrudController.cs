using System;
using System.Threading.Tasks;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePlan.Core.Commands.PlanSubsubtasks;
using Injaz.ModulePlan.Core.Commands.PlanSubtasks;
using Injaz.ModulePlan.Core.Queries.PlanSubtasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePlan.Controllers.PlanSubtasks;

[Authorize]
public class CrudController : Controller
{
    private readonly ISender _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CrudController(
        ISender mediator,
        IStringLocalizer<SharedResource> localizer)
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/plan-subtask")]
    [Authorize(Policy = PermissionNameList.PlanRead)]
    public async Task<IActionResult> List(
        string keyword = "",
        string assigneeType = "", // team | department | user
        Guid[] departmentIds = null,
        bool includeChildDepartments = false,
        Guid[] teamIds = null,
        Guid[] userIds = null,
        Guid[] planIds = null,
        Guid[] taskIds = null,
        DateTime? from = null,
        DateTime? to = null,
        string status = null,
        int pageNumber = 0,
        int pageSize = 20,
        string orderBy = null,

        // When set to true, the base items would
        // include a filter based on passed `planIds` and
        // `taskIds`.
        // This is needed when one wants to fetch
        // the subtasks of a plan/task but does not want
        // the filtered count to be affected.
        bool considerPlanFilterAsBase = false
    )
    {
        var query = new GetPlanSubtaskListQuery
        {
            Keyword = keyword,
            AssigneeType = assigneeType,
            DepartmentIds = departmentIds,
            IncludeChildDepartments = includeChildDepartments,
            TeamIds = teamIds,
            UserIds = userIds,
            PlanIds = planIds,
            TaskIds = taskIds,
            From = from,
            To = to,
            Status = status,
            PageNumber = pageNumber,
            PageSize = pageSize,
            OrderBy = orderBy,
            ConsiderPlanFilterAsBase = considerPlanFilterAsBase
        };

        return this.GetResponseObject(extra: await _mediator.Send(query));
    }

    [HttpGet]
    [Route("/plan-subtask/{id:guid}")]
    [Authorize(Policy = PermissionNameList.PlanRead)]
    public async Task<IActionResult> Get(
        Guid id,
        bool forEdit = false
    )
    {
        return this.GetResponseObject(extra: forEdit
            ? await _mediator.Send(new GetPlanSubtaskForEditByIdQuery { Id = id })
            : await _mediator.Send(new GetPlanSubtaskByIdQuery { Id = id }));
    }

    [HttpPost]
    [Route("/plan-subtask/{taskId:guid}")]
    [Authorize(Policy = PermissionNameList.PlanWrite)]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreatePlanSubtaskCommand planSubtask,
        Guid taskId
    )
    {
        planSubtask.TaskId = taskId;
        return this.GetResponseObject(extra: await _mediator.Send(planSubtask));
    }

    [HttpPut]
    [Route("/plan-subtask")]
    [Authorize(Policy = PermissionNameList.PlanWrite)]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] UpdatePlanSubtaskCommand planSubtask
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(planSubtask));
    }

    [HttpPut]
    [Route("/plan-subtask/subsubtask")]
    [Authorize(Policy = PermissionNameList.PlanWrite)]
    public async Task<IActionResult> UpdateSubsubtask(
        [BindBodySingleJson] UpdateSubsubtaskCommand planSubsubtask
    )
    {
        await _mediator.Send(planSubsubtask);
        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpDelete]
    [Route("/plan-subtask/{id:guid}")]
    [Authorize(Policy = PermissionNameList.PlanWrite)]
    public async Task<IActionResult> Delete(
        Guid id
    )
    {
        await _mediator.Send(new DeletePlanSubtaskCommand { Id = id });
        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }
}
