using System;
using System.Threading.Tasks;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModulePlan.Core.Commands.PlanSubtasks;
using Injaz.ModulePlan.Core.Queries.PlanSubtasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePlan.Controllers.PlanSubtasks;

[Authorize]
public class SubsubtaskApprovalController : Controller
{
    private readonly ISender _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public SubsubtaskApprovalController(
        ISender mediator,
        IStringLocalizer<SharedResource> localizer)
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/plan-subtask/approvable-finalizable-subsubtask")]
    [Authorize(Policy = PermissionNameList.PlanSubsubtaskFinalApproval)]
    public async Task<IActionResult> GetApprovableAndFinalizableSubsubtasks(
        DateTime? from = null,
        DateTime? to = null,
        Guid[] departmentIds = null,
        Guid[] teamIds = null,
        Guid[] userIds = null,
        Guid[] subtaskIds = null,
        Guid[] taskIds = null,
        Guid[] planIds = null,
        string approvalStatus = null,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var query = new GetApprovableAndFinalizableSubsubtaskListQuery
        {
            From = from,
            To = to,
            DepartmentIds = departmentIds,
            TeamIds = teamIds,
            UserIds = userIds,
            SubtaskIds = subtaskIds,
            TaskIds = taskIds,
            PlanIds = planIds,
            ApprovalStatus = approvalStatus,
            PageNumber = pageNumber,
            PageSize = pageSize,
        };

        return this.GetResponseObject(extra: await _mediator.Send(query));
    }

    [HttpPut]
    [Route("/plan-subtask/subsubtask/submit/{subsubtaskId:guid}")]
    [Authorize(Policy = PermissionNameList.PlanRead)]
    public async Task<IActionResult> Submit(Guid subsubtaskId)
    {
        await _mediator.Send(new SubmitPlanSubsubtaskCommand { SubsubtaskId = subsubtaskId });
        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpPut]
    [Route("/plan-subtask/subsubtask/approve/{subsubtaskId:guid}")]
    [Authorize(Policy = PermissionNameList.PlanRead)]
    public async Task<IActionResult> Approve(Guid subsubtaskId, string note)
    {
        await _mediator.Send(new ApprovePlanSubsubtaskCommand
        {
            SubsubtaskId = subsubtaskId,
            Note = note
        });
        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpPut]
    [Route("/plan-subtask/subsubtask/reject/{subsubtaskId:guid}")]
    [Authorize(Policy = PermissionNameList.PlanRead)]
    public async Task<IActionResult> Reject(Guid subsubtaskId, string note, double? progress)
    {
        await _mediator.Send(new RejectPlanSubsubtaskCommand
        {
            SubsubtaskId = subsubtaskId,
            Note = note,
            Progress = progress
        });
        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpPut]
    [Route("/plan-subtask/subsubtask/finalize")]
    [Authorize(Policy = PermissionNameList.PlanSubsubtaskFinalApproval)]
    public async Task<IActionResult> Finalize([BindBodySingleJson] FinalizePlanSubsubtaskCommand data)
    {
        await _mediator.Send(data);
        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }
}
