using System;
using Injaz.Core.Flow.Interfaces;
using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.RiskModel;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModulePlan;

public class DbContext : AppBaseDbContext, IFlowDbContext
{
    public DbContext(DbContextOptions<DbContext> options, IServiceProvider serviceProvider)
        : base(options, serviceProvider)
    {
    }

    public DbSet<Plan> Plans { get; set; }
    public DbSet<PlanTask> PlanTasks { get; set; }
    public DbSet<PlanSubtask> PlanSubtasks { get; set; }
    public DbSet<PlanSubsubtask> PlanSubsubtasks { get; set; }
    public DbSet<User> Users { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<DepartmentUserLink> DepartmentUserLinks { get; set; }
    public DbSet<FlowTransaction> FlowTransactions { get; set; }
    public DbSet<PlanResource> PlanResources { get; set; }
    public DbSet<PlanStrategicGoalLink> PlanStrategicGoalLinks { get; set; }
    public DbSet<PlanMinistryStrategicGoalLink> PlanMinistryStrategicGoalLinks { get; set; }
    public DbSet<PlanPlanInputLink> PlanInputLinks { get; set; }
    public DbSet<PlanPartnerLink> PlanPartnerLinks { get; set; }
    public DbSet<PlanPartneringDepartmentLink> PlanPartneringDepartmentLinks { get; set; }
    public DbSet<PlanOperationLink> PlanOperationLinks { get; set; }
    public DbSet<PlanKpiLink> PlanKpiLinks { get; set; }
    public DbSet<PlanTaskOperationLink> PlanTaskOperationLinks { get; set; }
    public DbSet<PlanTaskKpiLink> PlanTaskKpiLinks { get; set; }
    public DbSet<PlanSubsubtaskLibraryFileLink> PlanSubsubtaskLibraryFileLinks { get; set; }
    public DbSet<PlanSubsubtaskApproval> PlanSubsubtaskApprovals { get; set; }
    public DbSet<LibraryFile> LibraryFiles { get; set; }
    public DbSet<PlanInput> PlanInputs { get; set; }
    public DbSet<PlanCategory> PlanCategories { get; set; }
    public DbSet<PlanDependency> PlanDependencies { get; set; }
    public DbSet<PlanPolicyLink> PlanPolicyLinks { get; set; }
    public DbSet<AppSetting> AppSettings { get; set; }

    public DbSet<PlanPlanFuturePlanLink> PlanPlanFuturePlanLinks { get; set; }
    public DbSet<PlanFuturePlan> PlanFuturePlans { get; set; }
    public DbSet<Risk> Risks { get; set; }
}
