using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Injaz.Core.Dtos.PlanDtos.PlanCategories;
using Injaz.Core.Exceptions;
using Injaz.ModulePlan.Core.Queries.PlanCategories;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanCategories;

public class GetForEditByIdHandler : IRequestHandler<GetPlanCategoryForEditByIdQuery, PlanCategoryEditDto>
{
    private readonly DbContext _db;

    public GetForEditByIdHandler(DbContext db)
    {
        _db = db;
    }

    public Task<PlanCategoryEditDto> Handle(GetPlanCategoryForEditByIdQuery query, CancellationToken cancellationToken)
    {
        var item = _db.PlanCategories
            .Select(PlanCategoryEditDto.Mapper())
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
