using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.PlanDtos.PlanCategories;
using Injaz.ModulePlan.Core.Queries.PlanCategories;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanCategories;

public class GetListHandler : IRequestHandler<GetPlanCategoryListQuery, TableResultDto<PlanCategoryGetDto>>
{
    private readonly DbContext _db;

    public GetListHandler(
        DbContext db)
    {
        _db = db;
    }

    public Task<TableResultDto<PlanCategoryGetDto>> Handle(GetPlanCategoryListQuery query,
        CancellationToken cancellationToken)
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .PlanCategories
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return Task.FromResult(new TableResultDto<PlanCategoryGetDto>
        {
            Items = items
                .OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                .Skip(query.PageSize * query.PageNumber)
                .Take(query.PageSize)
                .Select(PlanCategoryGetDto.Mapper(lang))
                .ToList(),
            Count = _db.PlanCategories.Count(),
            FilteredCount = items.Count()
        });
    }
}
