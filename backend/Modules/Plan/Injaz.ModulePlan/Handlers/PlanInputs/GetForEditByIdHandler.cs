using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Injaz.Core.Dtos.PlanDtos.PlanInputs;
using Injaz.Core.Exceptions;
using Injaz.ModulePlan.Core.Queries.PlanInputs;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanInputs;

public class GetForEditByIdHandler : IRequestHandler<GetPlanInputForEditByIdQuery, PlanInputEditDto>
{
    private readonly DbContext _db;

    public GetForEditByIdHandler(DbContext db)
    {
        _db = db;
    }

    public Task<PlanInputEditDto> Handle(GetPlanInputForEditByIdQuery query, CancellationToken cancellationToken)
    {
        var item = _db.PlanInputs
            .Select(PlanInputEditDto.Mapper())
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
