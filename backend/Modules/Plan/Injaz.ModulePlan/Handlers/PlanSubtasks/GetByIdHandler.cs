using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Injaz.Core;
using Injaz.Core.Dtos.PlanDtos.PlanSubtasks;
using Injaz.Core.Exceptions;
using Injaz.Core.Services;
using Injaz.ModulePlan.Core.Queries.PlanSubtasks;
using Injaz.ModulePlan.Services.Plans;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanSubtasks;

public class GetByIdHandler : IRequestHandler<GetPlanSubtaskByIdQuery, PlanSubtaskGetDto>
{
    private readonly DbContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly PlanHelperService _planHelperService;
    private readonly PlanExpressionService _planExpressionService;

    public GetByIdHandler(
        DbContext db,
        CurrentUserService currentUserService,
        PlanHelperService planHelperService,
        PlanExpressionService planExpressionService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _planHelperService = planHelperService;
        _planExpressionService = planExpressionService;
    }

    public async Task<PlanSubtaskGetDto> Handle(GetPlanSubtaskByIdQuery query, CancellationToken cancellationToken)
    {
        var userId = _currentUserService.GetId();
        var lang = HelperFunctions.GetLanguageCode();

        var item = _planHelperService.GetSubtaskBaseItems(true)
            .Select(PlanSubtaskGetDto.Mapper(
                lang,
                userId,
                _db.Departments,
                await _planExpressionService.PlanAbility()
            ))
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return item;
    }
}
