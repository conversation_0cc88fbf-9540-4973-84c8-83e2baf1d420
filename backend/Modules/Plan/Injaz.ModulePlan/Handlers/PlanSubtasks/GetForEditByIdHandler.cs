using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Injaz.Core;
using Injaz.Core.Dtos.PlanDtos.PlanSubtasks;
using Injaz.Core.Exceptions;
using Injaz.ModulePlan.Core.Queries.PlanSubtasks;
using Injaz.ModulePlan.Services.Plans;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanSubtasks;

public class GetForEditByIdHandler : IRequestHandler<GetPlanSubtaskForEditByIdQuery, PlanSubtaskEditDto>
{
    private readonly PlanHelperService _planHelperService;

    public GetForEditByIdHandler(
        PlanHelperService planHelperService)
    {
        _planHelperService = planHelperService;
    }

    public Task<PlanSubtaskEditDto> Handle(GetPlanSubtaskForEditByIdQuery query,
        CancellationToken cancellationToken)
    {
        var item = _planHelperService.GetSubtaskBaseItems(true)
            .Select(PlanSubtaskEditDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
