using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.PlanDtos.PlanSubtasks;
using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.ModulePlan.Core.Queries.PlanSubtasks;
using Injaz.ModulePlan.Services.Plans;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanSubtasks;

public class GetListHandler : IRequestHandler<GetPlanSubtaskListQuery, TableResultDto<PlanSubtaskListDto>>
{
    private readonly DbContext _db;
    private readonly PlanHelperService _planHelperService;
    private readonly PlanExpressionService _planExpressionService;

    public GetListHandler(
        DbContext db,
        PlanHelperService planHelperService,
        PlanExpressionService planExpressionService)
    {
        _db = db;
        _planHelperService = planHelperService;
        _planExpressionService = planExpressionService;
    }

    public async Task<TableResultDto<PlanSubtaskListDto>> Handle(GetPlanSubtaskListQuery query,
        CancellationToken cancellationToken)
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";
        query.AssigneeType = query.AssigneeType?.ToLower().Trim() ?? "";

        var baseItems = _planHelperService.GetSubtaskBaseItems(true);

        if (query.ConsiderPlanFilterAsBase)
        {
            if (query.TaskIds is { Length: > 0 })
            {
                baseItems = baseItems.Where(x => query.TaskIds.Contains(x.TaskId));
            }

            if (query.PlanIds is { Length: > 0 })
            {
                baseItems = baseItems.Where(x => query.PlanIds.Contains(x.Task.PlanId));
            }
        }

        var items = baseItems
            .Where(x => (query.From == null || query.From <= x.From) && (query.To == null || x.To < query.To))
            .Where(x => x.NameAr.Contains(query.Keyword)
                        || x.NameEn.ToLower().Contains(query.Keyword)
            );

        if (!string.IsNullOrEmpty(query.AssigneeType))
        {
            items = query.AssigneeType switch
            {
                Plan.AssigneeTypeDepartment => items.Where(x =>
                    x.AssignedDepartmentId != null &&
                    (
                        !query.DepartmentIds.Any() ||
                        _db.Departments
                            .Where(y => query.DepartmentIds.Contains(y.Id))
                            .Any(y =>
                                query.IncludeChildDepartments
                                    ? x.AssignedDepartment.HierarchyCode.StartsWith(y.HierarchyCode)
                                    : x.AssignedDepartment.HierarchyCode == y.HierarchyCode
                            )
                    )
                ),
                Plan.AssigneeTypeTeam => items.Where(x =>
                    x.AssignedTeamId != null &&
                    (!query.TeamIds.Any() || query.TeamIds.Contains(x.AssignedTeamId!.Value))
                ),
                Plan.AssigneeTypeUser => items.Where(x =>
                    x.AssignedUserId != null &&
                    (!query.UserIds.Any() || query.UserIds.Contains(x.AssignedUserId!.Value))
                ),
                _ => items
            };
        }

        if (!query.ConsiderPlanFilterAsBase && query.TaskIds is { Length: > 0 })
        {
            items = items.Where(x => query.TaskIds.Contains(x.TaskId));
        }

        if (!query.ConsiderPlanFilterAsBase && query.PlanIds is { Length: > 0 })
        {
            items = items.Where(x => query.PlanIds.Contains(x.Task.PlanId));
        }

        var lang = HelperFunctions.GetLanguageCode();

        var filteredItems = items
            .Select(PlanSubtaskListDto.Mapper(
                lang,
                await _planExpressionService.PlanAbility()
            ));

        filteredItems = query.Status switch
        {
            "completed" => filteredItems.Where(x => x.IsApproved == true),
            "inProgress" => filteredItems.Where(x => x.IsApproved == false),
            _ => filteredItems
        };

        return new TableResultDto<PlanSubtaskListDto>
        {
            Items = filteredItems
                .ApplySort(query.OrderBy ?? "from descending")
                .Skip(query.PageSize * query.PageNumber)
                .Take(query.PageSize)
                .ToList(),
            Count = baseItems.Count(),
            FilteredCount = items.Count()
        };
    }
}
