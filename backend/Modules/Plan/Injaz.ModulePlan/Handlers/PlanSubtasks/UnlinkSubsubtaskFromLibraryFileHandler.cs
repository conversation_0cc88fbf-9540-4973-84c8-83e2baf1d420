using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Injaz.Core;
using Injaz.Core.Flow.Services;
using Injaz.Core.Permission;
using Injaz.Core.Services;
using Injaz.ModulePlan.Core.Commands.PlanSubtasks;
using MediatR;
using LinqKit;
using Injaz.Core.Exceptions;

namespace Injaz.ModulePlan.Handlers.PlanSubtasks;

public class UnlinkSubsubtaskFromLibraryFileHandler : IRequestHandler<UnlinkSubsubtaskFromLibraryFileCommand>
{
    private readonly DbContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly PermissionEnsurerService _permissionEnsurerService;

    public UnlinkSubsubtaskFromLibraryFileHandler(
        DbContext db,
        CurrentUserService currentUserService,
        PermissionEnsurerService permissionEnsurerService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _permissionEnsurerService = permissionEnsurerService;
    }

    public async Task<Unit> Handle(UnlinkSubsubtaskFromLibraryFileCommand command, CancellationToken cancellationToken)
    {
        // Ensure that the link exists.
        // Note: no unlinking unless the plan is initially approved.

        // Ensure that the user is involved with the plan.
        var userId = _currentUserService.GetId();
        var hasFullAccessPermission = await _permissionEnsurerService.Ensure(PermissionNameList.FullAccess);
        var isInvolvedWithExpression = HelperExpression.IsInvolvedWithPlanEntity(
            userId,
            _db.Departments,
            hasFullAccessPermission
        );

        var planSubtaskFileLibraryLink = _db
            .PlanSubsubtaskLibraryFileLinks
            .AsExpandable()
            .Where(x => x.PlanSubsubtask.Subtask.Task.Plan.FlowState == DefaultFlowState.ApprovedFinal)
            .Where(x => isInvolvedWithExpression.Invoke(x.PlanSubsubtask.Subtask.Task.Plan))
            .SingleOrDefault(x =>
                x.PlanSubsubtaskId == command.SubsubtaskId &&
                x.LibraryFileId == command.LibraryFileId
            );

        if (planSubtaskFileLibraryLink == null)
        {
            throw new ItemNotFoundException();
        }

        // Unlink 'em!
        _db.PlanSubsubtaskLibraryFileLinks.Remove(planSubtaskFileLibraryLink);
        _db.SaveChanges();

        return Unit.Value;
    }
}
