using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.PlanDtos.PlanTasks;
using Injaz.Core.Exceptions;
using Injaz.ModulePlan.Core.Queries.PlanTasks;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanTasks;

public class GetPeriodHandler : IRequestHandler<GetPlanTaskPeriodQuery, PlanTaskPeriodWithAssignedDto>
{
    private readonly DbContext _db;

    public GetPeriodHandler(
        DbContext db)
    {
        _db = db;
    }

    public Task<PlanTaskPeriodWithAssignedDto> Handle(GetPlanTaskPeriodQuery query,
        CancellationToken cancellationToken)
    {
        var item = _db
            .PlanTasks
            .AsExpandable()
            .Where(x => x.Id.Equals(query.Id))
            .Select(PlanTaskPeriodWithAssignedDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault();

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
