using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.PlanDtos.Plans;
using Injaz.Core.Extensions;
using Injaz.Core.Flow.Services;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModulePlan.Core.Queries.Plans;
using Injaz.ModulePlan.ExcelGenerators;
using Injaz.ModulePlan.Services.Plans;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModulePlan.Handlers.Plans;

public class ExportPlanListHandler : IRequestHandler<ExportPlanListQuery, byte[]>
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly DbContext _db;
    private readonly PlanHelperService _planHelperService;
    private readonly CurrentUserService _currentUserService;
    private readonly FlowService<Plan> _flowService;

    public ExportPlanListHandler(
        IStringLocalizer<SharedResource> localizer,
        DbContext db,
        PlanHelperService planHelperService,
        CurrentUserService currentUserService,
        FlowService<Plan> flowService)
    {
        _localizer = localizer;
        _db = db;
        _planHelperService = planHelperService;
        _currentUserService = currentUserService;
        _flowService = flowService;
    }

    public Task<byte[]> Handle(ExportPlanListQuery query, CancellationToken cancellationToken)
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";
        query.Initiatives = query.Initiatives?.ToLower().Trim() ?? "";
        query.AssigneeType = query.AssigneeType?.ToLower().Trim() ?? "";

        var baseItems = query.OnlyApprovable
            ? GetApprovablePlansForUser()
            : _planHelperService.GetBaseItems(true);

        var items = baseItems.Where(x =>
                (query.From == null || query.From <= x.From) && (query.To == null || x.To < query.To))
            .Where(x => x.NameAr.Contains(query.Keyword)
                        || x.NameEn.ToLower().Contains(query.Keyword)
            );

        if (!string.IsNullOrEmpty(query.Initiatives))
        {
            items = items.Where(x => x.Initiatives.ToLower().Contains(query.Initiatives));
        }

        if (query.Years is { Length: > 0 })
        {
            items = items.Where(x => query.Years.Contains(x.Year));
        }

        if (query.CategoryIds is { Length: > 0 })
        {
            items = items.Where(x => query.CategoryIds.Contains(x.CategoryId));
        }

        if (query.GovernmentStrategicGoalIds is { Length: > 0 })
        {
            items = items.Where(x =>
                x.GovernmentStrategicGoalId != null &&
                query.GovernmentStrategicGoalIds.Contains(x.GovernmentStrategicGoalId.Value));
        }

        if (query.KpiIds is { Length: > 0 })
        {
            items = items.Where(x =>
                x.KpiLinks.Any(y => query.KpiIds.Contains(y.KpiId))
            );
        }

        if (query.StrategicGoalIds is { Length: > 0 })
        {
            items = items.Where(x =>
                x.StrategicGoalLinks.Any(y =>
                    query.StrategicGoalIds.Contains(y.GoalId)));
        }

        if (query.PartnerIds is { Length: > 0 })
        {
            items = items.Where(x => x.PartnerLinks.Any(y => query.PartnerIds.Contains(y.PartnerId)));
        }

        if (!string.IsNullOrEmpty(query.Status))
        {
            items = items.Where(x => query.Status == "new" && x.FlowState != DefaultFlowState.ApprovedFinal ||
                                     query.Status == "approved" && x.FlowState == DefaultFlowState.ApprovedFinal);
        }

        if (!string.IsNullOrEmpty(query.ProgressStatus))
        {
            var progressExpression = HelperExpression.PlanProgress();
            items = items.Where(x =>
                query.ProgressStatus == "completed"
                    ? (
                        progressExpression.Invoke(x) != null &&
                        Math.Abs(progressExpression.Invoke(x).Value - 1.0) < 1e-5
                    )
                    : progressExpression.Invoke(x) == null || progressExpression.Invoke(x) < 1.0
            );
        }

        if (!string.IsNullOrEmpty(query.AssigneeType))
        {
            items = query.AssigneeType switch
            {
                Plan.AssigneeTypeDepartment => items.Where(x =>
                    x.AssignedDepartmentId != null &&
                    (
                        !query.DepartmentIds.Any() ||
                        _db.Departments
                            .Where(y => query.DepartmentIds.Contains(y.Id))
                            .Any(y =>
                                query.IncludeChildDepartments
                                    ? x.AssignedDepartment.HierarchyCode.StartsWith(y.HierarchyCode)
                                    : x.AssignedDepartment.HierarchyCode == y.HierarchyCode
                            )
                    )
                ),
                Plan.AssigneeTypeTeam => items.Where(x =>
                    x.AssignedTeamId != null &&
                    (!query.TeamIds.Any() || query.TeamIds.Contains(x.AssignedTeamId!.Value))
                ),
                Plan.AssigneeTypeUser => items.Where(x =>
                    x.AssignedUserId != null &&
                    (!query.UserIds.Any() || query.UserIds.Contains(x.AssignedUserId!.Value))
                ),
                _ => items
            };
        }

        var lang = HelperFunctions.GetLanguageCode();


        var appSetting = _db.AppSettings
            .Single();


        var itemsToExport = items
            .Select(PlanListExportDto.Mapper(
                lang
            ))
            .ApplySort(query.OrderBy ?? "from descending")
            .ToList();

        var generator = new PlanListReportExcelGenerator(
            itemsToExport,
            _localizer,
            appSetting
        );

        return Task.FromResult(generator.Generate());
    }

    private IQueryable<Plan> GetApprovablePlansForUser()
    {
        var userId = _currentUserService.GetId();
        var canReject = _flowService.Check(DefaultFlowState.RejectedFinal);
        var canRejectFinal = _flowService.Check(DefaultFlowState.RejectedFinal);
        var canApprove = _flowService.Check(DefaultFlowState.Approved);
        var canApproveImmediate = _flowService.Check(DefaultFlowState.ApprovedImmediate);
        var canApproveFinal = _flowService.Check(DefaultFlowState.ApprovedFinal);

        return _db
            .Plans
            .AsExpandable()
            .Where(x => canReject.Invoke(x, userId) ||
                        canRejectFinal.Invoke(x, userId) ||
                        canApprove.Invoke(x, userId) ||
                        canApproveImmediate.Invoke(x, userId) ||
                        canApproveFinal.Invoke(x, userId)
            );
    }
}
