using System;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.PlanDtos.Plans;
using Injaz.Core.Flow.Services;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Services;

namespace Injaz.ModulePlan.Services.Plans;

/// <summary>
/// A service to handle the creation of **COMPLEX**
/// plan-related expressions.
/// </summary>
public class PlanExpressionService
{
    private readonly CurrentUserService _currentUserService;
    private readonly DbContext _db;
    private readonly PermissionEnsurerService _permissionEnsurerService;

    public PlanExpressionService(
        CurrentUserService currentUserService,
        DbContext db,
        PermissionEnsurerService permissionEnsurerService
    )
    {
        _currentUserService = currentUserService;
        _db = db;
        _permissionEnsurerService = permissionEnsurerService;
    }

    public async Task<Expression<Func<Plan, PlanUserAbility>>> PlanAbility(
        bool respectHierarchy = true
    )
    {
        var userId = _currentUserService.GetId();
        var departments = _db.Departments.AsQueryable();

        // Checks if the user is involved with a single
        // plan entity + full access on database side.
        var isInvolved = HelperExpression.IsInvolvedWithPlanEntity(
            userId,
            departments,
            respectHierarchy
        );

        // Checks if the user is involved with a plan
        // entity or its children (tasks and subtasks).
        Expression<Func<Plan, bool>> isInvolvedWithPlan =
            item => isInvolved.Invoke(item)
                    || item.Tasks.Any(y => isInvolved.Invoke(item))
                    || item.Tasks.SelectMany(y => y.Subtasks).Any(y => isInvolved.Invoke(y));


        // Checks if the plan is editable (or deletable)
        // given the current state of the plan. (overrides
        // plan state if the user has full access).
        var hasFullAccessPermission = await _permissionEnsurerService.Ensure(PermissionNameList.FullAccess);
        Expression<Func<Plan, bool>> canEditOrDelete =
            item => item.FlowState == DefaultFlowState.Draft
                    || item.FlowState == DefaultFlowState.Rejected
                    || item.FlowState == DefaultFlowState.RejectedFinal;

        var hasPlanWritePermission = await _permissionEnsurerService.Ensure(PermissionNameList.PlanWrite);
        Expression<Func<Plan, bool>> canEdit = item => hasFullAccessPermission || (
            hasPlanWritePermission && canEditOrDelete.Invoke(item)
        );

        var hasPlanDeletePermission = await _permissionEnsurerService.Ensure(PermissionNameList.PlanDelete);
        Expression<Func<Plan, bool>> canDelete = item => hasFullAccessPermission || (
            hasPlanDeletePermission && canEditOrDelete.Invoke(item)
        );


        return item => new PlanUserAbility
        {
            CanEdit = isInvolvedWithPlan.Invoke(item) && canEdit.Invoke(item),
            CanDelete = isInvolvedWithPlan.Invoke(item) && canDelete.Invoke(item),
        };
    }
}
