using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Models;

namespace Injaz.ModuleSearch.Core.Services.SearchSources;

public class LibraryFileSearchSource : ISearchSource
{
    private readonly AppDataContext _appDataContext;

    public LibraryFileSearchSource(AppDataContext appDataContext)
    {
        _appDataContext = appDataContext;
    }

    public async Task<IQueryable<SearchableItemDto>> GetItemsAsync()
    {
        var lang = HelperFunctions.GetLanguageCode();
        return await Task.FromResult(
            _appDataContext
                .LibraryFiles
                .AsExpandable()
                .Select(item => new SearchableItemDto
                {
                    Id = item.Id,
                    Type = "evidence",
                    CreationTime = item.CreationTime,
                    Name = Convert.ToString(SupportedCultures.LanguageArabic == lang ? item.NameAr : item.NameEn),
                    Description = "",
                    Metadata =
                        "{"
                        + "\"" + "hasFile" + "\"" + ":"
                        + "\"" + (item.FileName == null ? "false" : "true") + "\"" + ","
                        + "\"" + "contentType" + "\"" + ":"
                        + "\"" + (item.ContentType ?? "") + "\"" + ","
                        + "\"" + "link" + "\"" + ":"
                        + "\"" + (item.Link ?? "") + "\""
                        + "}",
                })
        );
    }
}