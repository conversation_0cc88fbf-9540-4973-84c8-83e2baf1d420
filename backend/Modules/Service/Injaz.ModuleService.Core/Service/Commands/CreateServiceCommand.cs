using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Service;
using Injaz.Core.Dtos.ServiceCategory;
using Injaz.Core.Dtos.ServiceClientCategory;
using Injaz.Core.Dtos.ServiceDeliveryChannel;
using Injaz.Core.Dtos.ServicePartner;
using Injaz.Core.Dtos.ServiceProviderChannel;
using Injaz.Core.ValidationAttributes;
using MediatR;
using ServiceModel = Injaz.Core.Models.DomainClasses.App.ServiceModel.Service;

namespace Injaz.ModuleService.Core.Service.Commands;

public class CreateServiceCommand : IRequest<ServiceSimpleDto>
{
    [Display(Name = "ownership")]
    [RegularExpression("(?:"
                       + ServiceModel.OwnershipFederal + "|"
                       + ServiceModel.OwnershipLocal + ")")]
    public string Ownership { get; set; }

    [Display(Name = "main_service_name_ar")]
    [Required(ErrorMessage = "0_is_required")]
    public string MainServiceNameAr { get; set; }

    [Display(Name = "main_service_name_en")]
    public string MainServiceNameEn { get; set; }

    [Display(Name = "sub_service_name_ar")]
    public string SubServiceNameAr { get; set; }

    [Display(Name = "sub_service_name_en")]
    public string SubServiceNameEn { get; set; }

    [Display(Name = "supplementary_service_name_ar")]
    public string SupplementaryServiceNameAr { get; set; }

    [Display(Name = "supplementary_service_name_en")]
    public string SupplementaryServiceNameEn { get; set; }

    [Display(Name = "categories")] public IEnumerable<ServiceCategorySimpleDto> Categories { get; set; }
    [Display(Name = "description_ar")] public string DescriptionAr { get; set; }
    [Display(Name = "description_en")] public string DescriptionEn { get; set; }
    [Display(Name = "limitation")] public string Limitation { get; set; }
    [Display(Name = "is_there_a_package")] public bool IsThereAPackage { get; set; }
    [Display(Name = "package")] public string Package { get; set; }

    [Display(Name = "types")]
    [ItemsInList(ServiceModel.TypeGB, ServiceModel.TypeGC, ServiceModel.TypeGG)]
    public IEnumerable<string> Types { get; set; }

    [Display(Name = "delivery_time")] public string DeliveryTime { get; set; }

    [Display(Name = "payment_methods")]
    [ItemsInList(ServiceModel.PaymentCash, ServiceModel.PaymentElectronic)]
    public IEnumerable<string> PaymentMethods { get; set; }

    [Display(Name = "duration")] public string Duration { get; set; }

    [Display(Name = "is_electronically_convertible")]
    public bool IsElectronicallyConvertible { get; set; }

    [Display(Name = "electronic_transformation_ratio")]
    [RegularExpression("(?:"
                       + ServiceModel.TransformationComplete + "|"
                       + ServiceModel.TransformationPartial + ")")]
    public string ElectronicTransformationRatio { get; set; }

    [Display(Name = "individual_service_insurance")]
    public string IndividualServiceInsurance { get; set; }

    [Display(Name = "business_service_insurance")]
    public string BusinessServiceInsurance { get; set; }

    [Display(Name = "government_service_insurance")]
    public string GovernmentServiceInsurance { get; set; }

    [Display(Name = "procedures")] public string Procedures { get; set; }
    [Display(Name = "query_stage")] public string QueryStage { get; set; }
    [Display(Name = "application_stage")] public string ApplicationStage { get; set; }
    [Display(Name = "delivery_stage")] public string DeliveryStage { get; set; }

    [Display(Name = "receipt_of_the_request")]
    public string ReceiptOfTheRequest { get; set; }

    [Display(Name = "is_linked_with_other_parties")]
    public bool IsLinkedWithOtherParties { get; set; }

    [Display(Name = "linked_party_name")] public string LinkedPartyName { get; set; }

    [Display(Name = "linked_party_service")]
    public string LinkedPartyService { get; set; }

    [Display(Name = "post_evaluation_score")]
    public decimal? PostEvaluationScore { get; set; }

    [Display(Name = "evaluation_notes")] public string EvaluationNotes { get; set; }

    [Display(Name = "development")] public string Development { get; set; }

    [Display(Name = "development_entrances")]
    [ItemsInList(ServiceModel.EntranceComplaint,
        ServiceModel.EntranceGovernmentalOrientation,
        ServiceModel.EntranceProcessDevelopment,
        ServiceModel.EntranceReconnaissance,
        ServiceModel.EntranceSecretShopper,
        ServiceModel.EntranceSuggestion,
        ServiceModel.EntranceOther)]
    public IEnumerable<string> DevelopmentEntrances { get; set; }

    [Display(Name = "proactive")]
    [RegularExpression("(?:"
                       + ServiceModel.ProactiveAmenable + "|"
                       + ServiceModel.ProactiveNotAmenable + "|"
                       + ServiceModel.ProactiveApprovedPackage + ")")]
    public string Proactive { get; set; }

    [Display(Name = "proactive_standards")]
    [ItemsInList(ServiceModel.ProactiveStandardTime,
        ServiceModel.ProactiveStandardStatusChange,
        ServiceModel.ProactiveStandardInterdependence,
        ServiceModel.ProactiveStandardChangesInLaws,
        ServiceModel.ProactiveStandardRegulations,
        ServiceModel.ProactiveStandardSpatial
    )]
    public IEnumerable<string> ProactiveStandards { get; set; }

    [Display(Name = "development_effect")] public string DevelopmentEffect { get; set; }

    [Display(Name = "impact_on_quality_of_life")]
    public string ImpactOnQualityOfLife { get; set; }

    [Display(Name = "is_active")] public bool IsActive { get; set; }
    [Display(Name = "canceling_reason")] public string CancelingReason { get; set; }

    [Display(Name = "duration_type")]
    [RegularExpression("(?:"
                       + ServiceModel.DurationTypeSecond + "|"
                       + ServiceModel.DurationTypeMinute + "|"
                       + ServiceModel.DurationTypeHour + "|"
                       + ServiceModel.DurationTypeDay + "|"
                       + ServiceModel.DurationTypeMonth + ")", ErrorMessage = "0_is_invalid")]
    public string DurationType { get; set; }

    [Display(Name = "owning_department")] public DepartmentSimpleDto OwningDepartment { get; set; }
    [Display(Name = "fees")] public IEnumerable<ServiceFeeDto>? Fees { get; set; }
    public IEnumerable<DepartmentSimpleDto>? Departments { get; set; }
    public ICollection<ServicePartnerCreateDto>? Partners { get; set; }
    public ICollection<ServiceProviderChannelSimpleDto>? ProviderChannels { get; set; }
    public ICollection<ServiceDeliveryChannelSimpleDto>? DeliveryChannels { get; set; }
    public ICollection<ServiceClientCategorySimpleDto>? ClientCategories { get; set; }
}
