using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.Service;
using MediatR;

namespace Injaz.ModuleService.Core.Service.Queries;

public class GetServiceListQuery : IRequest<TableResultDto<ServiceListDto>>, IRequest<byte[]>
{
    public string Keyword { get; set; }

    public string[]? Ownerships { get; set; }

    public string[]? ElectronicTransformationRatios { get; set; }

    public string[]? Types { get; set; }

    public Guid[]? CategoryIds { get; set; }

    public Guid[]? ClientCategoryIds { get; set; }

    public Guid[]? ProviderChannelIds { get; set; }
    public Guid[]? OwningDepartmentIds { get; set; }
    public Guid[]? DeliveryChannelIds { get; set; }

    public string[]? PaymentMethods { get; set; }

    public string[]? DevelopmentEntrances { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
