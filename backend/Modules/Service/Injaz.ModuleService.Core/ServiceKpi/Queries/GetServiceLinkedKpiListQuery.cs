using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.Misc;
using MediatR;

namespace Injaz.ModuleService.Core.ServiceKpi.Queries;

public class GetServiceLinkedKpiListQuery : IRequest<TableResultDto<KpiWithResultDto>>
{
    public Guid Id { get; set; }
    public string Keyword { get; set; }
    public int? ResultFromYear { get; set; }
    public int? ResultToYear { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
}
