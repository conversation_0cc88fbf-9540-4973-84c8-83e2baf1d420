using Injaz.Core.Models.Abstraction;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Injaz.Core.Models.DomainClasses.App.ServiceModel;
using Microsoft.EntityFrameworkCore;
using ServiceModel = Injaz.Core.Models.DomainClasses.App.ServiceModel.Service;
using ServiceProviderChannelModel = Injaz.Core.Models.DomainClasses.App.ServiceModel.ServiceProviderChannel;
using ServiceClientCategoryModel = Injaz.Core.Models.DomainClasses.App.ServiceModel.ServiceClientCategory;
using ServiceDeliveryChannelModel = Injaz.Core.Models.DomainClasses.App.ServiceModel.ServiceDeliveryChannel;

namespace Injaz.ModuleService;

public class DbContext : AppBaseDbContext
{
    public DbContext(DbContextOptions<DbContext> options, IServiceProvider serviceProvider)
        : base(options, serviceProvider)
    {
    }

    public DbSet<ServiceModel> Services { get; set; }
    public DbSet<Kpi> Kpis { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<ServicePartnerLink> ServicePartnerLinks { get; set; }
    public DbSet<ServiceDepartmentLink> ServiceDepartmentLinks { get; set; }
    public DbSet<ServiceCategoryLink> ServiceCategoryLinks { get; set; }
    public DbSet<ServiceServiceClientCategoryLink> ServiceServiceClientCategoryLinks { get; set; }
    public DbSet<ServiceServiceDeliveryChannelLink> ServiceServiceDeliveryChannelLinks { get; set; }
    public DbSet<ServiceServiceProviderChannelLink> ServiceServiceProviderChannelLinks { get; set; }
    public DbSet<ServiceProviderChannelModel> ServiceProviderChannels { get; set; }
    public DbSet<ServiceClientCategoryModel> ServiceClientCategories { get; set; }
    public DbSet<ServiceDeliveryChannelModel> ServiceDeliveryChannels { get; set; }

    public DbSet<ServiceKpiLink> ServiceKpiLinks { get; set; }
    public DbSet<EvaluationInstance> EvaluationInstances { get; set; }
    public DbSet<User> Users { get; set; }
}
