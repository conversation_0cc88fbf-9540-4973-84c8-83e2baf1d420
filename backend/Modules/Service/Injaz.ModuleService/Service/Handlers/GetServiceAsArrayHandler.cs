using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Service;
using Injaz.ModuleService.Core.Service.Queries;
using MediatR;

namespace Injaz.ModuleService.Service.Handlers;

public class GetServiceAsArrayHandler :
    IRequestHandler<GetServiceAsListQuery, List<ServiceForCapacityDemandDto>>
{
    private readonly DbContext _db;

    public GetServiceAsArrayHandler(DbContext db) => _db = db;

    public async Task<List<ServiceForCapacityDemandDto>> Handle(
        GetServiceAsListQuery query,
        CancellationToken cancellationToken
    )
    {
        await  Task.CompletedTask;
        var items = _db.Services.AsExpandable();

        var lang = HelperFunctions.GetLanguageCode();

        var itemsList = items
            .OrderBy(x =>
                lang == SupportedCultures.LanguageArabic
                    ? (x.SubServiceNameAr ?? x.SupplementaryServiceNameAr ?? x.MainServiceNameAr)
                    : (x.SubServiceNameEn ?? x.SupplementaryServiceNameEn ?? x.MainServiceNameEn)
            )
            .Select(ServiceForCapacityDemandDto.Mapper(lang))
            .Distinct()
            .ToList();
        return itemsList;
    }
}
