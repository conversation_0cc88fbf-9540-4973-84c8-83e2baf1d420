using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Service;
using Injaz.Core.Exceptions;
using Injaz.ModuleService.Core.Service.Queries;
using MediatR;

namespace Injaz.ModuleService.Service.Handlers;

public class GetServiceByIdHandler :
    IRequestHandler<GetServiceByIdQuery, ServiceGetDto>
{
    private readonly DbContext _db;

    public GetServiceByIdHandler(DbContext db) => _db = db;

    public Task<ServiceGetDto> Handle(
        GetServiceByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .Services
            .AsExpandable()
            .Select(ServiceGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
