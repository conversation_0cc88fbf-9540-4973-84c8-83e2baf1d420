using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.Service;
using Injaz.Core.Models.SqlFunctions;
using Injaz.ModuleService.Core.Service.Queries;
using MediatR;
using Newtonsoft.Json;

namespace Injaz.ModuleService.Service.Handlers;

public class GetServiceListHandler :
    IRequestHandler<GetServiceListQuery, TableResultDto<ServiceListDto>>
{
    private readonly DbContext _db;

    public GetServiceListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<ServiceListDto>> Handle(
        GetServiceListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db.Services.AsExpandable();

        if (!string.IsNullOrEmpty(query.Keyword))
        {
            items = items.Where(x =>
                x.MainServiceNameAr.ToLower().Contains(query.Keyword) ||
                x.MainServiceNameEn.ToLower().Contains(query.Keyword) ||
                x.SubServiceNameAr.ToLower().Contains(query.Keyword) ||
                x.SubServiceNameEn.ToLower().Contains(query.Keyword) ||
                x.SupplementaryServiceNameAr.ToLower().Contains(query.Keyword) ||
                x.SupplementaryServiceNameEn.ToLower().Contains(query.Keyword)
            );
        }

        if (query.Ownerships != null && query.Ownerships.Length != 0)
        {
            items = items.Where(x => query.Ownerships.Contains(x.Ownership));
        }

        if (query.ElectronicTransformationRatios != null && query.ElectronicTransformationRatios.Length != 0)
        {
            items = items.Where(x => query.ElectronicTransformationRatios.Contains(x.ElectronicTransformationRatio));
        }

        if (query.Types is { Length: > 0 })
        {
            items = items.Where(x =>
                ContainsAtLeastOneSqlFunction.Call(x.Types, JsonConvert.SerializeObject(query.Types))
            );
        }

        if (query.CategoryIds is { Length: > 0 })
        {
            items = items.Where(x => x.CategoryLinks.Any(c => query.CategoryIds.Contains(c.CategoryId)));
        }

        if (query.ClientCategoryIds is { Length: > 0 })
        {
            items = items.Where(x => x
                .ServiceClientCategoryLinks
                .Any(c => query.ClientCategoryIds.Contains(c.ServiceClientCategoryId))
            );
        }

        if (query.ProviderChannelIds is { Length: > 0 })
        {
            items = items.Where(x => x
                .ServiceProviderChannelLinks
                .Any(c => query.ProviderChannelIds.Contains(c.ServiceProviderChannelId))
            );
        }

        if (query.DeliveryChannelIds is { Length: > 0 })
        {
            items = items.Where(x => x
                .ServiceDeliveryChannelLinks
                .Any(c => query.DeliveryChannelIds.Contains(c.ServiceDeliveryChannelId))
            );
        }

        if (query.OwningDepartmentIds is { Length: > 0 })
        {
            items = items.Where(x =>
                x.OwningDepartmentId != null && query.OwningDepartmentIds.Contains(x.OwningDepartmentId!.Value));
        }

        if (query.PaymentMethods is { Length: > 0 })
        {
            items = items.Where(x => ContainsAtLeastOneSqlFunction.Call(x.PaymentMethods,
                JsonConvert.SerializeObject(query.PaymentMethods))
            );
        }

        if (query.DevelopmentEntrances is { Length: > 0 })
        {
            items = items.Where(x => ContainsAtLeastOneSqlFunction.Call(x.DevelopmentEntrances,
                JsonConvert.SerializeObject(query.DevelopmentEntrances))
            );
        }

        var lang = HelperFunctions.GetLanguageCode();

        return Task.FromResult(
            new TableResultDto<ServiceListDto>
            {
                Items = items
                    .OrderBy(x =>
                        lang == SupportedCultures.LanguageArabic
                            ? (x.SubServiceNameAr ?? x.SupplementaryServiceNameAr ?? x.MainServiceNameAr)
                            : (x.SubServiceNameEn ?? x.SupplementaryServiceNameEn ?? x.MainServiceNameEn)
                    )
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .Select(ServiceListDto.Mapper(lang))
                    .ToList(),
                Count = _db.Services.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
