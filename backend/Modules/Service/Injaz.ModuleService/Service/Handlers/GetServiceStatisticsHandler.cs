using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc.StatisticsGroup;
using Injaz.Core.Dtos.ServiceCategory;
using Injaz.Core.Dtos.ServiceClientCategory;
using Injaz.Core.Dtos.ServiceProviderChannel;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleService.Core.Constants;
using Injaz.ModuleService.Core.Service.Queries;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleService.Service.Handlers;

public class GetServiceStatisticsHandler : IRequestHandler<GetServiceStatisticsQuery, StatisticsGroup[]>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public GetServiceStatisticsHandler(DbContext db, IStringLocalizer<SharedResource> localizer)
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<StatisticsGroup[]> Handle(
        GetServiceStatisticsQuery request,
        CancellationToken cancellationToken
    )
    {
        var items = _db.Services.AsExpandable().AsQueryable();

        var lang = HelperFunctions.GetLanguageCode();

        var serviceCategoryExpression = ServiceCategorySimpleDto.Mapper(lang);
        var serviceClientCategoryExpression = ServiceClientCategorySimpleDto.Mapper(lang);
        var serviceProviderChannelExpression = ServiceProviderChannelSimpleDto.Mapper(lang);

        return Task.FromResult(
            new StatisticsGroup[]
            {
                new()
                {
                    Title = _localizer["general_statistics"],
                    Items = new StatisticsGroupItem[]
                    {
                        new()
                        {
                            Label = _localizer["the_total"],
                            Type = "service-all",
                            Value = items.Count()
                        },
                        new()
                        {
                            Label = _localizer["active_services"],
                            Type = "service-active",
                            Value = items.Count(x => x.IsActive == 1)
                        },
                        new()
                        {
                            Label = _localizer["inactive_services"],
                            Type = "service-inactive",
                            Value = items.Count(x => x.IsActive == 0)
                        },
                    },
                },
                new()
                {
                    Title = _localizer["service_ownership_statistics"],
                    Items = items
                        .GroupBy(x => x.Ownership)
                        .Select(x => new { Id = x.Key, Count = x.Count() })
                        .AsEnumerable()
                        .Select(x => new StatisticsGroupItem
                        {
                            Id = x.Id,
                            Label = _localizer[
                                ServiceOwnerships
                                    .List
                                    .First(y => y.Id == x.Id)
                                    .Name
                            ],
                            Type = "service-ownership",
                            Value = x.Count
                        }),
                },

                // by service category
                new()
                {
                    Title = _localizer["service_category_statistics"],
                    Items = items
                        .SelectMany(x => x.CategoryLinks.Select(y => y.Category))
                        .Select(x => serviceCategoryExpression.Invoke(x))
                        .GroupBy(x => x)
                        .Select(x => new StatisticsGroupItem
                        {
                            Id = x.Key.Id.ToString().ToLower(),
                            Label = x.Key.Name,
                            Type = "service-category",
                            Value = x.Count()
                        })
                },

                new()
                {
                    Title = _localizer["service_type_statistics"],
                    Items = items
                        .AsEnumerable()
                        .SelectMany(x => x.Types)
                        .GroupBy(x => x)
                        .Select(x => new StatisticsGroupItem
                        {
                            Id = x.Key,
                            Label = x.Key,
                            Type = "service-type",
                            Value = x.Count()
                        })
                },

                // by client categories
                new()
                {
                    Title = _localizer["client_category_statistics"],
                    Items = items
                        .SelectMany(x => x.ServiceClientCategoryLinks.Select(y => y.ServiceClientCategory))
                        .Select(x => serviceClientCategoryExpression.Invoke(x))
                        .GroupBy(x => x)
                        .Select(x => new StatisticsGroupItem
                        {
                            Id = x.Key.Id.ToString().ToLower(),
                            Label = x.Key.Name,
                            Type = "client-category",
                            Value = x.Count()
                        })
                },

                // by provider channel
                new()
                {
                    Title = _localizer["provider_channel_statistics"],
                    Items = items
                        .SelectMany(x => x.ServiceProviderChannelLinks.Select(y => y.ServiceProviderChannel))
                        .Select(x => serviceProviderChannelExpression.Invoke(x))
                        .GroupBy(x => x)
                        .Select(x => new StatisticsGroupItem
                        {
                            Id = x.Key.Id.ToString().ToLower(),
                            Label = x.Key.Name,
                            Type = "provider-channel",
                            Value = x.Count()
                        })
                }
            }
        );
    }
}
