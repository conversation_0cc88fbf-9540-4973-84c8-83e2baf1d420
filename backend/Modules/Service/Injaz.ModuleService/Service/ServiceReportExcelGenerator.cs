using System.Drawing;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Service;
using Injaz.Core.ExcelGenerators.Misc;
using Injaz.Core.Resources.Shared;
using Microsoft.Extensions.Localization;
using OfficeOpenXml.Style;

namespace Injaz.ModuleService.Service;

public class ServiceReportExcelGenerator : IDisposable
{
    private readonly List<ServiceExportDto> _services;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly BaseExcelFile _file;
    private const int ColumnCount = 15; // Reduced number of columns
    private readonly int _totalRows;

    public ServiceReportExcelGenerator(
        List<ServiceExportDto> services,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _services = services;
        _localizer = localizer;
        _totalRows = 1 + _services.Count;
        _file = new BaseExcelFile();
    }

    public byte[] Generate()
    {
        if (HelperFunctions.GetLanguageCode() == SupportedCultures.LanguageArabic)
        {
            _file.SetRtl();
        }

        // Set border for the entire content, font size
        // font name, and vertical alignment to center.
        _file
            .Select(0, 0, _totalRows, ColumnCount)
            .SetBorder(ExcelBorderStyle.Thin)
            .SetVerticalAlignment(ExcelVerticalAlignment.Center)
            .SetFontSize(16)
            .SetFontName("Sakkal Majalla")
            .Deselect();

        // Set column widths - reduced to match ColumnCount
        _file.SetColumnWidth(50).MoveRight() // Main Service Name
            .SetColumnWidth(50).MoveRight() // Sub Service Name
            .SetColumnWidth(50).MoveRight() // Supplementary Service Name
            .SetColumnWidth(80).MoveRight() // Description
            .SetColumnWidth(35).MoveRight() // Service Ownership
            .SetColumnWidth(40).MoveRight() // Owning Department
            .SetColumnWidth(50).MoveRight() // Service Types
            .SetColumnWidth(50).MoveRight() // Categories
            .SetColumnWidth(50).MoveRight() // Client Categories
            .SetColumnWidth(50).MoveRight() // Provider Channels
            .SetColumnWidth(50).MoveRight() // Delivery Channels
            .SetColumnWidth(40).MoveRight() // Payment Methods
            .SetColumnWidth(35).MoveRight() // Electronic Transformation Ratio
            .SetColumnWidth(40).MoveRight() // Creation Date
            .SetColumnWidth(40) // Last Modification Date
            .Move(0, 0);

        // Center alignment for content
        _file.Select(0, 0, _totalRows, ColumnCount)
            .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
            .Move(0, 0);

        // Set rows heights for the entire document
        Enumerable.Range(0, _totalRows).ToList().ForEach(_ => _file.SetRowHeight(35).MoveDown());

        _file.Move(0, 0);

        PopulateHeader();
        PopulateData();

        _file.Move(0, 0);

        return _file.GetBytes();
    }

    private void PopulateHeader()
    {
        _file.Select(
                0,
                0,
                1,
                ColumnCount
            )
            .SetBold(true)
            .SetPatternType(ExcelFillStyle.Solid)
            .SetBackgroundColor(Color.FromArgb(150, 180, 214))
            .SetBold(true)
            .Deselect();

        int columnIndex = 0; // Keep track of the current column index

        // Only add columns up to the ColumnCount limit
        if (columnIndex < ColumnCount)
        {
            _file.SetValue(_localizer["main_service_name"].Value);
            _file.MoveRight();
            columnIndex++;
        }

        _file.SetValue(_localizer["sub_service_name"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["supplementary_service_name"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["service_description"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["service_ownership"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["owning_department"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["service_types"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["categories"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["client_categories"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["provider_channels"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["delivery_channels"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["payment_methods"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["delivery_time"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["duration"].Value);
        _file.MoveRight();
        columnIndex++;

        _file.SetValue(_localizer["duration_type"].Value);
        _file.MoveRight();
        columnIndex++;

        if (columnIndex < ColumnCount)
        {
            _file.SetValue(_localizer["proactive_amenable"].Value);
            _file.MoveRight();
            columnIndex++;
        }

        if (columnIndex < ColumnCount)
        {
            _file.SetValue(_localizer["is_active"].Value);
            _file.MoveRight();
            columnIndex++;
        }

        _file.MoveDown();
        _file.MoveLeft(columnIndex);
    }

    private void PopulateData()
    {
        var lang = HelperFunctions.GetLanguageCode();

        _services.ToList().ForEach(service =>
        {
            int columnIndex = 0; // Keep track of current column index

            // Main Service Name
            _file.SetValue(lang == SupportedCultures.LanguageArabic
                ? service.MainServiceNameAr
                : service.MainServiceNameEn);
            _file.MoveRight();
            columnIndex++;

            // Sub Service Name
            _file.SetValue(lang == SupportedCultures.LanguageArabic
                ? service.SubServiceNameAr
                : service.SubServiceNameEn);
            _file.MoveRight();
            columnIndex++;

            // Supplementary Service Name
            _file.SetValue(lang == SupportedCultures.LanguageArabic
                ? service.SupplementaryServiceNameAr
                : service.SupplementaryServiceNameEn);
            _file.MoveRight();
            columnIndex++;

            // Description
            _file.SetValue(lang == SupportedCultures.LanguageArabic
                ? service.DescriptionAr
                : service.DescriptionEn);
            _file.MoveRight();
            columnIndex++;

            // Ownership
            _file.SetValue(_localizer[service.Ownership]);
            _file.MoveRight();
            columnIndex++;

            // Owning Department
            _file.SetValue(service.OwningDepartment?.Name ?? "");
            _file.MoveRight();
            columnIndex++;

            // Service Types
            _file.SetValue(service.Types != null && service.Types.Length > 0
                ? string.Join(" - ", service.Types.Select(type => _localizer[type]))
                : "");
            _file.MoveRight();
            columnIndex++;

            // Categories
            _file.SetValue(service.Categories != null && service.Categories.Count > 0
                ? string.Join(" - ", service.Categories.Select(cat => cat.Name))
                : "");
            _file.MoveRight();
            columnIndex++;

            // Client Categories
            _file.SetValue(service.ClientCategories != null && service.ClientCategories.Count > 0
                ? string.Join(" - ", service.ClientCategories.Select(cat => cat.Name))
                : "");
            _file.MoveRight();
            columnIndex++;

            // Provider Channels
            _file.SetValue(service.ProviderChannels != null && service.ProviderChannels.Count > 0
                ? string.Join(" - ", service.ProviderChannels.Select(ch => ch.Name))
                : "");
            _file.MoveRight();
            columnIndex++;

            // Delivery Channels
            _file.SetValue(service.DeliveryChannels != null && service.DeliveryChannels.Count > 0
                ? string.Join(" - ", service.DeliveryChannels.Select(ch => ch.Name))
                : "");
            _file.MoveRight();
            columnIndex++;

            // Payment Methods
            _file.SetValue(service.PaymentMethods != null && service.PaymentMethods.Length > 0
                ? string.Join(" - ", service.PaymentMethods.Select(method => _localizer[method]))
                : "");
            _file.MoveRight();
            columnIndex++;

            // Delivery Time
            _file.SetValue(service.DeliveryTime);
            _file.MoveRight();
            columnIndex++;

            // Duration
            _file.SetValue(service.Duration);
            _file.MoveRight();
            columnIndex++;

            // Duration Type
            _file.SetValue(!string.IsNullOrEmpty(service.DurationType)
                ? _localizer[service.DurationType]
                : "");
            _file.MoveRight();
            columnIndex++;


            _file.MoveDown();
            _file.MoveLeft(columnIndex);
        });
    }

    public void Dispose()
    {
        _file.Dispose();
    }
}
