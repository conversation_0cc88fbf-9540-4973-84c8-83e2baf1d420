using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.ServiceClientCategory;
using Injaz.Core.Exceptions;
using Injaz.ModuleService.Core.ServiceClientCategory.Queries;
using MediatR;

namespace Injaz.ModuleService.ServiceClientCategory.Handlers;

public class GetServiceClientCategoryByIdHandler : IRequestHandler<GetServiceClientCategoryByIdQuery,
    ServiceClientCategoryGetDto>
{
    private readonly DbContext _db;

    public GetServiceClientCategoryByIdHandler(DbContext db) => _db = db;

    public Task<ServiceClientCategoryGetDto> Handle(
        GetServiceClientCategoryByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .ServiceClientCategories
            .AsExpandable()
            .Select(ServiceClientCategoryGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
