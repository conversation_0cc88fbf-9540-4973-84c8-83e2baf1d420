using LinqKit;
using Injaz.Core.Dtos.ServiceDeliveryChannel;
using Injaz.Core.Exceptions;
using Injaz.ModuleService.Core.ServiceDeliveryChannel.Queries;
using MediatR;

namespace Injaz.ModuleService.ServiceDeliveryChannel.Handlers;

public class GetServiceDeliveryChannelForEditByIdHandler :
    IRequestHandler<GetServiceDeliveryChannelForEditByIdQuery, ServiceDeliveryChannelEditDto>
{
    private readonly DbContext _db;

    public GetServiceDeliveryChannelForEditByIdHandler(DbContext db) => _db = db;

    public Task<ServiceDeliveryChannelEditDto> Handle(
        GetServiceDeliveryChannelForEditByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .ServiceDeliveryChannels
            .AsExpandable()
            .Select(ServiceDeliveryChannelEditDto.Mapper())
            .FirstOrDefault(x => x.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
