using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Evaluate.Services;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Services;
using Injaz.ModuleService.Core.ServiceKpi.Queries;
using MediatR;

namespace Injaz.ModuleService.ServiceKpi.Handlers;

public class GetServiceUnlinkedKpisHandler :
    IRequestHandler<GetServiceUnlinkedKpiListQuery, TableResultDto<KpiListDto>>
{
    private readonly DbContext _db;
    private readonly AppSettingService _appSettingService;
    private readonly EvaluationService<KpiResultPeriod> _kpiResultPeriodEvaluationService;
    private readonly PermissionEnsurerService _permissionEnsurerService;

    public GetServiceUnlinkedKpisHandler(
        DbContext db,
        AppSettingService appSettingService,
        EvaluationService<KpiResultPeriod> kpiResultPeriodEvaluationService,
        PermissionEnsurerService permissionEnsurerService
    )
    {
        _db = db;
        _appSettingService = appSettingService;
        _kpiResultPeriodEvaluationService = kpiResultPeriodEvaluationService;
        _permissionEnsurerService = permissionEnsurerService;
    }

    public async Task<TableResultDto<KpiListDto>> Handle(
        GetServiceUnlinkedKpiListQuery query,
        CancellationToken cancellationToken
    )
    {
        var keyword = query.Keyword;
        var kpiNumber = query.Number;

        keyword = keyword?.Trim().ToLower() ?? "";
        kpiNumber = kpiNumber?.ToLower().Trim() ?? "";

        var items = _db
            .Kpis
            .AsExpandable()
            .Where(x => x.ServiceLinks.All(y => !y.ServiceId.Equals(query.Id)));

        var count = items.Count();

        items = items.Where(x =>
            x.NameAr.ToLower().Contains(keyword) ||
            x.NameEn.ToLower().Contains(keyword)
        );

        if (!string.IsNullOrEmpty(kpiNumber))
        {
            items = items.Where(x => x.Code.Contains(kpiNumber));
        }

        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;
        var scoreDetailsExpression = _kpiResultPeriodEvaluationService.GetScoreDetailExpression();
        var evaluateActionAbilityAction =
            _kpiResultPeriodEvaluationService.GetEvaluateActionAbilityForCurrentUserExpression(_db.Departments);
        var scoreDetailsPeriodExpression = _kpiResultPeriodEvaluationService.GetScoreDetailExpression();
        var evaluateActionAbilityPeriodExpression =
            _kpiResultPeriodEvaluationService.GetEvaluateActionAbilityForCurrentUserExpression(_db.Departments);
        var hasEvaluationExportPermission =
            await _permissionEnsurerService.Ensure(PermissionNameList.KpiResultPeriodExportEvaluation);

        return new TableResultDto<KpiListDto>
        {
            Items = items
                .Where(x => x.Status == Kpi.StatusActive)
                .Select(KpiListDto.Mapper(
                    HelperFunctions.GetLanguageCode(),
                    canAchievedBeNegative,
                    hasEvaluationExportPermission,
                    scoreDetailsExpression,
                    evaluateActionAbilityAction,
                    scoreDetailsPeriodExpression,
                    evaluateActionAbilityPeriodExpression,
                    _db.EvaluationInstances.AsExpandable(),
                    _db.Users.AsExpandable()
                ))
                .OrderBy(x => x.Code.Length)
                .ThenBy(x => x.Code)
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize),
            Count = count,
            FilteredCount = items.Count()
        };
    }
}
