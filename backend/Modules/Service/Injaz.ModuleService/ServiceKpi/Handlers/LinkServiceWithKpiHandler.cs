using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App.ServiceModel;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleService.Core.ServiceKpi.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleService.ServiceKpi.Handlers;

public class LinkServiceWithKpiHandler : IRequestHandler<LinkServiceWithKpiCommand>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public LinkServiceWithKpiHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(LinkServiceWithKpiCommand command, CancellationToken cancellationToken)
    {
        var item = _db.ServiceKpiLinks.Find(command.Id, command.KpiId);

        if (item != null)
        {
            throw new GenericException { Messages = new string[] { _localizer["service_is_already_linked_to_kpi"] } };
        }

        var service = _db.Services.Find(command.Id);

        if (service == null)
        {
            throw new GenericException { Messages = new string[] { _localizer["service_does_not_exist"] } };
        }

        _db.ServiceKpiLinks.Add(new ServiceKpiLink { ServiceId = command.Id, KpiId = command.KpiId });
        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
