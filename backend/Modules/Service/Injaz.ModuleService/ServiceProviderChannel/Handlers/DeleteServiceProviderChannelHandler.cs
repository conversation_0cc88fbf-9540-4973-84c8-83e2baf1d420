using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleService.Core.ServiceProviderChannel.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleService.ServiceProviderChannel.Handlers;

public class DeleteServiceProviderChannelHandler : IRequestHandler<DeleteServiceProviderChannelCommand>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteServiceProviderChannelHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(DeleteServiceProviderChannelCommand command, CancellationToken cancellationToken)
    {
        var data = _db
            .ServiceProviderChannels
            .Where(x => x.Id == command.Id)
            .Select(x => new { Item = x, IsLinkedToOtherResources = x.ServiceLinks.Any() })
            .FirstOrDefault();

        if (data == null) throw new ItemNotFoundException();

        // Ensure that the item does not have any links under it.
        if (data.IsLinkedToOtherResources)
            throw new GenericException
                { Messages = new string[] { _localizer["cannot_delete_item_there_are_items_under_it"] } };

        _db.ServiceProviderChannels.Remove(data.Item);

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
