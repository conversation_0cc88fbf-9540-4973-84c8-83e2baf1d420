using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.Setting;
using Injaz.Core.Misc;
using Injaz.Core.Models.Misc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleSetting.Controllers;

public partial class SettingController
{
    [HttpPut]
    [Route("/setting/dashboard-top-items")]
    public async Task<IActionResult> UpdateDashboardTopItems(
        [BindBodySingleJson] DashboardTopItemsCreateDto dashboardTopItem
    )
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        var isMissingRequired =
            string.IsNullOrEmpty(dashboardTopItem.TitleAr) ||
            string.IsNullOrEmpty(dashboardTopItem.TitleEn) ||
            dashboardTopItem.Items.Any(x =>
                string.IsNullOrEmpty(x.LabelAr) ||
                string.IsNullOrEmpty(x.LabelEn) ||
                string.IsNullOrEmpty(x.IconClass) ||
                (string.IsNullOrEmpty(x.DescriptionAr) && (x.TagsAr == null || x.TagsAr.Length == 0) ||
                 string.IsNullOrEmpty(x.DescriptionEn) && (x.TagsEn == null || x.TagsEn.Length == 0))
            );

        if (isMissingRequired)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["missing_required_fields"] }
            );
        }

        var item = _appDataContext.AppSettings.Single();

        item.DashboardTopItem = new DashboardTopItem
        {
            TitleAr = dashboardTopItem.TitleAr,
            TitleEn = dashboardTopItem.TitleEn,
            Items = dashboardTopItem.Items.Select(y => new DashboardTopSubItem
            {
                LabelAr = y.LabelAr,
                LabelEn = y.LabelEn,
                IconClass = y.IconClass,
                DescriptionAr = y.DescriptionAr,
                DescriptionEn = y.DescriptionEn,
                TagsAr = y.TagsAr.ToArray(),
                TagsEn = y.TagsEn.ToArray()
            }).ToList()
        };


        _appDataContext.SaveChanges();

        // Notify clients of a setting change.
        await _defaultHubContext.Clients.All.Message(new { Type = MessageTypeList.AppSettingUpdate });

        return this.GetResponseObject(extra: _appDataContext
            .AppSettings
            .Select(DashboardTopItemsEditDto.Mapper())
            .Single());
    }

    [HttpPut]
    [Route("/setting/allowed-file-types")]
    public async Task<IActionResult> UpdateAllowedFileTypes(
        [BindBodySingleJson] AllowedFileTypesCreateDto allowedFileTypes
    )
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        var isMissingRequired = allowedFileTypes.Types.Any(x => string.IsNullOrEmpty(x.ToString()));

        if (isMissingRequired)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["missing_required_fields"] }
            );
        }

        var item = _appDataContext.AppSettings.Single();

        item.AllowedFileTypes = allowedFileTypes.Types.ToArray();

        _appDataContext.SaveChanges();

        // Notify clients of a setting change.
        await _defaultHubContext.Clients.All.Message(new { Type = MessageTypeList.AppSettingUpdate });

        return this.GetResponseObject(extra: _appDataContext
            .AppSettings
            .Select(AllowedFileTypesDto.Mapper())
            .Single()
        );
    }

    [HttpGet("/setting/allowed-file-types")]
    public IActionResult GetAllowedFileTypes()
    {
        var item = _appDataContext
            .AppSettings
            .Select(AllowedFileTypesDto.Mapper())
            .Single();

        return this.GetResponseObject(extra: item);
    }

    [HttpPut]
    [Route("/setting/general")]
    public async Task<IActionResult> UpdateAppSettingGeneral([BindBodySingleJson] AppSettingGeneralCreateDto data)
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        var item = _appDataContext.AppSettings.Single();

        item.AppId = data.AppId.ToUpper().Trim();
        item.AppNameAr = data.AppNameAr.Trim();
        item.AppNameEn = data.AppNameEn?.Trim() ?? item.AppNameAr;
        item.AppUrl = data.AppUrl?.Trim();
        item.AppEmailDomain = data.AppEmailDomain?.Trim();
        _appDataContext.SaveChanges();

        // Notify clients of a setting change.
        await _defaultHubContext.Clients.All.Message(new
        {
            Type = MessageTypeList.AppSettingUpdate,
        });


        return this.GetResponseObject(extra: _appDataContext
            .AppSettings
            .Select(AppSettingGeneralDto.Mapper(HelperFunctions.GetLanguageCode()))
            .Single());
    }

    [HttpGet]
    [Route("/setting/general")]
    public IActionResult GetAppSettingGeneral()
    {
        var item = _appDataContext
            .AppSettings
            .Select(AppSettingGeneralCreateDto.Mapper())
            .Single();

        return this.GetResponseObject(extra: item);
    }

    [HttpGet("/setting/dashboard-top-items")]
    public IActionResult GetAppSettingDashboard()
    {
        var item = _appDataContext
            .AppSettings
            .Select(DashboardTopItemsEditDto.Mapper())
            .Single();

        return this.GetResponseObject(extra: item);
    }


    [HttpPut]
    [Route("/setting/logo")]
    public async Task<IActionResult> UpdateAppLogo(
        [BindBodySingleJson] AppSettingLogoDto data
    )
    {
        async Task DeleteCurrentFile(string fileName)
        {
            try
            {
                await _fileManager.DeleteAsync($"{Directories.AppSettings}/{fileName}");
            }
            catch
            {
                // ignore
            }
        }

        async Task UploadImage(
            byte[]? image,
            string fileName,
            bool reset
        )
        {
            if (image != null)
            {
                // Try and upload the file.
                await using var ms = new MemoryStream(image);
                var contentType = await _fileTypeDetectorService.GetContentTypeAsync(ms);
                ms.Seek(0, SeekOrigin.Begin);

                await _fileManager.PutAsync(ms, Directories.AppSettings, fileName, contentType);
            }
            else if (reset)
            {
                await DeleteCurrentFile(fileName);
            }
        }

        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Update the images
        await UploadImage(data.Logo, App.AppLogoFileName, data.ResetLogo);
        await UploadImage(data.LoginLogo, App.AppLoginLogoFileName, data.ResetLoginLogo);
        await UploadImage(data.LoginBackground, App.AppLoginBackground, data.ResetLoginBackground);


        _appDataContext.SaveChanges();

        await _defaultHubContext.Clients.All.Message(new
        {
            Type = MessageTypeList.AppImageUpdate
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpGet]
    [Route("/setting/logo")]
    [AllowAnonymous]
    public async Task<IActionResult> GetAppLogo()
    {
        return await GetImage(App.AppLogoFileName, Defaults.AppLogo);
    }

    [HttpGet]
    [Route("/setting/login-logo")]
    [AllowAnonymous]
    public async Task<IActionResult> GetAppLoginLogo()
    {
        return await GetImage(App.AppLoginLogoFileName, Defaults.AppLoginLogo);
    }

    [HttpGet]
    [Route("/setting/login-background")]
    [AllowAnonymous]
    public async Task<IActionResult> GetAppLoginBackground()
    {
        return await GetImage(App.AppLoginBackground, Defaults.AppLoginBackground);
    }

    private async Task<IActionResult> GetImage(string filename, string defaultImage)
    {
        Stream stream;
        try
        {
            stream = await _fileManager.GetAsync($"{Directories.AppSettings}/{filename}");
        }
        catch
        {
            stream = new MemoryStream(Convert.FromBase64String(defaultImage));
        }

        return new FileStreamResult(stream, "image/png");
    }

    [HttpPut]
    [Route("/setting/theme")]
    public async Task<IActionResult> UpdateTheme(string primary, string secondary)
    {
        var setting = _appDataContext.AppSettings.Single();

        // Validate colors.
        if (
            !HelperFunctions.IsValidHexColor(primary) ||
            !HelperFunctions.IsValidHexColor(secondary)
        )
        {
            return this.GetResponseObject(
                0,
                statusCode: 423,
                messages: new string[] { _localizer["invalid_colors"] }
            );
        }

        primary = string.IsNullOrEmpty(primary) ? Defaults.AppPrimaryColor : primary;
        secondary = string.IsNullOrEmpty(secondary) ? Defaults.AppSecondaryColor : secondary;

        setting.AppPrimaryColor = primary;
        setting.AppSecondaryColor = secondary;

        _appDataContext.SaveChanges();

        // Notify clients of a setting change.
        await _defaultHubContext.Clients.All.Message(new
        {
            Type = MessageTypeList.AppSettingUpdate,
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpGet]
    [Route("/setting/theme")]
    public IActionResult GetTheme()
    {
        var setting = _appDataContext.AppSettings.Single();

        var primary = string.IsNullOrEmpty(setting.AppPrimaryColor)
            ? Defaults.AppPrimaryColor
            : setting.AppPrimaryColor;
        var secondary = string.IsNullOrEmpty(setting.AppPrimaryColor)
            ? Defaults.AppSecondaryColor
            : setting.AppSecondaryColor;

        return this.GetResponseObject(
            extra: new ThemeSettingDto
            {
                Primary = TailwindColorGenerator.Generate(primary),
                Secondary = TailwindColorGenerator.Generate(secondary),
            }
        );
    }
}
