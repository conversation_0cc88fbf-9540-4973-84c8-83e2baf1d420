using LinqKit;
using Injaz.Core.Dtos.Setting;
using Injaz.Core.Models;
using Injaz.ModuleSetting.Core.Commands.Queries;
using MediatR;

namespace Injaz.ModuleSetting.Handlers.DashboardSetting;

public class GetDashboardStyle1SettingHandler
    : IRequestHandler<GetDashboardStyle1SettingQuery, DashboardStyle1SettingDto>
{
    private readonly AppDataContext _appDataContext;

    public GetDashboardStyle1SettingHandler(
        AppDataContext appDataContext)
    {
        _appDataContext = appDataContext;
    }

    public async Task<DashboardStyle1SettingDto> Handle(
        GetDashboardStyle1SettingQuery query,
        CancellationToken cancellationToken)
    {
        await Task.CompletedTask;
        return _appDataContext
            .AppSettings
            .AsExpandable()
            .Select(x => x.DashboardSetting.Style1)
            .AsEnumerable()
            .Select(DashboardStyle1SettingDto.Mapper())
            .Single();
    }
}
