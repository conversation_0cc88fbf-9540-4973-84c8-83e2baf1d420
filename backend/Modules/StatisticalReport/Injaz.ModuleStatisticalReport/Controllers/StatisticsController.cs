using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;
using Injaz.ModuleStatisticalReport.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Identity;

namespace Injaz.ModuleStatisticalReport.Controllers;

[Route("statistical-report/statistics")]
[Authorize(Policy = PermissionNameList.StatisticalReportRead)]
public class StatisticsController : Controller
{
    private readonly IMediator _mediator;
    private readonly UserManager<User> _userManager;

    public StatisticsController(
        UserManager<User> userManager,
        IMediator mediator
    )
    {
        _mediator = mediator;
        _userManager = userManager;

    }

    [HttpGet("general")]
    public async Task<IActionResult> General()
    {
        var userId = new Guid(_userManager.GetUserId(User));
        var hasStatisticalReportPermission = await this.EnsureUserHasPermission(PermissionNameList.StatisticalReport);
        var hasStatisticalReportPublishPermission = await this.EnsureUserHasPermission(PermissionNameList.StatisticalReportPublish);
        var hasFullAccessPermission = await this.EnsureUserHasPermission(PermissionNameList.FullAccess);


        return this.GetResponseObject(extra: await _mediator.Send(new GetStatisticalReportStatisticsQuery
        {
            CurrentUserId = userId,
            CurrentUserHasStatisticalReportPermission = hasStatisticalReportPermission,
            CurrentUserHasStatisticalReportPublishPermission = hasStatisticalReportPublishPermission,
            CurrentUserHasFullAccessPermission = hasFullAccessPermission

        }));
    }
}
