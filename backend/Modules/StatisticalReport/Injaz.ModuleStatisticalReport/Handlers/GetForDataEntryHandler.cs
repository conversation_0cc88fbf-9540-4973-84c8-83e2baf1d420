using Injaz.Core.Dtos.StatisticalReport;
using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleStatisticalReport.Core.Commands;
using Injaz.ModuleStatisticalReport.Core.Queries;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleStatisticalReport.Handlers;

public class
    GetForDataEntryHandler : IRequestHandler<GetStatisticalReportForDataEntryQuery, StatisticalReportForDataEntryDto>
{
    private readonly IRepository _repo;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IMediator _mediator;

    public GetForDataEntryHandler(
        IRepository repo,
        IStringLocalizer<SharedResource> localizer,
        IMediator mediator
    )
    {
        _repo = repo;
        _localizer = localizer;
        _mediator = mediator;
    }

    public async Task<StatisticalReportForDataEntryDto> Handle(
        GetStatisticalReportForDataEntryQuery query,
        CancellationToken cancellationToken
    )
    {
        var report = _repo.FindById(query.Id);

        if (report == null) throw new ItemNotFoundException();

        // Ensure that no data entry when the report is
        // not published yet.
        if (report.IsPublished == 0)
        {
            throw new GenericException { Messages = new string[] { _localizer["report_is_not_published_yet"] } };
        }

        await _mediator.Send(new FillMissingStatisticalReportResultsCommand { Id = query.Id }, cancellationToken);

        // Return the results for data entry:
        return _repo.GetForDataEntryById(query.Id, query.CurrentUserId, query.CurrentUserHasFullAccessPermission);
    }
}
