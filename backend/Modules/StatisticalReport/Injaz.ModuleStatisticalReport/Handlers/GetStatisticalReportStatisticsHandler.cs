using System.Linq.Expressions;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Misc.StatisticsGroup;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModuleStatisticalReport.Core.Queries;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleStatisticalReport.Handlers;

public class GetStatisticalReportStatisticsHandler :
    IRequestHandler<GetStatisticalReportStatisticsQuery, StatisticsGroup[]>
{
    private readonly IDbContext _dbContext;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly YearService _yearService;

    public GetStatisticalReportStatisticsHandler(
        IDbContext dbContext,
        IStringLocalizer<SharedResource> localizer,
        YearService yearService)
    {
        _dbContext = dbContext;
        _localizer = localizer;
        _yearService = yearService;
    }

    public Task<StatisticsGroup[]> Handle(
        GetStatisticalReportStatisticsQuery request,
        CancellationToken cancellationToken
    )
    {
        var currentYear = _yearService.Value;
        bool hasAnyRequiredPermission = request.CurrentUserHasStatisticalReportPermission ||
                                        request.CurrentUserHasStatisticalReportPublishPermission;

        IQueryable<StatisticalReport> items;
        var userDepartments = new List<Guid>();


        if (request.CurrentUserHasFullAccessPermission)
        {
            // For full access, show all reports
            items = _dbContext.StatisticalReports.AsExpandable().AsQueryable();
        }
        else
        {
            // Get the hierarchy codes for the user's departments
            var userDepartmentHierarchyCodes = _dbContext.DepartmentUserLinks
                .Where(x => x.UserId == request.CurrentUserId)
                .Select(x => x.Department.HierarchyCode)
                .ToList();

            // Create a predicate to filter departments based on hierarchy codes
            Expression<Func<Department, bool>> departmentPredicate = x => false;
            userDepartmentHierarchyCodes.ForEach(code =>
                departmentPredicate = departmentPredicate.Or(y => y.HierarchyCode.StartsWith(code)));

            // Get department IDs that match the hierarchy code predicate
             userDepartments = _dbContext.Departments
                .Where(departmentPredicate)
                .Select(x => x.Id)
                .ToList();

             items = _dbContext.StatisticalReports
                .Where(report => report.Categories
                    .Any(category => category.DepartmentLinks
                        .Any(link => userDepartments.Contains(link.DepartmentId))))
                .AsExpandable().AsQueryable();
        }

        var isLockedExpression = HelperExpression.IsStatisticalReportLocked();
        var lang = HelperFunctions.GetLanguageCode();

        var generalStatistics = new StatisticsGroup
        {
            Title = _localizer["general_statistics"],
            Items = new[]
            {
                new StatisticsGroupItem
                {
                    Label = _localizer["the_total"],
                    Type = "statistical-report-all",
                    Value = hasAnyRequiredPermission
                        ? items.Count()
                        : items.Count(x => x.IsPublished == 1)
                },
                new StatisticsGroupItem
                {
                    Label = _localizer["active_statistical_reports"],
                    Type = "statistical-report-active",
                    Value = items.Count(x => x.IsPublished == 1 && !isLockedExpression.Invoke(x))
                },
                new StatisticsGroupItem
                {
                    Label = _localizer["locked_statistical_reports"],
                    Type = "statistical-report-locked",
                    Value = items.Count(x => x.IsPublished == 1 && isLockedExpression.Invoke(x))
                },
            }
        };

        if (hasAnyRequiredPermission)
        {
            generalStatistics.Items = generalStatistics.Items.Append(new StatisticsGroupItem
            {
                Label = _localizer["not_published_statistical_reports"],
                Type = "statistical-report-not-published",
                Value = items.Count(x => x.IsPublished == 0)
            });
        }

        var statisticalReports = (request.CurrentUserHasFullAccessPermission
                ? _dbContext.StatisticalReports
                : _dbContext.StatisticalReports
                    .Where(report => report.Categories
                        .Any(category => category.DepartmentLinks
                            .Any(link => userDepartments.Contains(link.DepartmentId)))))

            .Where(x => (request.CurrentUserHasStatisticalReportPermission ||
                         request.CurrentUserHasStatisticalReportPublishPermission) || x.IsPublished == 1)
            .Where(x => x.InitialYear <= currentYear)
            .Select(x => new
            {
                Report = x,
                CurrentYearValue = _dbContext.StatisticalReportCategoryResults
                    .Where(y => y.CategoryDepartmentLink.Category.ReportId == x.Id && y.Year == currentYear)
                    .Select(y => y.Value)
                    .Sum() ?? 0,
                LastYearValue = _dbContext.StatisticalReportCategoryResults
                    .Where(y => y.CategoryDepartmentLink.Category.ReportId == x.Id && y.Year == currentYear - 1)
                    .Select(y => y.Value)
                    .Sum()
            })
            .Select(result => new StatisticsGroupItem
            {
                Id = result.Report.Id.ToString().ToLower(),
                Label = (lang == SupportedCultures.LanguageArabic ? result.Report.NameAr : result.Report.NameEn) +
                        " - " + _localizer[result.Report.Cycle],
                Type = "statistical-report",
                Value = result.CurrentYearValue,
                ArrowDirection = (result.LastYearValue == null || result.LastYearValue == 0)
                    ? "both"
                    : result.CurrentYearValue < result.LastYearValue
                        ? "down"
                        : "up",
                ArrowColor = (result.LastYearValue == null || result.LastYearValue == 0)
                    ? null
                    : result.Report.IsDecreaseBest == 1
                        ? (result.CurrentYearValue < result.LastYearValue ? "success" : "danger")
                        : (result.CurrentYearValue > result.LastYearValue ? "success" : "danger"),
                ArrowValue = (result.LastYearValue == null || result.LastYearValue == 0)
                    ? null
                    : Math.Round(
                        (double)((result.CurrentYearValue - result.LastYearValue) / result.LastYearValue * 100),
                        2
                    ) + "%"
            })
            .ToList();


        var response = new[]
        {
            generalStatistics,
            new StatisticsGroup
            {
                Title = _localizer["statistical_reports"],
                Items = statisticalReports
            }
        };

        return Task.FromResult(response);
    }
}
