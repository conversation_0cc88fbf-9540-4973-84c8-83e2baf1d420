using Injaz.ModuleStatisticalReport.Core.Commands;
using MediatR;

namespace Injaz.ModuleStatisticalReport.Handlers;

public class PublishHandler : IRequestHandler<PublishStatisticalReportCommand>
{
    private readonly IRepository _repo;
    private readonly IMediator _mediator;

    public PublishHandler(
        IRepository repo,
        IMediator mediator
    )
    {
        _repo = repo;
        _mediator = mediator;
    }

    public async Task<Unit> Handle(PublishStatisticalReportCommand command, CancellationToken cancellationToken)
    {
        var item = _repo.FindById(command.Id);

        item.IsPublished = 1;

        _repo.UpdateItems(new[] { item });

        _repo.Commit();

        await _mediator.Send(new FillMissingStatisticalReportResultsCommand
        {
            Id = command.Id
        }, cancellationToken);

        return Unit.Value;
    }
}
