using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.StatisticalReport;
using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.Core.Services.FileManager;
using Injaz.ModuleStatisticalReport.Core.Commands.StatisticalReportCategoryResultAttachment;
using MediatR;
using Microsoft.Extensions.Localization;
using StatisticalReportCategoryResultAttachmentModel =
    Injaz.Core.Models.DomainClasses.App.StatisticalReportCategoryResultAttachment;

namespace Injaz.ModuleStatisticalReport.Handlers.StatisticalReportCategoryResultAttachment;

public class CreateStatisticalReportCategoryResultAttachmentHandler :
    IRequestHandler<CreateStatisticalReportCategoryResultAttachmentCommand,
        StatisticalReportCategoryResultAttachmentListDto>
{
    private readonly IDbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly FileTypeDetectorService _fileTypeDetectorService;
    private readonly IFileManager _fileManager;

    public CreateStatisticalReportCategoryResultAttachmentHandler(
        IDbContext db,
        IStringLocalizer<SharedResource> localizer,
        FileTypeDetectorService fileTypeDetectorService,
        IFileManager fileManager
    )
    {
        _db = db;
        _localizer = localizer;
        _fileTypeDetectorService = fileTypeDetectorService;
        _fileManager = fileManager;
    }

    public async Task<StatisticalReportCategoryResultAttachmentListDto> Handle(
        CreateStatisticalReportCategoryResultAttachmentCommand command,
        CancellationToken cancellationToken
    )
    {
        var data = _db
            .StatisticalReportCategoryResults
            .AsExpandable()
            .SingleOrDefault(x => x.Id == command.ResultId);

        if (data == null)
        {
            throw new ItemNotFoundException();
        }

        // Ensure that all results are unlocked.
        if (data.IsLocked == 1)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["some_or_all_results_are_currently_locked"] }
            };
        }

        // Get the content type of the file.
        await using var ms = new MemoryStream(command.File);
        var contentType = await _fileTypeDetectorService.GetContentTypeAsync(ms);
        ms.Seek(0, SeekOrigin.Begin);

        // Try and upload the file.
        var fileName = Guid.NewGuid().ToString();
        var result = await _fileManager.PutAsync(
            ms,
            Directories.StatisticalReportCategoryResultAttachments,
            fileName,
            contentType
        );

        if (!result.Succeeded)
        {
            throw new GenericException { Messages = new[] { result.ErrorMessage } };
        }

        var item = new StatisticalReportCategoryResultAttachmentModel
        {
            NameAr = command.NameAr,
            NameEn = command.NameEn,
            FileName = fileName,
            ContentType = contentType,
            StatisticalReportCategoryResultId = command.ResultId,
        };

        await _db.StatisticalReportCategoryResultAttachments.AddAsync(item, cancellationToken);

        _db.SaveChanges();

        return _db
            .StatisticalReportCategoryResultAttachments
            .Select(StatisticalReportCategoryResultAttachmentListDto.Mapper(HelperFunctions.GetLanguageCode()))
            .First(x => x.Id.Equals(item.Id));
    }
}
