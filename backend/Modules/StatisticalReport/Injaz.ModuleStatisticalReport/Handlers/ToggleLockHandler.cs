using Injaz.Core.Models.DomainClasses.App;
using Injaz.ModuleStatisticalReport.Core.Commands;
using MediatR;

namespace Injaz.ModuleStatisticalReport.Handlers;

public class ToggleLockHandler : IRequestHandler<ToggleLockStatisticalReportCommand>
{
    private readonly IRepository _repo;
    private readonly IMediator _mediator;

    public ToggleLockHandler(
        IRepository repo,
        IMediator mediator
    )
    {
        _repo = repo;
        _mediator = mediator;
    }

    public async Task<Unit> Handle(ToggleLockStatisticalReportCommand command, CancellationToken cancellationToken)
    {
        await _mediator.Send(new FillMissingStatisticalReportResultsCommand
        {
            Id = command.Id
        }, cancellationToken);

        var item = _repo.FindById(command.Id);

        var results = item
            .Categories
            .SelectMany(x => x.DepartmentLinks)
            .SelectMany(x => x.Results)
            .Where(x => command.ResultId == null || x.Id == command.ResultId)
            .Where(x => command.CategoryId == null ||
                        CheckIfParent(
                            x.CategoryDepartmentLink.CategoryId,
                            command.CategoryId.Value,
                            item.Categories.ToList()
                        )
            )
            .Where(x => command.Year == null || command.Year == x.Year)
            .Where(x => command.Period == null || command.Period == x.Period)
            .ToList();

        results.ForEach(x => x.IsLocked = command.Lock ? 1 : 0);

        _repo.UpdateResults(results);

        _repo.Commit();

        return Unit.Value;
    }

    private bool CheckIfParent(Guid? categoryId, Guid parentId, IList<StatisticalReportCategory> reportCategories)
    {
        if (categoryId == parentId) return true;
        if (categoryId == null) return false;

        categoryId = reportCategories.Single(x => x.Id == categoryId).ParentId;
        return CheckIfParent(categoryId, parentId, reportCategories);
    }
}
