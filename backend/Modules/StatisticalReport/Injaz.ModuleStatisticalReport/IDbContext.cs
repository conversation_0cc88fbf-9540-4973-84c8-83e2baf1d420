using Injaz.Core.Models.DomainClasses.App;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace Injaz.ModuleStatisticalReport;

public interface IDbContext
{
    int SaveChanges();

    void RemoveRange(IEnumerable<object> entities);

    EntityEntry<TEntity> Add<TEntity>(TEntity entity) where TEntity : class;

    void Dispose();

    DbSet<StatisticalReport> StatisticalReports { get; set; }
    DbSet<StatisticalReportCategory> StatisticalReportCategories { get; set; }
    DbSet<StatisticalReportCategoryDepartmentLink> StatisticalReportCategoryDepartmentLinks { get; set; }
    DbSet<StatisticalReportCategoryResult> StatisticalReportCategoryResults { get; set; }
    DbSet<StatisticalReportCategoryResultAttachment> StatisticalReportCategoryResultAttachments { get; set; }
    DbSet<DepartmentUserLink> DepartmentUserLinks { get; set; }
    DbSet<Department> Departments { get; set; }
    DbSet<User> Users { get; set; }

}
