using System.Linq.Expressions;
using LinqKit;
using Injaz.Core;
using Injaz.Core.DtoMappingStrategies;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.StatisticalReport;
using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.SqlFunctions;
using Injaz.Core.Permission;
using Injaz.Core.Services;
using Microsoft.EntityFrameworkCore;


namespace Injaz.ModuleStatisticalReport;

public class Repository : IRepository
{
    private readonly IDbContext _db;
    private readonly CurrentUserService _currentUserService;

    public Repository(
        IDbContext db,
        CurrentUserService currentUserService
    )
    {
        _db = db;
        _currentUserService = currentUserService;
    }

    public StatisticalReport FindById(Guid id)
    {
        return _db
            .StatisticalReports
            .Include(x => x.Categories)
            .ThenInclude(x => x.DepartmentLinks)
            .ThenInclude(x => x.Results)
            .SingleOrDefault(x => x.Id == id);
    }

    public TableResultDto<StatisticalReportListDto> GetList(
        string keyword,
        Guid[]? departmentIds,
        int pageNumber,
        int pageSize,
        int? initialYear,
        string[]? cycles,
        string? status,
        Guid currentUserId,
        bool currentUserHasStatisticalReportPermission,
        bool currentUserHasStatisticalReportLockAndUnlockPermission,
        bool currentUserHasStatisticalReportPublishPermission,
        bool currentUserHasStatisticalReportWritePermission,
        bool currentUserHasStatisticalReportDataEntryPermission,
        bool currentUserHasFullAccessPermission,
        string? orderBy
    )
    {
        var isLockedExpression = HelperExpression.IsStatisticalReportLocked();

        // Get the hierarchy codes for the user's departments
        var userDepartmentHierarchyCodes = _db.Departments
            .Where(x => x.UserLinks.Any(y => y.UserId == currentUserId))
            .Select(x => x.HierarchyCode)
            .ToList();

        // Create a predicate to filter departments based on hierarchy codes
        Expression<Func<Department, bool>> departmentPredicate = x => false;
        userDepartmentHierarchyCodes.ForEach(code =>
            departmentPredicate = departmentPredicate.Or(y => y.HierarchyCode.StartsWith(code)));

        // Get department IDs that match the hierarchy code predicate
        var userDepartmentIds = _db.Departments
            .Where(departmentPredicate)
            .Select(x => x.Id)
            .ToList();


        var baseItems = _db
            .StatisticalReports
            .AsExpandable()
            .Where(x =>
                // If user has full access permission, show all reports
                currentUserHasFullAccessPermission ||

                // If user has statistical report or publish permission, show all reports in their departments
                ((currentUserHasStatisticalReportPermission || currentUserHasStatisticalReportPublishPermission) &&
                 x.Categories.Any(y =>
                     y.DepartmentLinks.Any(d => userDepartmentIds.Contains(d.DepartmentId))
                 )
                ) ||

                // For users without specific permissions, show only published reports in their departments
                (x.IsPublished == 1 &&
                 x.Categories.Any(y =>
                     y.DepartmentLinks.Any(d => userDepartmentIds.Contains(d.DepartmentId))
                 )
                )
            )
            .Where(x =>
                currentUserHasStatisticalReportLockAndUnlockPermission ||
                currentUserHasStatisticalReportPublishPermission ||
                currentUserHasStatisticalReportWritePermission ||
                (
                    currentUserHasStatisticalReportDataEntryPermission &&
                    x.IsPublished == 1
                ) ||
                (
                    x.IsPublished == 1 &&
                    !isLockedExpression.Invoke(x)
                )
            );

        var items = baseItems
            .Where(x => x.NameAr.Contains(keyword) || x.NameEn.ToLower().Contains(keyword));

        if (initialYear is not null)
        {
            items = items.Where(x => x.InitialYear == initialYear);
        }

        if (cycles is { Length: > 0 })
        {
            items = items.Where(x => cycles.Contains(x.Cycle));
        }

        items = status switch
        {
            "locked" => items.Where(x => x.IsPublished == 1 && isLockedExpression.Invoke(x)),
            "active" => items.Where(x => x.IsPublished == 1 && !isLockedExpression.Invoke(x)),
            "not_published" =>
                (currentUserHasStatisticalReportPermission ||
                 currentUserHasStatisticalReportPublishPermission ||
                 currentUserHasFullAccessPermission)
                    ? items.Where(x => x.IsPublished == 0)
                    : items.Where(x => false),
            _ => items
        };

        if (departmentIds is { Length: > 0 })
        {
            items = items.Where(x =>
                x.Categories
                    .SelectMany(y => y.DepartmentLinks)
                    .Any(y => departmentIds.Contains(y.DepartmentId))
            );
        }

        return new TableResultDto<StatisticalReportListDto>
        {
            Items = items
                .Select(StatisticalReportListDto.Mapper())
                .ApplySort(orderBy)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .ToList(),
            Count = baseItems.Count(),
            FilteredCount = items.Count()
        };
    }

    public StatisticalReportGetDto GetById(Guid id)
    {
        var departments = _db.Departments.AsExpandable();

        var userId = _currentUserService.GetId();

        var hasFullAccessPermission = _db.Users
            .AsExpandable()
            .Where(x => EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.FullAccess, x.Id))
            .Any(x => x.Id == userId);

        return _db
            .StatisticalReports
            .Where(x => x.Id == id)
            .Map(new StatisticalReportGetDtoMappingStrategy(userId, departments,hasFullAccessPermission))
            .SingleOrDefault();
    }


    public StatisticalReportEditDto GetForEditById(Guid id)
    {
        return _db
            .StatisticalReports
            .Where(x => x.Id == id)
            .Map(new StatisticalReportEditDtoMappingStrategy(_db.StatisticalReportCategoryResults))
            .SingleOrDefault();
    }

    public StatisticalReportForDataEntryDto GetForDataEntryById(
        Guid id,
        Guid userId,
        bool hasFullAccessPermission
    )
    {
        var expression = StatisticalReportForDataEntryDto.Mapper(
            HelperFunctions.GetLanguageCode(),
            userId,
            hasFullAccessPermission
        );

        return _db
            .StatisticalReports
            .AsExpandable()
            .Where(x => x.Id == id)
            .Select(x => expression.Invoke(x))
            .SingleOrDefault();
    }

    public IEnumerable<StatisticalReportCategory> GetCategories(Guid id)
    {
        return _db
            .StatisticalReportCategories
            .Where(x => x.ReportId == id)
            .ToList();
    }

    public IEnumerable<StatisticalReportCategoryResult> FindResultsById(Guid[] resultIds)
    {
        return _db
            .StatisticalReportCategoryResults.Include(x => x.Attachments)
            .Where(x => resultIds.Contains(x.Id))
            .ToList();
    }

    public IEnumerable<StatisticalReportCategoryResult> FindResultsByReportId(Guid reportId)
    {
        return _db.StatisticalReportCategoryResults.Where(x =>
            x.CategoryDepartmentLink.Category.ReportId == reportId & x.Value != null);
    }

    public void CreateItems(IEnumerable<StatisticalReport> items)
    {
        _db.StatisticalReports.AddRange(items);
    }

    public void UpdateItems(IEnumerable<StatisticalReport> items)
    {
        _db.StatisticalReports.AttachRange(items);
    }

    public void CreateResults(IEnumerable<StatisticalReportCategoryResult> items)
    {
        _db.StatisticalReportCategoryResults.AddRange(items);
    }

    public void UpdateResults(IEnumerable<StatisticalReportCategoryResult> items)
    {
        _db.StatisticalReportCategoryResults.AttachRange(items);
    }

    public void RemoveById(Guid id)
    {
        _db.RemoveRange(
            _db.StatisticalReportCategoryResultAttachments.Where(x =>
                x.Result.CategoryDepartmentLink.Category.ReportId == id)
        );
        _db.RemoveRange(
            _db.StatisticalReportCategoryResults.Where(x => x.CategoryDepartmentLink.Category.ReportId == id)
        );
        _db.RemoveRange(
            _db.StatisticalReportCategoryDepartmentLinks.Where(x => x.Category.ReportId == id)
        );
        _db.RemoveRange(
            _db.StatisticalReportCategories.Where(x => x.ReportId == id)
        );
        _db.RemoveRange(
            _db.StatisticalReports.Where(x => x.Id == id)
        );
    }

    public void RemoveCategories(IEnumerable<StatisticalReportCategory> items)
    {
        var statisticalReportCategories = items.ToList();

        var itemIds = statisticalReportCategories.Select(y => y.Id);

        _db.RemoveRange(statisticalReportCategories);

        _db.RemoveRange(
            _db.StatisticalReportCategoryResultAttachments.Where(x =>
                itemIds.Contains(x.Result.CategoryDepartmentLink.Category.ReportId)
            )
        );
    }

    public bool HasAnyResults(IEnumerable<StatisticalReportCategory> categories)
    {
        return _db
            .StatisticalReportCategoryResults
            .Where(x => categories.Select(y => y.Id).Contains(x.CategoryDepartmentLink.CategoryId))
            .Any(x => x.Value != null || x.Attachments.Any());
    }


    public void CreateCategories(IEnumerable<StatisticalReportCategory> item)
    {
        _db.StatisticalReportCategories.AddRange(item);
    }

    public void CreateCategory(StatisticalReportCategory item)
    {
        _db.StatisticalReportCategories.Add(item);
    }

    public void CreateDepartment(StatisticalReportCategoryDepartmentLink item)
    {
        _db.StatisticalReportCategoryDepartmentLinks.Add(item);
    }

    public void RemoveDepartment(IEnumerable<StatisticalReportCategoryDepartmentLink> items)
    {
        _db.RemoveRange(items);
    }

    public void Commit()
    {
        _db.SaveChanges();
    }
}
