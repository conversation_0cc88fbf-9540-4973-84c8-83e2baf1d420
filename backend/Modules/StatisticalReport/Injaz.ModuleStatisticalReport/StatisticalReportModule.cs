using System.Reflection;
using Injaz.Core;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;


namespace Injaz.ModuleStatisticalReport;

public static class StatisticalReportModule
{
    public static void Setup(IServiceCollection services, string connectionString)
    {
        services.AddMediatR(typeof(StatisticalReportModule));
        services.AddScoped<IRepository, Repository>();

        // Add DbContext.
        var migrationsAssembly = typeof(HelperFunctions).GetTypeInfo().Assembly.GetName().Name;
        services
            .AddDbContext<IDbContext, DbContext>(options =>
                options.UseSqlServer(
                    connectionString,
                    b => b.MigrationsAssembly(migrationsAssembly))
            );
    }
}
