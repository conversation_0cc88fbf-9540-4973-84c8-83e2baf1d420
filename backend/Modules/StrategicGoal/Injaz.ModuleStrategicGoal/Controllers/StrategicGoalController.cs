using Injaz.Core;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.StrategicGoal;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleStrategicGoal.Controllers;

[Authorize(Policy = PermissionNameList.StrategicGoal)]
public class StrategicGoalController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly AppSettingService _appSettingService;

    public StrategicGoalController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer,
        AppSettingService appSettingService
    )
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
        _appSettingService = appSettingService;
    }

    [HttpGet]
    [Route("/strategic-goal")]
    public IActionResult List(
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.ToLower().Trim() ?? "";

        var items = _appDataContext
            .StrategicGoals
            .Where(x =>
                x.NameAr.Contains(keyword) ||
                x.NameEn.ToLower().Contains(keyword) ||
                x.Code.ToLower().Contains(keyword)
            );

        return this.GetResponseObject(extra: new
        {
            Items = items
                .Select(StrategicGoalListDto.Mapper(HelperFunctions.GetLanguageCode()))
                .OrderByDescending(g => g.ToYear + g.FromYear)
                .ThenBy(g => g.Order)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .ToList(),
            Count = _appDataContext.StrategicGoals.Count(),
            FilteredCount = items.Count()
        });
    }

    [HttpGet]
    [Route("/strategic-goal/{id}")]
    public IActionResult Get(
        Guid id,
        bool forEdit = false
    )
    {
        var item = forEdit
            ? _appDataContext.StrategicGoals
                .Select(StrategicGoalEditDto.Mapper())
                .FirstOrDefault(x => x.Id.Equals(id)) as object
            : _appDataContext.StrategicGoals
                .Select(StrategicGoalGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpGet("/strategic-goal/kpi/{id:guid}")]
    public IActionResult GetStrategicGoalKpis(
        Guid id,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;

        var items = _appDataContext
            .Kpis
            .Where(x => x.Status == Kpi.StatusActive)
            .Where(x => x.StrategicGoalLinks.Any(y => y.StrategicGoalId.Equals(id)))
            .Select(KpiWithResultDto.Mapper(HelperFunctions.GetLanguageCode(), canAchievedBeNegative));

        if (!items.Any())
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: new TableResultDto<KpiWithResultDto>
        {
            Items = items
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .ToList(),
            Count = items.Count(),
            FilteredCount = items.Count()
        });
    }

    [HttpPost]
    [Route("/strategic-goal")]
    public IActionResult Create(
        [BindBodySingleJson] StrategicGoalCreateDto strategicGoal
    )
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Create and add the new item.
        var item = new StrategicGoal
        {
            Code = strategicGoal.Code?.Trim(),
            NameAr = strategicGoal.NameAr.Trim(),
            NameEn = strategicGoal.NameEn.Trim(),
            DescriptionAr = strategicGoal.DescriptionAr?.Trim(),
            DescriptionEn = strategicGoal.DescriptionEn?.Trim(),
            Order = strategicGoal.Order,
            Category = strategicGoal.Category,
            FromYear = strategicGoal.FromYear,
            ToYear = strategicGoal.ToYear,
            Weight = strategicGoal.Weight
        };
        _appDataContext.StrategicGoals.Add(item);
        _appDataContext.SaveChanges();


        // Return the newly created item using dtos.
        return this.GetResponseObject(
            extra: _appDataContext
                .StrategicGoals
                .Select(StrategicGoalGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    [HttpPut]
    [Route("/strategic-goal")]
    public IActionResult Update(
        [BindBodySingleJson] StrategicGoalEditDto strategicGoal
    )
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Get item.
        var item = _appDataContext.StrategicGoals.Find(strategicGoal.Id);

        // Ensure the item exists.
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Update the item.
        item.Code = strategicGoal.Code?.Trim();
        item.NameAr = strategicGoal.NameAr.Trim();
        item.NameEn = strategicGoal.NameEn.Trim();
        item.DescriptionAr = strategicGoal.DescriptionAr?.Trim();
        item.DescriptionEn = strategicGoal.DescriptionEn?.Trim();
        item.Category = strategicGoal.Category;
        item.Order = strategicGoal.Order;
        item.FromYear = strategicGoal.FromYear;
        item.ToYear = strategicGoal.ToYear;
        item.Weight = strategicGoal.Weight;
        _appDataContext.SaveChanges();

        // Return the newly created item using dtos.
        return this.GetResponseObject(
            extra: _appDataContext
                .StrategicGoals
                .Select(StrategicGoalGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    [HttpDelete]
    [Route("/strategic-goal/{id}")]
    public IActionResult Delete(
        Guid id
    )
    {
        // Get item.
        var data = _appDataContext.StrategicGoals
            .Where(x => x.Id == id)
            .Select(x => new
            {
                Item = x,
                IsLinkedToOtherResources =
                    x.StrategicPerspectiveGoalsLinks.Any() ||
                    x.StrategicPillarGoalsLinks.Any() ||
                    x.OperationLinks.Any() ||
                    x.KpiLinks.Any() ||
                    x.BenchmarkLinks.Any() ||
                    x.RiskLinks.Any()
            })
            .SingleOrDefault();

        // Ensure the item exists.
        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Ensure nothing is linked to the team.
        if (data.IsLinkedToOtherResources)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["cannot_delete_item_item_connected_to_other_resources"] }
            );
        }

        // Remove the item.
        _appDataContext.StrategicGoals.Remove(data.Item);

        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            messages: new string[] { _localizer["item_removed_successfully"] }
        );
    }

    [HttpPatch]
    [Route("/strategic-goal/{id:guid}/update-order")]
    public IActionResult UpdateOrder(Guid id, [BindBodySingleJson] int order)
    {
        // Get item.
        var item = _appDataContext.StrategicGoals.Find(id);

        // Ensure the item exists.
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Update the item.
        item.Order = order;

        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            messages: new string[] { _localizer["item_updated_successfully"] }
        );
    }
}
