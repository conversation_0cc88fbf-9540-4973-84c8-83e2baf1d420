using Injaz.Core.Models;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleTeam.Controllers;

[Authorize(Policy = PermissionNameList.Team)]
public partial class TeamController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public TeamController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
    }
}
