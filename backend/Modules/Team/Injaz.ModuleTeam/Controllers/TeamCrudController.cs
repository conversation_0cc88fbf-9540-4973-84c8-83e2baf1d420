using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Team;
using Injaz.Core.Models.DomainClasses.App;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleTeam.Controllers;

public partial class TeamController
{
    [HttpGet]
    [Route("/team")]
    public IActionResult List(
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.ToLower().Trim() ?? "";

        var items = _appDataContext
            .Teams
            .AsExpandable()
            .Where(x => x.NameAr.Contains(keyword)
                        || x.NameEn.ToLower().Contains(keyword)
            );


        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: new
        {
            Items = items
                .OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .Select(TeamListDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.Teams.Count(),
            FilteredCount = items.Count()
        });
    }

    [HttpGet]
    [Route("/team/{id}")]
    public IActionResult Get(
        Guid id,
        bool forEdit = false
    )
    {
        var item = forEdit
            ? _appDataContext.Teams.AsExpandable()
                .Select(TeamEditDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id)) as object
            : _appDataContext.Teams.AsExpandable()
                .Select(TeamGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/team")]
    public IActionResult Create(
        [BindBodySingleJson] TeamCreateDto team
    )
    {
        return CreateOrUpdate(team);
    }

    [HttpPut]
    [Route("/team")]
    public IActionResult Update(
        [BindBodySingleJson] TeamEditDto team
    )
    {
        return CreateOrUpdate(team);
    }

    [HttpDelete]
    [Route("/team/{id}")]
    public IActionResult Delete(
        Guid id
    )
    {
        // Get item.
        var data = _appDataContext.Teams
            .Where(x => x.Id == id)
            .Select(x => new
            {
                Team = x,
                IsLinkedToOtherResources =
                    x.Plans.Any() ||
                    x.Tasks.Any() ||
                    x.Subtasks.Any()
            })
            .SingleOrDefault();

        // Ensure the item exists.
        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Ensure nothing is linked to the team.
        if (data.IsLinkedToOtherResources)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["cannot_delete_item_item_connected_to_other_resources"] }
            );
        }

        // _appDataContext.TeamUserLinks.RemoveRange(
        //     _appDataContext.TeamUserLinks.Where(x => x.TeamId.Equals(id)));

        // Remove the item.
        _appDataContext.Teams.Remove(data.Team);

        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            messages: new string[] { _localizer["item_removed_successfully"] }
        );
    }

    private IActionResult CreateOrUpdate(TeamCreateDto team)
    {
        if (!Validate(team, out var errorResult))
        {
            return errorResult;
        }

        Team item;
        if (team is TeamEditDto existingTeam)
        {
            item = _appDataContext
                .Teams
                .AsExpandable()
                .FirstOrDefault(x => x.Id.Equals(existingTeam.Id));

            // Ensure the item exists.
            if (item == null)
            {
                return this.GetResponseObject(success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] }
                );
            }
        }
        else
        {
            item = new Team();
        }

        item.NameAr = team.NameAr;
        item.NameEn = team.NameEn;

        // Add the item if it is new.
        if (!(team is TeamEditDto))
        {
            _appDataContext.Teams.Add(item);
        }

        _appDataContext.SaveChanges();

        // Return the item using the appropriate dto.
        return this.GetResponseObject(extra: _appDataContext
            .Teams.AsExpandable()
            .Select(TeamGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .First(x => x.Id.Equals(item.Id))
        );
    }

    private bool Validate(TeamCreateDto team, out IActionResult errorResult)
    {
        errorResult = null;

        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            errorResult = this.GetResponseObject(success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage))
                    .ToArray()
            );
            return false;
        }

        // Clean
        team.NameAr = team.NameAr.Trim();
        team.NameEn = HelperFunctions.Default(team.NameEn?.Trim(), team.NameAr);

        return true;
    }
}
