using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.StandardTask;
using MediatR;

namespace Injaz.ModuleTournament.Core.Commands;

public class CreateStandardSubtaskCommentCommand : IRequest<StandardSubtaskCommentDto>
{
    [Display(Name = "content")]
    [Required(ErrorMessage = "0_is_required")]
    public string Content { get; set; }

    // Used during creation
    public Guid? SubtaskId { get; set; }
}
