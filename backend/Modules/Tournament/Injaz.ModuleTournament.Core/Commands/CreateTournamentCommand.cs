using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Dtos.Tournament;
using Injaz.Core.ValidationAttributes;
using MediatR;

namespace Injaz.ModuleTournament.Core.Commands;

public class CreateTournamentCommand : IRequest<TournamentGetDto>
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "order")]
    [Required(ErrorMessage = "0_is_required")]
    public int? Order { get; set; }

    [Display(Name = "years")]
    [MinLength(1, ErrorMessage = "0_should_be_at_least_1")]
    public int[] Years { get; set; }

    [Display(Name = "is_hidden")] public bool IsHidden { get; set; }

    [Display(Name = "version")]
    [Required(ErrorMessage = "0_is_required")]
    public string Version { get; set; }

    [Display(Name = "year")]
    [Required(ErrorMessage = "0_is_required")]
    public int Year { get; set; }

    [Display(Name = "library_file")]
    [Required(ErrorMessage = "0_is_required")]
    public LibraryFileSimpleDto LibraryFile { get; set; }

    [Display(Name = "logo_file")]
    [MaxFileSize(2 * 1024 * 1024, ErrorMessage = "image_should_be_less_than_2mb")]
    [FileType("image/*")]
    public byte[] LogoFile { get; set; }
}
