using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.StandardTask;
using MediatR;

namespace Injaz.ModuleTournament.Core.Queries;

public class
    GetStandardSubtaskListQuery : IRequest<TableResultDto<StandardSubtaskListDto, StandardSubtaskStatisticsDto>>
{
    public Guid CurrentUserId;

    public bool CurrentUserHasTournamentPermission;

    public string Keyword { get; set; }

    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public string[] Statuses { get; set; }

    public Guid[] TaskIds { get; set; }

    public Guid[] TournamentIds { get; set; }

    public Guid[] PillarIds { get; set; }

    public Guid[] StandardIds { get; set; }

    public bool OnlyNeedAction { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
