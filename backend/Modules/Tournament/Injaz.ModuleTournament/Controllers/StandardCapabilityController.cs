using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleTournament.Core.Commands;
using Injaz.ModuleTournament.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.ModuleTournament.Controllers;

[Authorize(Policy = PermissionNameList.TournamentRead)]
public class StandardCapabilityController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public StandardCapabilityController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/standard/capability/{id:guid}")]
    public async Task<IActionResult> ListLinkedCapabilities(Guid id, string keyword, int pageNumber = 0,
        int pageSize = 20)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetStandardCapabilityListQuery
        {
            Id = id,
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }

    [HttpGet]
    [Route("/standard/capability/{id:guid}/unlinked")]
    public async Task<IActionResult> ListUnlinkedCapabilities(Guid id, string keyword, int pageNumber = 0,
        int pageSize = 20)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetStandardUnlinkedCapabilityListQuery
        {
            Id = id,
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }

    [HttpPost]
    [Route("/standard/capability/{id:guid}")]
    [Authorize(Policy = PermissionNameList.Tournament)]
    public async Task<IActionResult> LinkWithCapability(Guid id, Guid capabilityId)
    {
        await _mediator.Send(new LinkStandardWithCapabilityCommand
        {
            Id = id,
            CapabilityId = capabilityId
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpDelete]
    [Route("/standard/capability/{id:guid}")]
    [Authorize(Policy = PermissionNameList.Tournament)]
    public async Task<IActionResult> UnlinkFromCapability(Guid id, Guid capabilityId)
    {
        await _mediator.Send(new UnlinkStandardFromCapabilityCommand
        {
            Id = id,
            CapabilityId = capabilityId
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpPut]
    [Route("/standard/capability/order/{id:guid}")]
    [Authorize(Policy = PermissionNameList.Tournament)]
    public async Task<IActionResult> OrderCapabilities(
        Guid id,
        Guid[] orderedCapabilityIds,
        string keyword,
        int pageNumber,
        int pageSize
    )
    {
        await _mediator.Send(new OrderStandardCapabilityListCommand
        {
            Id = id,
            OrderedCapabilityIds = orderedCapabilityIds,
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }
}
