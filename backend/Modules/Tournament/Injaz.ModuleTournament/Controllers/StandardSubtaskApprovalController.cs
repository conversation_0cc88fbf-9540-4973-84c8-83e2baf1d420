using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.ModuleTournament.Controllers;

public class StandardTaskApprovalController : Controller
{
    private readonly IMediator _mediator;
    private readonly UserManager<User> _userManager;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public StandardTaskApprovalController(
        IMediator mediator,
        UserManager<User> userManager,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _userManager = userManager;
        _localizer = localizer;
    }

    [HttpPut]
    [Route("/standard/task/subtask/{id:guid}/initially-submit")]
    public async Task<IActionResult> InitiallySubmit(Guid id)
    {
        await _mediator.Send(new InitiallySubmitStandardSubtaskCommand
        {
            Id = id,
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
            CurrentUserHasTournamentPermission = await this.EnsureUserHasPermission(PermissionNameList.Tournament),
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpPut]
    [Route("/standard/task/subtask/{id:guid}/initially-approve")]
    public async Task<IActionResult> InitiallyApprove(Guid id, string note)
    {
        await _mediator.Send(new InitiallyApproveStandardSubtaskCommand
        {
            Id = id,
            Note = note,
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
            CurrentUserHasTournamentPermission = await this.EnsureUserHasPermission(PermissionNameList.Tournament),
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpPut]
    [Route("/standard/task/subtask/{id:guid}/initially-reject")]
    public async Task<IActionResult> InitiallyReject(Guid id, string note)
    {
        await _mediator.Send(new InitiallyRejectStandardSubtaskCommand
        {
            Id = id,
            Note = note,
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
            CurrentUserHasTournamentPermission = await this.EnsureUserHasPermission(PermissionNameList.Tournament),
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpPut]
    [Route("/standard/task/subtask/{id:guid}/submit")]
    public async Task<IActionResult> Submit(Guid id)
    {
        await _mediator.Send(new SubmitStandardSubtaskCommand
        {
            Id = id,
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
            CurrentUserHasTournamentPermission = await this.EnsureUserHasPermission(PermissionNameList.Tournament),
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpPut]
    [Route("/standard/task/subtask/{id:guid}/approve")]
    public async Task<IActionResult> Approve(Guid id, string note)
    {
        await _mediator.Send(new ApproveStandardSubtaskCommand
        {
            Id = id,
            Note = note,
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
            CurrentUserHasTournamentPermission = await this.EnsureUserHasPermission(PermissionNameList.Tournament),
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpPut]
    [Route("/standard/task/subtask/{id:guid}/reject")]
    public async Task<IActionResult> Reject(Guid id, string note, double? progress)
    {
        await _mediator.Send(new RejectStandardSubtaskCommand
        {
            Id = id,
            Note = note,
            Progress = progress,
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
            CurrentUserHasTournamentPermission = await this.EnsureUserHasPermission(PermissionNameList.Tournament),
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpPut]
    [Route("/standard/task/subtask/{id:guid}/finalize")]
    [Authorize(Policy = PermissionNameList.Tournament)]
    public async Task<IActionResult> Finalize(Guid id, string note)
    {
        await _mediator.Send(new FinalizeStandardSubtaskCommand
        {
            Id = id,
            Note = note,
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User))
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }
}
