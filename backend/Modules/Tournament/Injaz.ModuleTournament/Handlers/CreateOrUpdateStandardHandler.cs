using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Standard;
using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Services;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;

namespace Injaz.ModuleTournament.Handlers;

public class CreateOrUpdateStandardHandler :
    IRequestHandler<CreateStandardCommand, StandardGetDto>,
    IRequestHandler<UpdateStandardCommand, StandardGetDto>
{
    private readonly IDbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdateStandardHandler(
        IDbContext db,
        ValidationService validationService
    )
    {
        _db = db;
        _validationService = validationService;
    }

    public Task<StandardGetDto> Handle(CreateStandardCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<StandardGetDto> Handle(UpdateStandardCommand command, CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private Task<StandardGetDto> CreateOrUpdate(CreateStandardCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }

        Standard item;

        if (command is UpdateStandardCommand updateCommand)
        {
            item = _db.Standards.Find(updateCommand.Id);

            if (item == null) throw new ItemNotFoundException();
        }
        else
        {
            item = new Standard
            {
                PillarId = command.PillarId!.Value,
                Order = _db.Standards.Count(x => x.PillarId == command.PillarId!.Value)
            };

            _db.Standards.Add(item);
        }


        item.NameAr = command.NameAr.Trim();
        item.NameEn = command.NameEn?.Trim() ?? item.NameAr;
        item.Weight = command.Weight!.Value;
        item.TeamAchievementSummary = command.TeamAchievementSummary;
        item.TeamFormationLibraryFileId = command.TeamFormationLibraryFile?.Id;
        item.PresentationLibraryFileId = command.PresentationLibraryFile?.Id;

        _db.SaveChanges();

        // Return the newly created item using dtos.
        return Task.FromResult(
            _db.Standards
                .AsExpandable()
                .Select(StandardGetDto.Mapper(
                    HelperFunctions.GetLanguageCode(),
                    command.CurrentUserId,
                    command.CurrentUserHasTournamentPermission
                ))
                .FirstOrDefault(p => p.Id.Equals(item.Id))
        );
    }
}
