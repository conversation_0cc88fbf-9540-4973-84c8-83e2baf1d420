using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.StandardTask;
using Injaz.Core.Exceptions;
using Injaz.Core.Hubs.Default;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Localization;
using LinqKit;
using Microsoft.EntityFrameworkCore;

namespace Injaz.ModuleTournament.Handlers;

public class CreateOrUpdateStandardSubtaskHandler :
    IRequestHandler<CreateStandardSubtaskCommand, StandardSubtaskGetDto>,
    IRequestHandler<UpdateStandardSubtaskCommand, StandardSubtaskGetDto>
{
    private readonly IDbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ValidationService _validationService;
    private readonly IHubContext<DefaultHub, IDefaultClient> _defaultHubContext;

    public CreateOrUpdateStandardSubtaskHandler(
        IDbContext db,
        IStringLocalizer<SharedResource> localizer,
        ValidationService validationService,
        IHubContext<DefaultHub, IDefaultClient> defaultHubContext
    )
    {
        _db = db;
        _localizer = localizer;
        _validationService = validationService;
        _defaultHubContext = defaultHubContext;
    }

    public Task<StandardSubtaskGetDto> Handle(CreateStandardSubtaskCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<StandardSubtaskGetDto> Handle(UpdateStandardSubtaskCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private async Task<StandardSubtaskGetDto> CreateOrUpdate(CreateStandardSubtaskCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
        {
            throw new GenericException
            {
                Messages = errors.Select(x => x.ErrorMessage).ToArray()
            };
        }

        // Ensure that to is larger than or equal to from
        if (command.From > command.To)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["from_time_cannot_be_larger_than_to_time"] }
            };
        }

        StandardSubtask item;

        if (command is UpdateStandardSubtaskCommand updateCommand)
        {
            var expression = HelperExpression.StandardSubtaskStatus();

            var group = _db.StandardSubtasks
                .AsExpandable()
                .Where(x => x.Id == updateCommand.Id)
                .Select(x => new
                {
                    Item = x,
                    Status = expression.Invoke(x)
                })
                .FirstOrDefault();

            item = group?.Item;

            var status = group?.Status;

            if (item == null) throw new ItemNotFoundException();


            // Ensure that the subtask status is draft or
            // current user is a supervisor.
            if (
                status != StandardSubtaskApproval.TypeDraft &&
                status != StandardSubtaskApproval.TypeInitiallyRejected &&
                !updateCommand.CurrentUserHasTournamentPermission
            )
            {
                throw new GenericException
                {
                    Messages = new string[] { _localizer["cannot_update_an_initially_approved_subtask"] }
                };
            }
        }
        else
        {
            item = new StandardSubtask
            {
                TaskId = command.TaskId
            };

            _db.StandardSubtasks.Add(item);
        }

        // Ensure main task exists
        var task = _db
            .StandardTasks
            .Include(x => x.Subtasks)
            .FirstOrDefault(x => x.Id == item.TaskId);

        if (task == null) throw new ItemNotFoundException();

        // Ensure that the subtask time frame falls
        // within the task time frame.
        if (!(command.From >= task.From && command.To <= task.To))
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["the_subtask_time_frame_should_fall_within_its_parent_task"] }
            };
        }

        // Ensure current user has subtask creator
        // role.
        var member = _db.StandardUserLinks.Find(task.StandardId, command.CurrentUserId);
        var isSubtaskCreator = member?.Roles.Contains(StandardUserLink.RoleSubtaskCreator) ?? false;

        if (!isSubtaskCreator && !command.CurrentUserHasTournamentPermission)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["you_are_not_authorized_to_create_subtasks"] }
            };
        }


        item.NameAr = command.NameAr.Trim();
        item.NameEn = command.NameEn?.Trim() ?? item.NameAr;
        item.From = command.From;
        item.To = command.To;

        _db.SaveChanges();

        // Notify clients of their tasks change.
        await _defaultHubContext.Clients.All.Message(new { Type = MessageTypeList.MyTasksNotificationCountUpdate });

        // Return the newly created item using dtos.
        return _db
            .StandardSubtasks
            .AsExpandable()
            .Where(x => x.Id == item.Id)
            .Where(x =>
                command.CurrentUserHasTournamentPermission ||
                x.Task.Standard.UserLinks.Any(y => y.UserId == command.CurrentUserId)
            )
            .Select(
                StandardSubtaskGetDto.Mapper(
                    HelperFunctions.GetLanguageCode(),
                    command.CurrentUserId,
                    command.CurrentUserHasTournamentPermission
                )
            )
            .FirstOrDefault();
    }
}
