using Injaz.Core.Constants;
using Injaz.Core.Hubs.Default;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;
using Microsoft.AspNetCore.SignalR;

namespace Injaz.ModuleTournament.Handlers;

public class DeletePrincipleHandler : IRequestHandler<DeletePrincipleCommand>
{
    private readonly IDbContext _db;
    private readonly IHubContext<DefaultHub, IDefaultClient> _defaultHubContext;


    public DeletePrincipleHandler(
        IDbContext db,
        IHubContext<DefaultHub, IDefaultClient> defaultHubContext
    )
    {
        _db = db;
        _defaultHubContext = defaultHubContext;
    }

    public async Task<Unit> Handle(DeletePrincipleCommand command, CancellationToken cancellationToken)
    {
        _db.Principles.RemoveRange(_db.Principles.Where(x => x.Id == command.Id));

        _db.SaveChanges();

        // Notify clients of their tasks change.
        await _defaultHubContext.Clients.All.Message(new { Type = MessageTypeList.MyTasksNotificationCountUpdate });

        return Unit.Value;
    }
}
