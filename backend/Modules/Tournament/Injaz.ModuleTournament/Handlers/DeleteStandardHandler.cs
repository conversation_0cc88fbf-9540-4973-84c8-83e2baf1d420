using Injaz.ModuleTournament.Core.Commands;
using MediatR;

namespace Injaz.ModuleTournament.Handlers;

public class DeleteStandardHandler : IRequestHandler<DeleteStandardCommand>
{
    private readonly IDbContext _db;

    public DeleteStandardHandler(IDbContext db) => _db = db;

    public Task<Unit> Handle(DeleteStandardCommand command, CancellationToken cancellationToken)
    {
        _db.Principles.RemoveRange(_db.Principles.Where(x => x.StandardId == command.Id));
        _db.StandardSubtasks.RemoveRange(_db.StandardSubtasks.Where(x => x.Task.StandardId == command.Id));
        _db.StandardTasks.RemoveRange(_db.StandardTasks.Where(x => x.StandardId == command.Id));
        _db.Standards.RemoveRange(_db.Standards.Where(x => x.Id == command.Id));
        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
