using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Exceptions;
using Injaz.Core.Hubs.Default;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Localization;
using LinqKit;
using Injaz.Core.Models.SqlFunctions;
using Newtonsoft.Json;

namespace Injaz.ModuleTournament.Handlers;

public class DeleteStandardSubtaskHandler : IRequestHandler<DeleteStandardSubtaskCommand>
{
    private readonly IDbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IHubContext<DefaultHub, IDefaultClient> _defaultHubContext;


    public DeleteStandardSubtaskHandler(
        IDbContext db,
        IStringLocalizer<SharedResource> localizer,
        IHubContext<DefaultHub, IDefaultClient> defaultHubContext)
    {
        _db = db;
        _localizer = localizer;
        _defaultHubContext = defaultHubContext;
    }

    public async Task<Unit> Handle(DeleteStandardSubtaskCommand command, CancellationToken cancellationToken)
    {
        var expression = HelperExpression.StandardSubtaskStatus();

        var group = _db.StandardSubtasks
            .AsExpandable()
            .Where(x => x.Id == command.Id)
            .Select(x => new
            {
                Item = x,
                Status = expression.Invoke(x)
            })
            .FirstOrDefault();

        var item = group?.Item;

        var status = group?.Status;

        if (item == null) throw new ItemNotFoundException();

        var subtaskCreatorRole = JsonConvert.SerializeObject(new[] { StandardUserLink.RoleSubtaskCreator });

        var isSubtaskCreator = _db.StandardSubtasks
            .AsExpandable()
            .Any(x =>
                x.Id == command.Id &&
                x.Task.Standard.UserLinks.Any(y =>
                    y.UserId == command.CurrentUserId && ContainsAtLeastOneSqlFunction.Call(y.Roles, subtaskCreatorRole)
                )
            );

        if (
            !command.CurrentUserHasTournamentPermission &&
            !isSubtaskCreator
        )
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["you_are_not_authorized_to_perform_this_action"] }
            };
        }

        if (
            status != StandardSubtaskApproval.TypeDraft &&
            status != StandardSubtaskApproval.TypeInitiallyRejected &&
            !command.CurrentUserHasTournamentPermission
        )
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["cannot_delete_an_initially_approved_subtask"] }
            };
        }

        _db.StandardSubtasks.RemoveRange(_db.StandardSubtasks.Where(x => x.Id == command.Id));

        _db.SaveChanges();

        // Notify clients of their tasks change.
        await _defaultHubContext.Clients.All.Message(new { Type = MessageTypeList.MyTasksNotificationCountUpdate });

        return Unit.Value;
    }
}
