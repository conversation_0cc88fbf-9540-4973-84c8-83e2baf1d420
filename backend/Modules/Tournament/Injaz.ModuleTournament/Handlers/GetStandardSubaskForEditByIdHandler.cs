using LinqKit;
using Injaz.Core.Dtos.StandardTask;
using Injaz.Core.Exceptions;
using Injaz.ModuleTournament.Core.Queries;
using MediatR;

namespace Injaz.ModuleTournament.Handlers;

public class
    GetStandardSubtaskForEditByIdHandler : IRequestHandler<GetStandardSubtaskForEditByIdQuery, StandardSubtaskEditDto>
{
    private readonly IDbContext _db;

    public GetStandardSubtaskForEditByIdHandler(IDbContext db) => _db = db;

    public Task<StandardSubtaskEditDto> Handle(
        GetStandardSubtaskForEditByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        var item = _db
            .StandardSubtasks
            .AsExpandable()
            .Where(x => x.Id == query.Id)
            .Select(StandardSubtaskEditDto.Mapper())
            .FirstOrDefault();

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
