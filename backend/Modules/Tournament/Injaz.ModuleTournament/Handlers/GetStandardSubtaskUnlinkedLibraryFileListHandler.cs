using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Dtos.Misc;
using Injaz.ModuleTournament.Core.Queries;
using MediatR;

namespace Injaz.ModuleTournament.Handlers;

public class GetStandardSubtaskUnlinkedLibraryFileListHandler : IRequestHandler<
    GetStandardSubtaskUnlinkedLibraryFileListQuery,
    TableResultDto<LibraryFileGetDto>
>
{
    private readonly IDbContext _db;

    public GetStandardSubtaskUnlinkedLibraryFileListHandler(IDbContext db) => _db = db;

    public Task<TableResultDto<LibraryFileGetDto>> Handle(
        GetStandardSubtaskUnlinkedLibraryFileListQuery query,
        CancellationToken cancellationToken
    )
    {
        var keyword = query.Keyword?.Trim().ToLower() ?? "";

        var items = _db
            .LibraryFiles
            .AsExpandable()
            .Where(x => x.StandardSubtaskLinks.All(y => y.SubtaskId != query.Id));

        var count = items.Count();

        items = items.Where(x =>
            x.NameAr.ToLower().Contains(keyword) ||
            x.NameEn.ToLower().Contains(keyword)
        );

        return Task.FromResult(
            new TableResultDto<LibraryFileGetDto>
            {
                Items = items
                    .Select(LibraryFileGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                    .OrderBy(x => x.Name)
                    .Skip(query.PageNumber * query.PageSize)
                    .Take(query.PageSize),
                Count = count,
                FilteredCount = items.Count()
            }
        );
    }
}
