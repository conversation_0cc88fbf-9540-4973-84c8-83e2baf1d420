using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.Standard;
using Injaz.ModuleTournament.Core.Queries;
using MediatR;

namespace Injaz.ModuleTournament.Handlers;

public class GetStandardSubtaskUnlinkedMemberListHandler : IRequestHandler<
    GetStandardSubtaskUnlinkedMemberListQuery,
    TableResultDto<StandardMemberDto>
>
{
    private readonly IDbContext _db;

    public GetStandardSubtaskUnlinkedMemberListHandler(IDbContext db) => _db = db;

    public Task<TableResultDto<StandardMemberDto>> Handle(
        GetStandardSubtaskUnlinkedMemberListQuery query,
        CancellationToken cancellationToken)
    {
        var keyword = query.Keyword?.ToLower().Trim() ?? "";

        var lang = HelperFunctions.GetLanguageCode();

        var items = _db
            .StandardSubtasks
            .AsExpandable()
            .Where(x => x.Id == query.Id)
            .SelectMany(subtask =>
                subtask.Task.Standard.UserLinks.Where(standardMember =>
                    subtask.StandardUserLinks.All(subtaskMember => standardMember.UserId != subtaskMember.UserId)
                )
            );

        var count = items.Count();

        items = items.Where(x => x.User.NameAr.Contains(keyword) || x.User.NameEn.Contains(keyword));

        return Task.FromResult(
            new TableResultDto<StandardMemberDto>
            {
                Items = items
                    .Select(StandardMemberDto.Mapper(lang))
                    .OrderBy(x => x.User.Name)
                    .Skip(query.PageNumber * query.PageSize)
                    .Take(query.PageSize)
                    .ToList(),
                Count = count,
                FilteredCount = items.Count()
            }
        );
    }
}
