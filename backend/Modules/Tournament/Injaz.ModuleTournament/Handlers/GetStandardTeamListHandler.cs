using LinqKit;
using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.Standard;
using Injaz.ModuleTournament.Core.Queries;
using MediatR;

namespace Injaz.ModuleTournament.Handlers;

public class GetStandardTeamListHandler : IRequestHandler<GetStandardTeamListQuery, TableResultDto<StandardTeamListDto>>
{
    private readonly IDbContext _db;

    public GetStandardTeamListHandler(IDbContext db) => _db = db;

    public Task<TableResultDto<StandardTeamListDto>> Handle(
        GetStandardTeamListQuery query,
        CancellationToken cancellationToken
    )
    {
        var keyword = query.Keyword?.ToLower().Trim() ?? "";

        var lang = HelperFunctions.GetLanguageCode();

        var items = _db
            .Standards
            .AsExpandable()
            .AsQueryable();

        if (!string.IsNullOrEmpty(keyword))
        {
            items = items.Where(x => x.NameAr.Contains(keyword) || x.NameEn.ToLower().Contains(keyword));
        }

        // Filter by tournament
        items = query.TournamentIds is { Length: > 0 }
            ? items.Where(x => query.TournamentIds.Contains(x.Pillar.TournamentId))
            : items.Where(x => x.Pillar.Tournament.IsDefault == 1);

        items = items
            .OrderBy(x => x.Pillar.Order)
            .ThenBy(x => x.Order)
            .ThenBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn);

        var filteredCount = items.Count();

        if (query.PageSize > -1)
        {
            items = items
                .Skip(query.PageSize * query.PageNumber)
                .Take(query.PageSize);
        }

        return Task.FromResult(
            new TableResultDto<StandardTeamListDto>
            {
                Items = items
                    .Select(StandardTeamListDto.Mapper(lang))
                    .ToList(),
                Count = _db.Standards.Count(),
                FilteredCount = filteredCount
            }
        );
    }
}
