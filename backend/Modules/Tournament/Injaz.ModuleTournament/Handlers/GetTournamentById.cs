using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Tournament;
using Injaz.Core.Exceptions;
using Injaz.ModuleTournament.Core.Queries;
using MediatR;

namespace Injaz.ModuleTournament.Handlers;

public class GetTournamentByIdHandler : IRequestHandler<GetTournamentByIdQuery, TournamentGetDto>
{
    private readonly IDbContext _db;

    public GetTournamentByIdHandler(IDbContext db) => _db = db;

    public Task<TournamentGetDto> Handle(GetTournamentByIdQuery query, CancellationToken cancellationToken)
    {
        var item = _db.Tournaments
            .AsExpandable()
            .Select(TournamentGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(p => p.Id.Equals(query.Id));

        if (item == null) throw new ItemNotFoundException();

        return Task.FromResult(item);
    }
}
