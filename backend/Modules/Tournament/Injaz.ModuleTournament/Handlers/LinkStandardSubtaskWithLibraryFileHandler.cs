using Injaz.Core;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;
using LinqKit;

namespace Injaz.ModuleTournament.Handlers;

public class LinkStandardSubtaskWithLibraryFileHandler : IRequestHandler<LinkStandardSubtaskWithLibraryFileCommand>
{
    private readonly IDbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public LinkStandardSubtaskWithLibraryFileHandler(
        IDbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(LinkStandardSubtaskWithLibraryFileCommand command, CancellationToken cancellationToken)
    {
        var dtoExpression = LibraryFileSimpleDto.Mapper(HelperFunctions.GetLanguageCode());

        var data = _db
            .LibraryFiles
            .AsExpandable()
            .Where(x => x.Id.Equals(command.LibraryFileId))
            .Select(x => new
            {
                File = dtoExpression.Invoke(x),
                IsLinked = x.StandardSubtaskLinks.Any(y => y.SubtaskId == command.Id),
            })
            .FirstOrDefault();

        var file = data?.File;

        var isLinked = data?.IsLinked ?? false;

        var expression = HelperExpression.StandardSubtaskStatus();

        var group = _db.StandardSubtasks
            .AsExpandable()
            .Where(x => x.Id == command.Id)
            .Select(x => new
            {
                Item = x,
                Status = expression.Invoke(x)
            })
            .FirstOrDefault();

        var item = group?.Item;

        var status = group?.Status;

        // Ensure that the file and standard exists.
        if (item == null || file == null)
        {
            throw new GenericException
                { Messages = new string[] { _localizer["standard_or_library_file_does_not_exist"] } };
        }

        // Make sure that the current subtask status is
        // either initially approved or rejected or assigned.
        if (
            status != StandardSubtaskApproval.TypeInitiallyApproved &&
            status != StandardSubtaskApproval.TypeAssigned &&
            status != StandardSubtaskApproval.TypeRejected
        )
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["cannot_update_the_task_now"] }
            };
        }

        // Ensure that the item is not already linked with the standard.
        if (isLinked)
        {
            throw new GenericException { Messages = new string[] { _localizer["standard_is_already_using_the_file"] } };
        }

        _db.StandardSubtaskLibraryFileLinks.Add(
            new StandardSubtaskLibraryFileLink
            {
                SubtaskId = command.Id,
                LibraryFileId = command.LibraryFileId
            }
        );

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
