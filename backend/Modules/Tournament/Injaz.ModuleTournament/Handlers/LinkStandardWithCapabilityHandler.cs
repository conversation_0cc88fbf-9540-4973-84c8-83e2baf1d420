using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Capability;
using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;


namespace Injaz.ModuleTournament.Handlers;

public class LinkStandardWithCapabilityHandler : IRequestHandler<LinkStandardWithCapabilityCommand>
{
    private readonly IDbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public LinkStandardWithCapabilityHandler(
        IDbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(LinkStandardWithCapabilityCommand command, CancellationToken cancellationToken)
    {
        var dtoExpression = CapabilityGetDto.Mapper(HelperFunctions.GetLanguageCode());

        var data = _db
            .Capabilities
            .AsExpandable()
            .Where(x => x.Id.Equals(command.CapabilityId))
            .Select(x => new
            {
                Capability = dtoExpression.Invoke(x),
                IsLinked = x.StandardLinks.Any(y => y.StandardId.Equals(command.Id)),
            })
            .FirstOrDefault();

        var capability = data?.Capability;
        var isLinked = data?.IsLinked ?? false;

        var item = _db
            .Standards
            .Where(x => x.Id == command.Id)
            .Select(x => new
            {
                Standard = x,
                MaxOrder = x.CapabilityLinks.Any()
                    ? x.CapabilityLinks.OrderByDescending(y => y.Order).First().Order
                    : (int?)null
            })
            .FirstOrDefault();

        // Ensure that the file and standard exists.
        if (item?.Standard == null || capability == null)
        {
            throw new GenericException
                { Messages = new string[] { _localizer["standard_or_capability_does_not_exist"] } };
        }

        // Ensure that the item is not already linked with the standard.
        if (isLinked)
        {
            throw new GenericException { Messages = new string[] { _localizer["standard_is_already_using_the_file"] } };
        }

        _db.StandardCapabilityLinks.Add(
            new StandardCapabilityLink
            {
                StandardId = command.Id,
                CapabilityId = command.CapabilityId,
                Order = (item?.MaxOrder ?? -1) + 1
            }
        );

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
