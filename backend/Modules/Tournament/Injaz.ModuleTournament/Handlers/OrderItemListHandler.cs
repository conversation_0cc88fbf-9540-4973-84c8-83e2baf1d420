using Injaz.Core.Exceptions;
using Injaz.Core.Models.Abstraction;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleTournament.Handlers;

public class OrderItemListHandler : IRequestHandler<OrderTournamentItemListCommand>
{
    private readonly IDbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public OrderItemListHandler(
        IDbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(OrderTournamentItemListCommand command, CancellationToken cancellationToken)
    {
        // Check that type is valid.
        var validTypes = new[] { "pillar", "standard", "principle" };
        if (!validTypes.Contains(command.Type))
        {
            throw new GenericException { Messages = new string[] { _localizer["invalid_type"] } };
        }

        // Check that all ids are there.
        var items = (command.Type switch
        {
            "pillar" => _db.Pillars.Where(x => command.Ids.Contains(x.Id)).ToList(),
            "standard" => _db.Standards.Where(x => command.Ids.Contains(x.Id)).ToList(),
            _ => _db.Principles.Where(x => command.Ids.Contains(x.Id)).ToList() as IEnumerable<ITournamentEntity>
        }).ToList();

        if (items.Count != command.Ids.Length)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["could_not_find_all_items"] }
            };
        }

        // Order
        items.ForEach(x =>
        {
            var idx = Array.FindIndex(command.Ids, y => y == x.Id);
            x.Order = idx;
        });

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
