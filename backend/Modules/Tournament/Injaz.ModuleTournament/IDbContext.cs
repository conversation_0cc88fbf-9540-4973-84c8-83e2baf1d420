using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.DomainClasses.App.EvaluationModel;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;

namespace Injaz.ModuleTournament;

public interface IDbContext
{
    int SaveChanges();

    void RemoveRange(IEnumerable<object> entities);

    EntityEntry<TEntity> Add<TEntity>(TEntity entity) where TEntity : class;

    void Dispose();

    DbSet<Tournament> Tournaments { get; set; }
    DbSet<Pillar> Pillars { get; set; }
    DbSet<Standard> Standards { get; set; }
    DbSet<Principle> Principles { get; set; }

    public DbSet<Capability> Capabilities { get; set; }
    public DbSet<Kpi> Kpis { get; set; }
    public DbSet<LibraryFile> LibraryFiles { get; set; }

    public DbSet<StandardCapabilityLink> StandardCapabilityLinks { get; set; }
    public DbSet<StandardKpiLink> StandardKpiLinks { get; set; }
    public DbSet<StandardLibraryFileLink> StandardLibraryFileLinks { get; set; }
    DbSet<StandardUserLink> StandardUserLinks { get; set; }
    DbSet<StandardTask> StandardTasks { get; set; }
    DbSet<StandardSubtask> StandardSubtasks { get; set; }
    DbSet<StandardSubtaskStandardUserLink> StandardSubtaskStandardUserLinks { get; set; }
    DbSet<StandardSubtaskApproval> StandardSubtaskApprovals { get; set; }
    DbSet<StandardSubtaskLibraryFileLink> StandardSubtaskLibraryFileLinks { get; set; }
    DbSet<StandardSubtaskComment> StandardSubtaskComments { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<EvaluationInstance> EvaluationInstances { get; set; }
    public DbSet<User> Users { get; set; }
}
