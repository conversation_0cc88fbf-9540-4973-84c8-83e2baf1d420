using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.User;
using MediatR;

namespace Injaz.ModuleUser.Core.Queries;

public class GetUserListQuery : IRequest<TableResultDto<UserListDto>>
{
    public string Keyword { get; set; }

    public string EmployeeNumber { get; set; }

    public string Email { get; set; }

    public string[] Statuses { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
