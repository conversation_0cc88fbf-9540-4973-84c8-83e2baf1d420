using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleUser.Core.Commands;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleUser.Controllers;

public class AccountController : Controller
{
    private readonly IMediator _mediator;
    private readonly UserManager<User> _userManager;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public AccountController(
        IMediator mediator,
        UserManager<User> userManager,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _userManager = userManager;
        _localizer = localizer;
    }

    [HttpPost]
    [Route("/account/forget-password")]
    [AllowAnonymous]
    public async Task<IActionResult> ForgetPassword(
        [BindBodySingleJson] ForgetPasswordCommand forgetPassword
    )
    {
        await _mediator.Send(forgetPassword);

        return this.GetResponseObject(
            messages: new string[] {
                _localizer["password_reset_link_sent_please_check_your_email"] }
        );
    }

    [HttpPost]
    [Route("/account/reset-password")]
    [AllowAnonymous]
    public async Task<IActionResult> ResetPassword(
        [BindBodySingleJson] ResetPasswordCommand resetPassword
    )
    {
        await _mediator.Send(resetPassword);

        return this.GetResponseObject(
            messages: new string[] { _localizer["password_reset_successful"] }
        );
    }
}
