using Injaz.Core.Permission;
using Injaz.ModuleUser.Core.Commands;
using Injaz.ModuleUser.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleUser.Controllers;

public class NewUserRequestController : Controller
{
    private readonly IMediator _mediator;

    public NewUserRequestController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    [Route("/new-user-request")]
    [Authorize(Policy = PermissionNameList.User)]
    public async Task<IActionResult> List(
        string? keyword = "",
        string[]? flowStates = null,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var response = await _mediator.Send(new GetNewUserRequestListQuery
        {
            Keyword = keyword,
            PageNumber = pageNumber,
            FlowStates = flowStates,
            PageSize = pageSize
        });

        return this.GetResponseObject(extra: response);
    }

    [HttpGet]
    [Route("/new-user-request/{id:guid}")]
    [Authorize(Policy = PermissionNameList.User)]
    public async Task<IActionResult> Get(Guid id)
    {
        var item = await _mediator.Send(new GetNewUserRequestByIdQuery { Id = id });
        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/new-user-request")]
    [AllowAnonymous]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreateNewUserRequestCommand request
    )
    {
        var response = await _mediator.Send(request);
        return this.GetResponseObject(extra: response);
    }
}