using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleUser.Core.Commands;
using Injaz.ModuleUser.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.ModuleUser.Controllers;

public class ProfileController : Controller
{
    private readonly IMediator _mediator;
    private readonly UserManager<User> _userManager;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public ProfileController(
        IMediator mediator,
        UserManager<User> userManager,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _userManager = userManager;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/user/profile")]
    public async Task<IActionResult> Get()
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetUserForProfileQuery
        {
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User))
        }));
    }

    [HttpPut]
    [Route("/user/change-password")]
    public async Task<IActionResult> ChangePassword(string currentPassword, string newPassword)
    {
        await _mediator.Send(new ChangeUserPasswordCommand
        {
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
            CurrentPassword = currentPassword,
            NewPassword = newPassword
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }
}
