using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleUser.Core.Commands;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleUser.Handlers;

public class ChangePasswordHandler : IRequestHandler<ChangeUserPasswordCommand>
{
    private readonly UserManager<User> _userManager;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IMediator _mediator;

    public ChangePasswordHandler(
        UserManager<User> userManager,
        IStringLocalizer<SharedResource> localizer,
        IMediator mediator
    )
    {
        _userManager = userManager;
        _localizer = localizer;
        _mediator = mediator;
    }

    public async Task<Unit> Handle(ChangeUserPasswordCommand command, CancellationToken cancellationToken)
    {
        var user = await _userManager.FindByIdAsync(command.CurrentUserId.ToString());

        // Ensure that the user is not ldap or 365
        if (user.Type != User.TypeNormal)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["cannot_change_password_for_this_user"] }
            };
        }

        var result = await _userManager.ChangePasswordAsync(user, command.CurrentPassword, command.NewPassword);

        if (!result.Succeeded)
        {
            throw new GenericException
            {
                Messages = result.Errors.Select(x => x.Description).ToArray()
            };
        }

        // Remove the requirement for changing password.
        await _mediator.Send(new ToggleUserMandatoryPasswordChangeCommand
        {
            UserId = user.Id,
            IsEnabled = false
        }, cancellationToken);

        return Unit.Value;
    }
}
