using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.NewUserRequest;
using Injaz.Core.Exceptions;
using Injaz.Core.Flow.Services;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Services;
using Injaz.ModuleUser.Core.Commands;
using Injaz.ModuleUser.Core.Queries;
using MediatR;

namespace Injaz.ModuleUser.Handlers;

public class GetNewUserRequestByIdHandler : IRequestHandler<GetNewUserRequestByIdQuery, NewUserRequestGetDto>
{
    private readonly DbContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly FlowService<NewUserRequest> _flowService;

    public GetNewUserRequestByIdHandler(
        DbContext db,
        CurrentUserService currentUserService,
        FlowService<NewUserRequest> flowService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _flowService = flowService;
    }

    public Task<NewUserRequestGetDto> Handle(GetNewUserRequestByIdQuery query, CancellationToken cancellationToken)
    {
        var item = _db.NewUserRequests
            .AsExpandable()
            .Where(x => x.Id == query.Id)
            .Select(NewUserRequestGetDto.Mapper(
                HelperFunctions.GetLanguageCode(),
                _currentUserService.GetId(),
                _flowService.GenerateActionAvailability()))
            .FirstOrDefault();

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}