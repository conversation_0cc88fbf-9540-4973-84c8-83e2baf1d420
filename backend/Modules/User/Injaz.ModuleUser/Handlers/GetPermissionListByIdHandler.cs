using Injaz.Core.Services;
using Injaz.ModuleUser.Core.Queries;
using MediatR;

namespace Injaz.ModuleUser.Handlers;

public class GetPermissionListByIdHandler : IRequestHandler<GetUserPermissionListByIdQuery, string[]>
{
    private readonly PermissionService _permissionService;

    public GetPermissionListByIdHandler(PermissionService permissionService)
    {
        _permissionService = permissionService;
    }

    public Task<string[]> Handle(GetUserPermissionListByIdQuery request, CancellationToken cancellationToken)
    {
        return _permissionService.GetPermissionIdList(request.Id);
    }
}