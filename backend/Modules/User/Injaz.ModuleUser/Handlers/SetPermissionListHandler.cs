using Injaz.Core;
using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModuleUser.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleUser.Handlers;

public class SetPermissionListHandler : IRequestHandler<SetUserPermissionListCommand>
{
    private readonly AdminUserService _adminUserService;
    private readonly PermissionService _permissionService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public SetPermissionListHandler(
        AdminUserService adminUserService,
        PermissionService permissionService,
        IStringLocalizer<SharedResource> localizer)
    {
        _adminUserService = adminUserService;
        _permissionService = permissionService;
        _localizer = localizer;
    }

    public async Task<Unit> Handle(SetUserPermissionListCommand request, CancellationToken cancellationToken)
    {
        // Ensure that the user is not admin.
        if (request.Id == _adminUserService.GetId())
        {
            throw new GenericException()
                { Messages = new string[] { _localizer["cannot_set_permissions_for_main_admin_user"] } };
        }

        // Validate permission combination.
        if (!AppHelperFunctions.ValidatePermissionIdList(request.PermissionIdList, out var error))
        {
            throw new GenericException()
            {
                Messages = new string[]
                {
                    _localizer[error]
                }
            };
        }

        // Save.            
        await _permissionService.SetPermissionIdList(request.Id, request.PermissionIdList);
        return default;
    }
}