using System.Reflection;
using Injaz.Core;
using Injaz.Core.Flow.Extensions;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.ModuleUser;

public static class UserModule
{
    public static void Setup(IServiceCollection services, string connectionString)
    {
        services.AddMediatR(typeof(UserModule));

        services.AddFlowServices(Assembly.GetAssembly(typeof(UserModule)));

        // Add DbContext.
        var migrationsAssembly = typeof(HelperFunctions).GetTypeInfo().Assembly.GetName().Name;
        services
            .AddDbContext<DbContext>(options =>
                options.UseSqlServer(
                    connectionString,
                    b => b.MigrationsAssembly(migrationsAssembly))
            );
    }
}